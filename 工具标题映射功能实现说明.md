# 工具标题映射功能实现说明

## 功能概述

实现了一个工具标题映射系统，使得在PluginFilter的OnFunctionInvocationAsync方法中能够显示正确的工具标题，而不是默认的`{context.Function.PluginName}.{context.Function.Name}`格式。

## 实现原理

### 1. 映射Dictionary的创建和管理

在`AgentSkillDomainService.ChatCompletionStream`方法中：
- 创建一个线程安全的`ConcurrentDictionary<string, string>`来存储工具标题映射
- 将映射存储到HttpContext.Items中，供PluginFilter使用
- 确保每次ChatCompletionStream调用时都重新创建映射，保证生命周期正常

```csharp
// 创建工具标题映射Dictionary，确保线程安全
var toolTitleMapping = new ConcurrentDictionary<string, string>();

// 将映射存储到HttpContext中，供PluginFilter使用
_httpContextAccessor.HttpContext.Items["ToolTitleMapping"] = toolTitleMapping;
```

### 2. 不同类型工具的标题映射

#### KnowledgeToolsImporter
- 使用`knowledge.Name`作为显示标题
- 映射格式：`"KnowledgeTools.{functionName}" -> "{knowledge.Name}"`

```csharp
// 添加工具标题映射，使用knowledge.Name作为显示标题
if (toolTitleMapping != null)
{
    string toolKey = $"KnowledgeTools.{truncatedName}";
    toolTitleMapping.TryAdd(toolKey, knowledge.Name);
}
```

#### McpToolsImporter
- 使用`gpt_mcpservice`表中的`ServiceName` + `gpt_mcpservicetool`表中的`ToolTitle`作为显示标题
- 如果ServiceName为空，则使用ServiceCode作为备选；如果ToolTitle为空，则使用ToolName作为备选
- 映射格式：`"{serviceCode}.{toolName}" -> "{ServiceName}{ToolTitle}"`（直接连接，无分隔符）

```csharp
// 添加工具标题映射，使用服务名称+工具标题作为显示标题
foreach (var dbTool in dbTools)
{
    string toolKey = $"{mcpService.ServiceCode}.{dbTool.ToolName}";
    string serviceName = !string.IsNullOrEmpty(mcpService.ServiceName) ? mcpService.ServiceName : mcpService.ServiceCode;
    string toolTitle = !string.IsNullOrEmpty(dbTool.ToolTitle) ? dbTool.ToolTitle : dbTool.ToolName;
    string displayTitle = $"{serviceName}{toolTitle}";
    toolTitleMapping.TryAdd(toolKey, displayTitle);
}
```

#### DataPlatformToolsImporter
- 使用工具的`title`字段作为显示标题
- 如果title为空，则使用name作为备选
- 映射格式：`"GetDataTools.{functionName}" -> "{tool.title}"`

```csharp
// 添加工具标题映射，使用tool.title作为显示标题
if (toolTitleMapping != null)
{
    string toolKey = $"GetDataTools.{truncatedName}";
    string displayTitle = !string.IsNullOrEmpty(title) ? title : name;
    toolTitleMapping.TryAdd(toolKey, displayTitle);
}
```

#### ApiToolsImporter
- 使用`gpt_plugin`表中的`PluginCode`字段作为显示标题
- 映射格式：`"{pluginCode}.{functionName}" -> "{PluginCode}"`

```csharp
// 添加工具标题映射，使用pluginEntity.PluginCode作为显示标题
if (toolTitleMapping != null)
{
    foreach (var function in plugin)
    {
        string toolKey = $"{pluginEntity.PluginCode}.{function.Name}";
        string displayTitle = pluginEntity.PluginCode;
        toolTitleMapping.TryAdd(toolKey, displayTitle);
    }
}
```

#### DataKnowledgeImporter
- 使用`section.ParagraphTitle`字段作为显示标题
- 如果ParagraphTitle为空，则使用id作为备选
- 映射格式：`"DataKnowledgeTools.{functionName}" -> "{section.ParagraphTitle}"`
- 已移除内部的ToolEvent调用，统一由PluginFilter处理

```csharp
// 添加工具标题映射，使用section.ParagraphTitle作为显示标题
if (toolTitleMapping != null && sectionTitleMapping != null && sectionTitleMapping.ContainsKey(id))
{
    string toolKey = $"DataKnowledgeTools.{truncatedName}";
    string displayTitle = sectionTitleMapping[id];
    toolTitleMapping.TryAdd(toolKey, displayTitle);
}
```

### 3. PluginFilter中的标题获取

在`PluginFilter.OnFunctionInvocationAsync`方法中：
- 首先生成默认标题：`{context.Function.PluginName}.{context.Function.Name}`
- 尝试从HttpContext中获取工具标题映射
- 如果找到映射，使用映射的标题；否则使用默认标题

```csharp
string defaultTitle = $"{context.Function.PluginName}.{context.Function.Name}";
string title = defaultTitle;

// 尝试从HttpContext中获取工具标题映射
var httpContext = _httpContextAccessor.HttpContext;
if (httpContext?.Items.TryGetValue("ToolTitleMapping", out var mappingObj) == true)
{
    if (mappingObj is ConcurrentDictionary<string, string> toolTitleMapping)
    {
        // 尝试获取映射的标题
        if (toolTitleMapping.TryGetValue(defaultTitle, out var mappedTitle))
        {
            title = mappedTitle;
        }
    }
}
```

## 修改的文件

### 1. AgentSkillDomainService.cs
- 添加了ConcurrentDictionary的using语句
- 在ChatCompletionStream方法中创建工具标题映射
- 修改CreateKernelWithTool方法签名，传递映射Dictionary
- 修改AddKnowLedgePlugin和AddMcpToolsWithCustomService方法调用

### 2. KnowledgeToolsImporter.cs
- 添加了ConcurrentDictionary的using语句
- 修改ImportKnowledgeTools方法签名，接受映射Dictionary参数
- 在创建知识库工具时添加标题映射逻辑

### 3. McpToolsImporter.cs
- 添加了ConcurrentDictionary的using语句
- 修改ImportMcpTools、ProcessServiceTools、LoadSpecificTools、LoadAllTools方法签名
- 在加载MCP工具成功后添加标题映射逻辑
- 支持从数据库查询工具标题信息

### 4. DataPlatformToolsImporter.cs
- 添加了ConcurrentDictionary的using语句
- 修改ImportGetDataTools方法签名，接受映射Dictionary参数
- 在创建数据平台工具时添加标题映射逻辑
- 目前该功能是可选的，可根据需要启用

### 5. ApiToolsImporter.cs
- 添加了ConcurrentDictionary的using语句
- 修改ImportApiTools方法签名，接受映射Dictionary参数
- 在创建API插件成功后添加标题映射逻辑
- 使用PluginCode作为显示标题

### 6. DataKnowledgeImporter.cs
- 添加了ConcurrentDictionary的using语句
- 修改QueryAllVectorDatabaseDataAsync、ProcessMetadataAndInjectToolsAsync等方法签名
- 在生成工具时收集section.ParagraphTitle信息并添加标题映射
- 移除了内部的ToolEvent调用，统一由PluginFilter处理

### 7. PluginFilter.cs
- 添加了ConcurrentDictionary的using语句
- 修改OnFunctionInvocationAsync方法，使用映射Dictionary获取正确的工具标题

## 线程安全性

- 使用`ConcurrentDictionary<string, string>`确保线程安全
- 每次ChatCompletionStream调用时重新创建映射，避免并发问题
- 通过HttpContext.Items传递映射，确保请求级别的隔离

## 向后兼容性

- 如果映射中没有找到对应的工具，会使用默认的标题格式
- 不影响现有的工具调用逻辑
- 对于没有配置映射的工具类型，保持原有行为

## 使用效果

### 知识库工具
- 原来显示：`KnowledgeTools.QueryXXXKnowledge`
- 现在显示：`{knowledge.Name}`（如：企业政策知识库）

### MCP工具
- 原来显示：`{serviceCode}.{toolName}`
- 现在显示：`{ServiceName}{ToolTitle}`（如：性能查询服务CPU使用率查询）

### 数据平台工具
- 原来显示：`GetDataTools.{functionName}`
- 现在显示：`{tool.title}`（如：销售数据查询工具）

### API插件工具
- 原来显示：`{pluginCode}.{functionName}`
- 现在显示：`{PluginCode}`（如：WeatherAPI）

### 数据知识库工具
- 原来显示：`DataKnowledgeTools.{functionName}`
- 现在显示：`{section.ParagraphTitle}`（如：销售报表分析）

## 测试建议

1. 测试知识库工具调用时的标题显示
2. 测试MCP工具调用时的标题显示
3. 测试没有映射的工具是否使用默认标题
4. 测试多个并发请求的标题映射隔离
5. 验证工具功能本身不受影响

## 注意事项

1. 确保数据库中的`gpt_mcpservice.ServiceName`和`gpt_mcpservicetool.ToolTitle`字段有正确的值
2. 知识库的Name字段应该有意义的显示名称
3. 映射Dictionary的生命周期与ChatCompletionStream请求一致
4. 工具标题映射不影响工具的实际执行逻辑
5. MCP工具标题使用"+"号连接服务名称和工具标题，确保显示格式的一致性
