# 工具标题映射测试示例

## 测试场景

### 1. 知识库工具测试

**测试数据准备：**
- 知识库名称：`企业政策知识库`
- 知识库代码：`enterprise_policy`
- 生成的工具名：`QueryEnterprisePolicyKnowledge`

**预期结果：**
- 原来显示：`KnowledgeTools.QueryEnterprisePolicyKnowledge`
- 现在显示：`企业政策知识库`

**测试步骤：**
1. 配置Agent包含该知识库
2. 调用ChatCompletionStream
3. 触发知识库工具调用
4. 检查PluginFilter中的ToolEvent标题

### 2. MCP工具测试

**测试数据准备：**
- 服务名称：`性能查询服务`
- 服务代码：`performance_query_service`
- 工具名称：`query_performance_data`

**预期结果：**
- 原来显示：`performance_query_service.query_performance_data`
- 现在显示：`性能查询服务`

**测试步骤：**
1. 配置Agent包含该MCP服务
2. 调用ChatCompletionStream
3. 触发MCP工具调用
4. 检查PluginFilter中的ToolEvent标题

### 3. 数据平台工具测试

**测试数据准备：**
- 工具标题：`销售数据查询工具`
- 工具名称：`query_sales_data`
- 生成的函数名：`QuerySalesData`

**预期结果：**
- 原来显示：`GetDataTools.QuerySalesData`
- 现在显示：`销售数据查询工具`

**测试步骤：**
1. 启用DataPlatformToolsImporter
2. 配置数据平台工具
3. 调用ChatCompletionStream
4. 触发数据平台工具调用
5. 检查PluginFilter中的ToolEvent标题

### 4. API插件工具测试

**测试数据准备：**
- 插件代码：`WeatherAPI`
- 函数名称：`GetCurrentWeather`

**预期结果：**
- 原来显示：`WeatherAPI.GetCurrentWeather`
- 现在显示：`WeatherAPI`

**测试步骤：**
1. 配置Agent包含该API插件
2. 调用ChatCompletionStream
3. 触发API插件工具调用
4. 检查PluginFilter中的ToolEvent标题

### 5. 并发测试

**测试场景：**
- 同时发起多个ChatCompletionStream请求
- 每个请求配置不同的工具

**预期结果：**
- 每个请求的工具标题映射互不干扰
- 映射Dictionary正确隔离

## 测试代码示例

### 模拟工具调用测试

```csharp
// 模拟PluginFilter中的标题获取逻辑
public string GetToolTitle(string pluginName, string functionName, IHttpContextAccessor httpContextAccessor)
{
    string defaultTitle = $"{pluginName}.{functionName}";
    string title = defaultTitle;

    // 尝试从HttpContext中获取工具标题映射
    var httpContext = httpContextAccessor.HttpContext;
    if (httpContext?.Items.TryGetValue("ToolTitleMapping", out var mappingObj) == true)
    {
        if (mappingObj is ConcurrentDictionary<string, string> toolTitleMapping)
        {
            // 尝试获取映射的标题
            if (toolTitleMapping.TryGetValue(defaultTitle, out var mappedTitle))
            {
                title = mappedTitle;
                Console.WriteLine($"使用映射标题: {defaultTitle} -> {title}");
            }
            else
            {
                Console.WriteLine($"未找到映射标题，使用默认标题: {defaultTitle}");
            }
        }
    }

    return title;
}
```

### 验证映射创建

```csharp
// 验证知识库工具映射
public void TestKnowledgeToolMapping()
{
    var toolTitleMapping = new ConcurrentDictionary<string, string>();
    
    // 模拟知识库工具映射
    string toolKey = "KnowledgeTools.QueryEnterprisePolicyKnowledge";
    string knowledgeName = "企业政策知识库";
    toolTitleMapping.TryAdd(toolKey, knowledgeName);
    
    // 验证映射
    Assert.True(toolTitleMapping.TryGetValue(toolKey, out var mappedTitle));
    Assert.Equal(knowledgeName, mappedTitle);
}

// 验证MCP工具映射
public void TestMcpToolMapping()
{
    var toolTitleMapping = new ConcurrentDictionary<string, string>();

    // 模拟MCP工具映射
    string toolKey = "performance_query_service.query_performance_data";
    string serviceName = "性能查询服务";
    toolTitleMapping.TryAdd(toolKey, serviceName);

    // 验证映射
    Assert.True(toolTitleMapping.TryGetValue(toolKey, out var mappedTitle));
    Assert.Equal(serviceName, mappedTitle);
}

// 验证数据平台工具映射
public void TestDataPlatformToolMapping()
{
    var toolTitleMapping = new ConcurrentDictionary<string, string>();

    // 模拟数据平台工具映射
    string toolKey = "GetDataTools.QuerySalesData";
    string toolTitle = "销售数据查询工具";
    toolTitleMapping.TryAdd(toolKey, toolTitle);

    // 验证映射
    Assert.True(toolTitleMapping.TryGetValue(toolKey, out var mappedTitle));
    Assert.Equal(toolTitle, mappedTitle);
}

// 验证API插件工具映射
public void TestApiToolMapping()
{
    var toolTitleMapping = new ConcurrentDictionary<string, string>();

    // 模拟API插件工具映射
    string toolKey = "WeatherAPI.GetCurrentWeather";
    string pluginCode = "WeatherAPI";
    toolTitleMapping.TryAdd(toolKey, pluginCode);

    // 验证映射
    Assert.True(toolTitleMapping.TryGetValue(toolKey, out var mappedTitle));
    Assert.Equal(pluginCode, mappedTitle);
}
```

## 日志验证

### 预期日志输出

**知识库工具映射日志：**
```
[ImportKnowledgeTools] 添加工具标题映射: KnowledgeTools.QueryEnterprisePolicyKnowledge -> 企业政策知识库
```

**MCP工具映射日志：**
```
[LoadSpecificTools] 添加MCP工具标题映射: performance_query_service.query_performance_data -> 性能查询服务
```

**数据平台工具映射日志：**
```
[ImportGetDataTools] 添加数据平台工具标题映射: GetDataTools.QuerySalesData -> 销售数据查询工具
```

**API插件工具映射日志：**
```
[ImportApiTools] 添加API工具标题映射: WeatherAPI.GetCurrentWeather -> WeatherAPI
```

**PluginFilter使用映射日志：**
```
[OnFunctionInvocationAsync] 使用映射标题: KnowledgeTools.QueryEnterprisePolicyKnowledge -> 企业政策知识库
[OnFunctionInvocationAsync] 发送ToolEvent: title=企业政策知识库, name=QueryEnterprisePolicyKnowledge
```

## 故障排查

### 常见问题

1. **映射未生效**
   - 检查HttpContext.Items中是否存在"ToolTitleMapping"
   - 确认工具键名格式是否正确
   - 验证映射Dictionary是否为空

2. **标题显示不正确**
   - 检查数据库中ServiceName/知识库Name字段值
   - 确认映射逻辑中的备选值处理

3. **并发问题**
   - 验证每个请求都创建了独立的映射Dictionary
   - 检查HttpContext隔离是否正常

### 调试代码

```csharp
// 在PluginFilter中添加调试日志
public async Task OnFunctionInvocationAsync(FunctionInvocationContext context, Func<FunctionInvocationContext, Task> next)
{
    string defaultTitle = $"{context.Function.PluginName}.{context.Function.Name}";
    
    // 调试：检查HttpContext.Items
    var httpContext = _httpContextAccessor.HttpContext;
    Console.WriteLine($"[DEBUG] HttpContext存在: {httpContext != null}");
    
    if (httpContext?.Items != null)
    {
        Console.WriteLine($"[DEBUG] HttpContext.Items数量: {httpContext.Items.Count}");
        Console.WriteLine($"[DEBUG] 包含ToolTitleMapping: {httpContext.Items.ContainsKey("ToolTitleMapping")}");
        
        if (httpContext.Items.TryGetValue("ToolTitleMapping", out var mappingObj))
        {
            if (mappingObj is ConcurrentDictionary<string, string> toolTitleMapping)
            {
                Console.WriteLine($"[DEBUG] 映射Dictionary大小: {toolTitleMapping.Count}");
                Console.WriteLine($"[DEBUG] 查找键: {defaultTitle}");
                Console.WriteLine($"[DEBUG] 映射存在: {toolTitleMapping.ContainsKey(defaultTitle)}");
            }
        }
    }
    
    // ... 其余逻辑
}
```

## 性能考虑

1. **内存使用**：每个请求创建独立的映射Dictionary，请求结束后自动回收
2. **查找效率**：ConcurrentDictionary提供O(1)的查找性能
3. **线程安全**：使用ConcurrentDictionary确保并发访问安全

## 兼容性验证

1. **向后兼容**：未配置映射的工具仍使用原有标题格式
2. **功能完整性**：工具调用功能本身不受影响
3. **错误处理**：映射获取失败时优雅降级到默认标题
