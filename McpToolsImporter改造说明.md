# McpToolsImporter 改造说明

## 改造概述

根据需求，我们对 `AddMcpToolsWithCustomService` 进行了重构，将其逻辑抽取到新的 `McpToolsImporter` 类中，并实现了基于 `agent.mcps` 配置的智能工具过滤功能。

## 改造内容

### 1. 新建文件

**文件路径**: `src/Mysoft.GPTEngine.Domain/AgentSkillEnhancement/McpToolsImporter.cs`

这个新的导入器类负责：
- 解析 `agent.mcps` 配置
- 根据配置决定是加载全部工具还是指定工具
- 处理服务配置和超时设置
- 设置自定义请求头

### 2. 修改文件

**文件路径**: `src/Mysoft.GPTEngine.Domain/AgentSkillDomainService.cs`

- 简化了 `AddMcpToolsWithCustomService` 方法
- 将复杂的逻辑委托给新的 `McpToolsImporter`

**文件路径**: `src/Mysoft.GPTEngine.Domain/McpCustomService.cs`

- 新增了 `LoadSpecificMcpToolsToKernelAsync` 方法
- 支持加载指定的工具列表

## 核心功能

### 工具过滤逻辑

根据 `agent.mcps` 中的数据结构：

```json
"mcps": [
    {
        "serviceCode": "performance_query_service",
        "serviceGUID": "3a1adbff-21db-ff0d-fce7-6a295ae2c089",
        "toolGUID": null,
        "toolName": null
    }
]
```

**过滤规则**：
1. **如果有 `serviceCode`，没有 `toolGUID`** → 注入全部工具
2. **如果有具体的 `toolGUID`** → 只注入 `toolGUID` 对应的工具

### 数据库集成

- **McpServiceRepository**: 获取服务配置信息
- **McpServiceToolRepository**: 获取具体工具信息
- **超时设置**: 从 `gpt_mcpservice` 表的 `TimeoutSeconds` 字段获取

### 超时配置

从数据库的 `gpt_mcpservice.TimeoutSeconds` 字段读取超时配置，默认为30秒。通过调用 `McpCustomService.SetTimeout(timeoutSeconds)` 方法动态设置每个服务的超时时间。

## 类结构

### McpToolsImporter

```csharp
public class McpToolsImporter
{
    // 构造函数
    public McpToolsImporter(Kernel kernel, IServiceProvider serviceProvider)
    
    // 主要方法
    public async Task ImportMcpTools(List<AgentMcpDto> mcpConfigs)
    
    // 私有方法
    private async Task ProcessServiceTools(string serviceGuid, List<AgentMcpDto> toolConfigs)
    private async Task LoadSpecificTools(McpServiceEntity mcpService, List<AgentMcpDto> toolConfigs)
    private async Task LoadAllTools(McpServiceEntity mcpService)
    private async Task SetCustomHeaders(McpServiceEntity mcpService)
    private async Task SetTimeoutConfiguration(McpServiceEntity mcpService)
    private Dictionary<string, string> ParseCustomHeaders(string customHeadersJson)
}
```

### McpCustomService 新增方法

```csharp
// 加载指定工具的方法
public async Task<bool> LoadSpecificMcpToolsToKernelAsync(
    Kernel kernel,
    string mcpServerUrl,
    string pluginName,
    List<string> specificToolNames,
    CancellationToken cancellationToken = default)

// 动态设置超时的方法
public void SetTimeout(int timeoutSeconds)

// 设置详细超时配置的方法
public void SetTimeouts(TimeSpan connectTimeout, TimeSpan readTimeout, TimeSpan writeTimeout, TimeSpan keepAliveInterval)
```

## 使用方式

### 原来的调用方式
```csharp
// 在 AgentSkillDomainService 中
await AddMcpToolsWithCustomService(kernel);
```

### 现在的调用方式
```csharp
// 在 AgentSkillDomainService 中
await new McpToolsImporter(kernel, _serviceProvider).ImportMcpTools(agent.Mcps);
```

## 优势

1. **代码分离**: 将MCP工具导入逻辑从 `AgentSkillDomainService` 中分离出来
2. **职责单一**: `McpToolsImporter` 专门负责MCP工具的导入
3. **灵活过滤**: 支持全部工具和指定工具两种加载模式
4. **配置驱动**: 基于数据库配置进行工具过滤和超时设置
5. **易于维护**: 清晰的类结构和方法分工

## 兼容性

- ✅ 保持了原有的API接口不变
- ✅ 支持现有的配置格式
- ✅ 向后兼容现有的MCP服务配置
- ✅ 保持了相同的错误处理和日志记录

## 测试建议

1. **全部工具加载测试**: 配置 `toolGUID` 为 `null`，验证是否加载所有工具
2. **指定工具加载测试**: 配置具体的 `toolGUID`，验证是否只加载指定工具
3. **超时配置测试**: 验证从数据库读取的超时配置是否生效
4. **自定义请求头测试**: 验证自定义请求头是否正确设置
5. **错误处理测试**: 验证各种异常情况的处理

## 超时配置工作流程

1. **读取配置**: 从 `gpt_mcpservice.TimeoutSeconds` 字段读取超时配置
2. **动态设置**: 调用 `McpCustomService.SetTimeout(timeoutSeconds)` 设置超时
3. **应用配置**: 在 `ConnectAsync` 时应用超时配置到 HttpClient
4. **生效范围**: 超时配置对该服务的所有MCP工具调用生效

### 示例流程

```csharp
// 1. 从数据库获取服务配置
var mcpService = await _mcpServiceRepository.GetAsync(s => s.ServiceGUID == serviceGuid);

// 2. 设置超时配置（从数据库TimeoutSeconds字段）
var timeoutSeconds = mcpService.TimeoutSeconds > 0 ? mcpService.TimeoutSeconds : 30;
_mcpCustomService.SetTimeout(timeoutSeconds);

// 3. 连接到MCP服务器（应用超时配置）
var connected = await _mcpCustomService.ConnectAsync(mcpService.ServiceURL);

// 4. 加载工具（使用设置的超时配置）
var toolsLoaded = await _mcpCustomService.LoadMcpToolsToKernelAsync(kernel, mcpService.ServiceURL, mcpService.ServiceCode);
```

## 注意事项

1. 确保数据库中的 `gpt_mcpservice` 和 `gpt_mcpservicetool` 表数据完整
2. `TimeoutSeconds` 字段应该设置合理的值（建议30-120秒）
3. 工具名称长度应该控制在合理范围内，避免被截断
4. 自定义请求头应该是有效的JSON格式
5. 超时配置在每次处理新服务时都会重新设置，确保每个服务使用正确的超时值

## 编译状态

✅ 编译成功，无错误
⚠️ 有一些警告，但不影响功能使用
