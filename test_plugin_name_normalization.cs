using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace TestPluginNameNormalization
{
    class Program
    {
        static void Main(string[] args)
        {
            // 测试插件名称规范化功能
            TestNormalizePluginName();
        }

        static void TestNormalizePluginName()
        {
            Console.WriteLine("测试插件名称规范化功能:");
            Console.WriteLine("================================");

            // 测试用例
            var testCases = new[]
            {
                "amap-map",           // 原始错误案例
                "test.plugin",        // 包含点号
                "my-plugin-name",     // 包含多个连字符
                "plugin_name",        // 已经符合规范
                "123plugin",          // 数字开头
                "plugin@#$%",         // 包含特殊字符
                "",                   // 空字符串
                "a",                  // 单字符
                "plugin--name",       // 连续连字符
                "plugin__name",       // 连续下划线
                "_plugin_",           // 开头结尾下划线
                "中文插件",            // 中文字符
                "plugin-中文-name"     // 混合字符
            };

            foreach (var testCase in testCases)
            {
                var normalized = NormalizePluginName(testCase);
                Console.WriteLine($"'{testCase}' -> '{normalized}'");
            }
        }

        /// <summary>
        /// 规范化插件名称，确保符合Semantic Kernel的命名要求
        /// 插件名称只能包含ASCII字母、数字和下划线
        /// </summary>
        /// <param name="pluginName">原始插件名称</param>
        /// <returns>规范化后的插件名称</returns>
        static string NormalizePluginName(string pluginName)
        {
            if (string.IsNullOrEmpty(pluginName))
            {
                return "Mcp";
            }

            var normalized = new StringBuilder();
            bool lastWasUnderscore = false;

            foreach (char c in pluginName)
            {
                if (char.IsLetterOrDigit(c))
                {
                    // ASCII字母和数字直接添加
                    normalized.Append(c);
                    lastWasUnderscore = false;
                }
                else if (c == '_')
                {
                    // 下划线直接添加，但避免连续的下划线
                    if (!lastWasUnderscore)
                    {
                        normalized.Append('_');
                        lastWasUnderscore = true;
                    }
                }
                else
                {
                    // 其他字符（如连字符、点号等）替换为下划线，但避免连续的下划线
                    if (!lastWasUnderscore)
                    {
                        normalized.Append('_');
                        lastWasUnderscore = true;
                    }
                }
            }

            var result = normalized.ToString();

            // 移除开头和结尾的下划线
            result = result.Trim('_');

            // 如果结果为空或只包含下划线，使用默认名称
            if (string.IsNullOrEmpty(result) || result.All(c => c == '_'))
            {
                result = "Mcp";
            }

            // 确保首字符是字母
            if (!char.IsLetter(result[0]))
            {
                result = "Mcp" + result;
            }

            return result;
        }
    }

    // 扩展方法，用于支持 All 方法
    public static class LinqExtensions
    {
        public static bool All(this string source, Func<char, bool> predicate)
        {
            foreach (char c in source)
            {
                if (!predicate(c))
                    return false;
            }
            return true;
        }
    }
}
