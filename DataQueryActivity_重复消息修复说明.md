# DataQueryActivity 重复用户消息修复说明

## 问题描述

在DataQueryActivity执行后，历史消息中出现了重复的用户提问：
- 一条是原始的用户输入：`"现在查询一下深圳公司的销售情况"`
- 另一条是带格式前缀的消息：`"0:现在查询一下深圳公司的销售情况"`

## 问题根因分析

### 重复保存的两个位置：

1. **StartActivity（第85行）**：
   ```csharp
   await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, input));
   ```
   - 保存了带 `0:` 前缀的用户消息到 `ChatMessages`
   - 这是系统标准的用户消息保存方式

2. **DataQueryActivity.SaveConversationMemory（原第567行）**：
   ```csharp
   await _conversationMemoryService.SaveConversationMemoryAsync(
       userGuid, chatGuid, userInput, assistantOutput, tenantCode, tenantName, batchGuid, userName);
   ```
   - 保存了原始的用户消息到会话记忆
   - 导致了重复保存

### 数据流分析：

```
用户输入: "现在查询一下深圳公司的销售情况"
    ↓
StartActivity.PostExecuteActivityAsync()
    ↓ AddMessage(User, "0:现在查询一下深圳公司的销售情况")
ChatMessages: [{"role":"user","content":"0:现在查询一下深圳公司的销售情况"}]
    ↓
DataQueryActivity.SaveConversationMemory()
    ↓ SaveConversationMemoryAsync(userInput="现在查询一下深圳公司的销售情况")
会话记忆: [{"role":"user","content":"现在查询一下深圳公司的销售情况"}]
```

**结果**：两个不同的存储位置保存了同一条用户消息的不同格式版本。

## 修复方案

### 修改 SaveConversationMemory 方法

**修改前（第567-568行）**：
```csharp
// 使用 ConversationMemoryService 的标准方法保存用户消息和助手回复
await _conversationMemoryService.SaveConversationMemoryAsync(
    userGuid, chatGuid, userInput, assistantOutput, tenantCode, tenantName, batchGuid, userName);
```

**修改后（第565-587行）**：
```csharp
// 注意：不保存用户消息，因为StartActivity已经保存了用户消息
// 只保存助手回复到会话记忆，避免重复保存用户消息
var assistantMessages = new List<ChatMessageDto>
{
    new ChatMessageDto
    {
        Role = ChatRoleConstant.Assistant,
        Content = assistantOutput,
        UserGUID = userGuid,
        UserName = userName,
        ChatGUID = Guid.Parse(chatGuid),
        Index = 0, // 索引将在SaveConversationMessagesAsync中重新设置
        BatchGUID = batchGuid,
        TenantCode = tenantCode,
        TenantName = tenantName,
        IsHidden = 0
    }
};

await _conversationMemoryService.SaveConversationMessagesAsync(
    userGuid, chatGuid, assistantMessages, tenantCode, tenantName, batchGuid, userName);
```

### 修改要点：

1. **避免重复保存**：不再调用 `SaveConversationMemoryAsync`，该方法会同时保存用户消息和助手回复
2. **只保存助手回复**：使用 `SaveConversationMessagesAsync` 只保存助手回复
3. **保持工具调用逻辑**：工具调用消息的保存逻辑保持不变
4. **更新注释**：明确说明不保存用户消息的原因

## 修复效果

### 修复前：
```
ChatMessages (AddMessage):
- {"role":"user","content":"0:现在查询一下深圳公司的销售情况"}
- {"role":"assistant","content":"0:根据查询结果..."}

会话记忆 (ConversationMemory):
- {"role":"user","content":"现在查询一下深圳公司的销售情况"}  // 重复！
- {"role":"assistant","content":"根据查询结果..."}
```

### 修复后：
```
ChatMessages (AddMessage):
- {"role":"user","content":"0:现在查询一下深圳公司的销售情况"}
- {"role":"assistant","content":"0:根据查询结果..."}

会话记忆 (ConversationMemory):
- {"role":"assistant","content":"根据查询结果..."}  // 只保存助手回复
```

## 系统设计原则

### 消息保存的职责分工：

1. **StartActivity**：
   - 负责保存用户输入到 `ChatMessages`
   - 使用 `AddMessage` 方法，格式为 `EventDataConstant.TextEvent`

2. **DataQueryActivity**：
   - 负责保存助手回复到 `ChatMessages`（通过 `AddMessage`）
   - 负责保存助手回复到会话记忆（通过 `SaveConversationMemory`）
   - **不应该**重复保存用户消息

3. **ConversationMemoryService**：
   - 负责会话记忆的持久化存储
   - 支持历史消息的加载和缓存

### 避免重复的设计原则：

1. **单一职责**：每个Activity只负责保存自己产生的消息
2. **避免重复**：不同的存储机制不应该保存相同的消息
3. **格式一致**：同一类型的消息应该使用统一的格式

## 验证建议

建议进行以下测试来验证修复效果：

1. **单轮对话测试**：
   - 发送一条用户消息
   - 验证只有一条用户消息被保存
   - 确认助手回复正确保存

2. **多轮对话测试**：
   - 进行多轮对话
   - 验证每轮对话的用户消息都不重复
   - 确认对话历史完整且无重复

3. **工具调用测试**：
   - 测试包含知识库查询的对话
   - 验证工具调用消息正确保存
   - 确认用户消息仍然不重复

## 总结

通过这次修复：
- ✅ 解决了用户消息重复保存的问题
- ✅ 保持了助手回复的正确保存
- ✅ 维护了工具调用消息的完整性
- ✅ 符合系统的设计原则和职责分工

这确保了DataQueryActivity能够正确处理消息保存，避免重复数据，提供清晰的对话历史。
