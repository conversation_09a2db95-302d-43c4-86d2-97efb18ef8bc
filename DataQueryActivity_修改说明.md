# DataQueryActivity 修改说明

## 修改概述

根据您的需求，对 `DataQueryActivity.cs` 进行了以下主要逻辑的完善，并与 `KnowledgeActivity` 进行了对比分析：

### 一、修正输入参数获取逻辑（与KnowledgeActivity保持一致）

**KnowledgeActivity 的逻辑（第98行）：**
```csharp
_input = inputs.FirstOrDefault(x => x.Code == "input")?.LiteralValue ?? _input;
```

**DataQueryActivity 修改后的逻辑（第107行）：**
```csharp
// 解析输入参数 - 与KnowledgeActivity保持一致
await _chatRunDto.InputsArgumentParser(flowNode);
var inputs = flowNode.Config.Inputs;
_input = inputs.FirstOrDefault(x => x.Code == "input")?.LiteralValue ?? _input;

_logger.LogInformation("[DataQueryActivity] 输入参数解析: Code=input, LiteralValue={value}", _input);
```

**修改说明：**
- 与 `KnowledgeActivity` 保持一致，查找 `Code == "input"` 的参数
- 这是标准的输入参数获取方式，确保兼容性
- 增加了详细的日志记录

### 二、完善输出设置逻辑

**原始逻辑（第198行）：**
```csharp
// 设置输出参数
await AddFirstOutput(flowNode, chatOutput);
```

**修改后的逻辑（第200-209行）：**
```csharp
// 将大模型的回答存储到消息记录中（作为助手消息）
if (!string.IsNullOrEmpty(chatOutput))
{
    await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, chatOutput));
    _logger.LogInformation("[DataQueryActivity] 已将输出存储到消息记录: {outputLength} 字符", chatOutput.Length);
}

// 设置输出参数 - 将大模型的回答放到output传递给下一个节点
await AddFirstOutput(flowNode, chatOutput);
_logger.LogInformation("[DataQueryActivity] 已设置输出参数到第一个output: {outputValue}", chatOutput);
```

**修改说明：**
- **新增功能**：将大模型的回答存储到消息记录中（`ChatMessages`）
- 使用 `AddMessage` 方法将输出作为助手消息保存
- 确保大模型的回答正确传递给下一个节点
- 增加了输出设置成功的确认日志

### 三、与KnowledgeActivity的对比分析

#### 相同点：
1. **输入参数获取**：都使用 `inputs.FirstOrDefault(x => x.Code == "input")?.LiteralValue`
2. **输出传递**：都使用 `AddFirstOutput(flowNode, result)` 传递给下一个节点
3. **字段事件推送**：都使用 `FieldEvent` 推送输出参数

#### 差异点：
1. **消息记录存储**：
   - `KnowledgeActivity`：没有将输出存储到消息记录
   - `DataQueryActivity`：**新增**了 `AddMessage` 调用，将输出存储到消息记录

2. **会话记忆处理**：
   - `KnowledgeActivity`：没有特殊的会话记忆处理
   - `DataQueryActivity`：有专门的 `SaveConversationMemory` 方法处理工具调用记忆

### 四、增强日志记录

**新增的日志记录包括：**

1. **输入参数解析日志**：
   - 显示解析到的输入参数值
   - 记录所有输入配置的详情

2. **输出参数设置日志**：
   - 确认输出参数设置成功
   - 显示所有输出参数的值
   - 记录字段事件推送的详细信息

3. **消息记录存储日志**：
   - 确认输出已存储到消息记录
   - 显示存储的内容长度

## 核心改进点

1. **输入获取标准化**：与 `KnowledgeActivity` 保持一致的输入参数获取方式
2. **输出存储完善**：新增了将输出存储到消息记录的功能
3. **输出传递可靠**：确保大模型回答正确传递给下一个节点
4. **日志更详细**：便于调试和分析数据流转过程
5. **错误处理更完善**：增加了各种边界情况的处理

## 测试建议

建议测试以下场景：
1. 正常的输入参数解析和输出传递
2. 输入参数为空的情况
3. 输出存储到消息记录的完整流程
4. 输出参数传递给下一个节点的完整流程
5. 验证消息记录中是否正确保存了助手回答

通过增强的日志记录，可以清楚地看到每个步骤的执行情况和数据流转过程。
