# AgentSkillDomainService 对话历史修复说明

## 问题描述

在修复了重复消息问题后，`AgentSkillDomainService` 出现了和 `DataQueryActivity` 一样的问题：
- 给大模型的请求中丢失了用户输入的历史
- 只剩下最后一次用户的输入
- 无法进行连续的多轮对话

## 问题根因分析

### 问题产生的原因：

1. **移除了用户消息保存**：
   - 为了解决重复消息问题，我们移除了 `ExtractNewMessagesFromChatHistory` 中的用户消息保存
   - 这导致会话记忆中缺少用户消息

2. **会话记忆加载机制**：
   - `LoadConversationMemoryAsync` 从数据库加载历史对话
   - 如果数据库中没有用户消息，就无法构建完整的对话历史
   - 大模型只能看到当前的用户输入，无法进行连续对话

3. **数据流断裂**：
   ```
   用户输入 -> StartActivity -> AddMessage(User) -> ChatMessages (界面显示)
            -> AgentSkillDomainService -> ClearChatMessages() (清空界面消息)
            -> LoadConversationMemoryAsync -> 缺少历史用户消息 ❌
            -> 大模型只看到当前输入，无法连续对话 ❌
   ```

## 解决方案

### 核心思路：
**分离界面显示和历史存储的职责**
- **界面显示**：通过 `ChatMessages` 处理，避免重复
- **历史存储**：通过会话记忆处理，确保连续性

### 具体修复：

#### 1. 恢复用户消息到会话记忆（设置为隐藏）

**修改 ExtractNewMessagesFromChatHistory 方法**（第692-700行）：

```csharp
// 添加用户消息到会话记忆（设置为隐藏，避免界面重复显示）
// 这是为了确保历史对话的连续性，让大模型能看到完整的对话历史
newMessages.Add(new ChatMessageDto
{
    Role = ChatRoleConstant.User,
    Content = userInput,
    Index = 0, // 临时索引，将在保存时重新设置
    BatchGUID = batchGuid,
    UserName = userName, // 设置用户名
    IsHidden = 1 // 设置为隐藏，避免在界面重复显示，但保存到会话记忆用于历史加载
});
```

#### 2. 关键设计：IsHidden = 1

- **IsHidden = 1**：消息保存到数据库，但不在界面显示
- **IsHidden = 0**：消息保存到数据库，并在界面显示

这样可以：
- ✅ 保存用户消息到会话记忆（用于历史对话）
- ✅ 避免在界面重复显示（通过IsHidden=1）
- ✅ 确保大模型能看到完整的对话历史

## 修复效果

### 修复前：
```
数据库会话记忆：
- {"role":"assistant","content":"这是我的回复1"}
- {"role":"assistant","content":"这是我的回复2"}
- 缺少用户消息 ❌

大模型看到的历史：
- 只有助手回复，没有用户消息
- 无法理解对话上下文 ❌

界面显示：
- 通过StartActivity的ChatMessages显示用户消息 ✅
- 通过AddMessage显示助手回复 ✅
```

### 修复后：
```
数据库会话记忆：
- {"role":"user","content":"用户问题1","isHidden":1} (隐藏，不在界面显示)
- {"role":"assistant","content":"这是我的回复1","isHidden":0}
- {"role":"user","content":"用户问题2","isHidden":1} (隐藏，不在界面显示)
- {"role":"assistant","content":"这是我的回复2","isHidden":0}

大模型看到的历史：
- 完整的用户-助手对话序列 ✅
- 能够理解对话上下文，进行连续对话 ✅

界面显示：
- 通过StartActivity的ChatMessages显示用户消息 ✅
- 通过AddMessage显示助手回复 ✅
- 隐藏的用户消息不会重复显示 ✅
```

## 设计架构

### 消息处理的双轨制：

1. **界面显示轨道**：
   - `StartActivity` -> `AddMessage(User)` -> `ChatMessages` (界面显示)
   - `AgentSkillDomainService` -> `AddMessage(Assistant)` -> `ChatMessages` (界面显示)

2. **历史存储轨道**：
   - `ExtractNewMessagesFromChatHistory` -> 用户消息 (IsHidden=1) -> 会话记忆 (历史加载)
   - `ExtractNewMessagesFromChatHistory` -> 助手回复 (IsHidden=0) -> 会话记忆 (历史加载)

### 职责分工：

- **StartActivity**：负责当前用户消息的界面显示
- **AgentSkillDomainService**：负责当前助手回复的界面显示
- **ExtractNewMessagesFromChatHistory**：负责所有消息的历史存储
- **ConversationMemoryService**：负责历史对话的加载和管理

## 与 DataQueryActivity 的一致性

现在 `AgentSkillDomainService` 的处理方式与 `DataQueryActivity` 保持一致：
- 都使用 `IsHidden` 机制分离界面显示和历史存储
- 都确保大模型能看到完整的对话历史
- 都避免了界面上的重复消息显示

## 验证建议

1. **多轮对话测试**：
   - 进行多轮连续对话
   - 验证大模型能记住之前的对话内容
   - 确认每轮对话都有上下文连续性

2. **界面显示测试**：
   - 验证界面上没有重复的用户消息
   - 确认助手回复正确显示
   - 检查对话历史的完整性

3. **工具调用测试**：
   - 测试包含工具调用的多轮对话
   - 验证工具调用不影响对话连续性
   - 确认复杂对话场景的正确处理

## 总结

通过这次修复：
- ✅ 解决了对话历史丢失的问题
- ✅ 保持了界面显示的清洁（无重复消息）
- ✅ 确保了大模型的连续对话能力
- ✅ 建立了清晰的双轨制消息处理架构
- ✅ 与DataQueryActivity保持了一致的设计模式

现在 `AgentSkillDomainService` 既能避免重复消息，又能保持完整的对话历史，提供良好的用户体验。
