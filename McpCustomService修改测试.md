# McpCustomService HttpClient 修改测试

## 问题描述

原始错误：
```
[McpCustomService] 连接MCP服务器失败: https://mcp.amap.com/mcp?key=438e45d40578f0e3c843940fc7a41bfd 
System.InvalidOperationException: This instance has already started one or more requests. Properties can only be modified before sending the first request.
   at System.Net.Http.HttpClient.CheckDisposedOrStarted()
   at System.Net.Http.HttpClient.set_Timeout(TimeSpan value)
   at Mysoft.GPTEngine.Domain.McpCustomService.ConnectAsync(String mcpServerUrl, CancellationToken cancellationToken) in D:\workspace\gptengine\src\Mysoft.GPTEngine.Domain\McpCustomService.cs:line 312
```

## 解决方案

### 主要修改

1. **移除单一HttpClient字段**：将 `_httpClient` 字段改为 `IHttpClientFactory _httpClientFactory`
2. **动态创建HttpClient**：在每次连接时创建新的HttpClient实例
3. **添加HttpClient配置方法**：创建 `CreateConfiguredHttpClient()` 方法
4. **更新请求头应用方法**：修改 `ApplyHeadersToHttpClient(HttpClient httpClient)` 方法接受HttpClient参数
5. **简化其他方法**：移除在 `GetAllToolsAsync` 和 `ExecuteToolAsync` 中动态设置请求头的逻辑

### 具体修改内容

#### 1. 字段修改
```csharp
// 原来
private readonly HttpClient _httpClient;

// 修改后
private readonly IHttpClientFactory _httpClientFactory;
```

#### 2. 构造函数修改
```csharp
// 原来
public McpCustomService(..., HttpClient httpClient, ...)

// 修改后  
public McpCustomService(..., IHttpClientFactory httpClientFactory, ...)
```

#### 3. 新增HttpClient创建方法
```csharp
private HttpClient CreateConfiguredHttpClient()
{
    var httpClient = _httpClientFactory.CreateClient();
    httpClient.Timeout = _readTimeout;
    _logger.LogDebug("[McpCustomService] 创建新的HttpClient实例，超时: {timeout}", _readTimeout);
    return httpClient;
}
```

#### 4. 修改ConnectAsync方法
```csharp
// 原来
_httpClient.Timeout = _readTimeout;
ApplyHeadersToHttpClient();
_mcpClient = await McpClientFactory.CreateAsync(
    new SseClientTransport(transportOptions, _httpClient, _loggerFactory)
);

// 修改后
var httpClient = CreateConfiguredHttpClient();
ApplyHeadersToHttpClient(httpClient);
_mcpClient = await McpClientFactory.CreateAsync(
    new SseClientTransport(transportOptions, httpClient, _loggerFactory)
);
```

#### 5. 更新ApplyHeadersToHttpClient方法
```csharp
// 原来
private void ApplyHeadersToHttpClient()
{
    // 使用 _httpClient
}

// 修改后
private void ApplyHeadersToHttpClient(HttpClient httpClient)
{
    // 使用传入的 httpClient 参数
}
```

## 优势

1. **解决Timeout问题**：每次连接都使用新的HttpClient实例，避免重复设置Timeout
2. **更好的资源管理**：通过IHttpClientFactory管理HttpClient生命周期
3. **更清晰的架构**：请求头设置集中在连接时进行
4. **避免状态污染**：每次连接都是独立的HttpClient实例

## 注意事项

1. **临时请求头**：在 `ExecuteToolAsync` 中设置临时请求头现在不再支持，需要在 `ConnectAsync` 之前调用 `SetCustomHeaders`
2. **性能考虑**：每次连接都创建新的HttpClient，但通过IHttpClientFactory管理，性能影响可控
3. **向后兼容**：API接口保持不变，只是内部实现改变

## 测试建议

1. 测试正常的MCP连接流程
2. 测试超时配置是否生效
3. 测试自定义请求头是否正确设置
4. 测试多次连接是否都能成功
