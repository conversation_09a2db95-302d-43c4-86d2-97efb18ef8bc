using FastTracker;
using FastTracker.Common;
using FastTracker.Config;
using FastTracker.Diagnostics;
using FastTracker.Tracing;
using FastTracker.Tracing.Extensions;
using FastTracker.Tracing.Segments;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;

namespace Mysoft.GPTEngine.Diagnostics.Extend.DiagnosticProcessor
{
    /// <summary>
    /// Processes HTTP request and response diagnostics for tracing. It manages request contexts and captures relevant
    /// data like URL, method, and status code.
    /// Handles exceptions and collects request body if configured.
    /// </summary>
    /// <seealso href="https://github.com/dotnet/runtime/blob/main/src/libraries/System.Net.Http/src/HttpDiagnosticsGuide.md"/>
    public class HttpClientDiagnosticDiagnosticProcessor : ITracingDiagnosticProcessor
    {
        private readonly ITracingContext _tracingContext;

        private readonly ConcurrentDictionary<HttpRequestMessage, SegmentContext> _processingDiagnostiDic = new ConcurrentDictionary<HttpRequestMessage, SegmentContext>();

        private readonly IServerUrls _serverUrls;

        public string ListenerName { get; } = "HttpHandlerDiagnosticListener";
        public bool AllowSubscribe { get; private set; }

        private readonly ITrackerConfigAccessor _configAccessor;
        public HttpClientDiagnosticDiagnosticProcessor(ITrackerConfigAccessor configAccessor, ITracingContext tracingContext, IServerUrls serverUrls)
        {
            _configAccessor = configAccessor;
            AllowSubscribe = configAccessor.Config.CollectLayer.HTTP.Enable;
            _tracingContext = tracingContext;
            _serverUrls = serverUrls;
        }

        [DiagnosticName("System.Net.Http.HttpRequestOut.Start")]
        public void HttpRequest([Property(Name = "Request")] HttpRequestMessage request)
        {
            if (_serverUrls.IsBaseOf(request.RequestUri)) return;
            string url = request.RequestUri.OriginalString.Split('?')[0];

            if (!_tracingContext.CreateExitSegmentContext(url, $"{request.RequestUri.Host}:{request.RequestUri.Port}", out var context, new HttpClientICarrierHeaderCollectionCore(request))) return;

            if (!_processingDiagnostiDic.TryAdd(request, context)) return;

            context.Span.SpanLayer = SpanLayer.HTTP;
            context.Span.Component = Components.HTTPCLIENT;
            context.Span.AddTag(Tags.URL, url);
            context.Span.AddTag(Tags.HTTP_METHOD, request.Method.ToString());
            if (_configAccessor.Config.CollectLayer.HTTP.CollectQueryString && !string.IsNullOrWhiteSpace(request.RequestUri.Query))
            {
                context.Span.AddTag(Tags.HTTP_QUERY, request.RequestUri.Query);
            }
            if (CollectBody(request, out string body))
            {
                context.Span.AddTag(Tags.HTTP_BODY, body);
            }
        }

        [DiagnosticName("System.Net.Http.HttpRequestOut.Stop")]
        public void HttpResponse([Property(Name = "Response")] HttpResponseMessage response)
        {
            if (response == null) { return; }
            if (!_processingDiagnostiDic.TryRemove(response.RequestMessage, out var context)) { return; }

            var statusCode = (int)response.StatusCode;
            if (statusCode >= 400)
            {
                context.Span.ErrorOccurred();
            }
            context.Span.AddTag(Tags.STATUS_CODE, statusCode);
            _tracingContext.Release(context);
        }

        [DiagnosticName("System.Net.Http.HttpRequestOut.Exception")]
        public void HttpException([Property(Name = "Request")] HttpRequestMessage request, [Property(Name = "Exception")] Exception exception)
        {
            if (request == null) { return; }
            if (!_processingDiagnostiDic.TryRemove(request, out var context))
            {
                return;
            }
            if (context != null)
            {
                context.Span.ErrorOccurred(exception);
                _tracingContext.Release(context);
            }
        }

        private bool CollectBody(HttpRequestMessage request, out string body)
        {
            body = string.Empty;
            if (!_configAccessor.Config.CollectLayer.HTTP.CollectBodyContent
               || request.Content == null
               || request.Content.Headers.ContentLength > _configAccessor.Config.CollectLayer.HTTP.CollectBodyLengthThreshold)
            {
                return false;
            }
            if (!_configAccessor.Config.CollectLayer.HTTP.CollectBodyContentTypes.Any(t => request.Content.Headers.ContentType.MediaType == t))
            {
                return false;
            }
            try
            {
                body = request.Content.ReadAsStringAsync().Result;
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }

    public class HttpClientICarrierHeaderCollectionCore : ICarrierHeaderCollection
    {
        private readonly HttpRequestMessage _request;

        public HttpClientICarrierHeaderCollectionCore(HttpRequestMessage request)
        {
            _request = request;
        }

        public void Add(string key, string value)
        {
            _request.Headers.Add(key, value);
        }

        public IEnumerator<KeyValuePair<string, string>> GetEnumerator()
        {
            throw new NotImplementedException();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}
