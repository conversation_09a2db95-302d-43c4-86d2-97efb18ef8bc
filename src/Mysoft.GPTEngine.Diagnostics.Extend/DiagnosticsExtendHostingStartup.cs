using Microsoft.AspNetCore.Hosting;

[assembly: HostingStartup(typeof(Mysoft.GPTEngine.Diagnostics.Extend.DiagnosticsExtendHostingStartup))]

namespace Mysoft.GPTEngine.Diagnostics.Extend
{
    public class DiagnosticsExtendHostingStartup : IHostingStartup
    {
        public void Configure(IWebHostBuilder builder)
        {
            builder.ConfigureServices(services => services.AddFastTrackerDiagnosticProcessor());
        }
    }
}
