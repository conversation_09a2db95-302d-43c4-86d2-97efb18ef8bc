using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Mysoft.GPTEngine.Application.Extensions
{
    public static class AutoMapperExtension
    {
        public static IServiceCollection AddAutoMapper(this IServiceCollection services, params Assembly[] assemblies)
        {
            return AddAutoMapperClasses(services, null, assemblies);
        }

        private static IServiceCollection AddAutoMapperClasses(IServiceCollection services, Action<IServiceProvider, IMapperConfigurationExpression> configAction, IEnumerable<Assembly> assembliesToScan, ServiceLifetime serviceLifetime = ServiceLifetime.Transient)
        {
            if (configAction != null)
            {
                services.AddOptions<MapperConfigurationExpression>().Configure(delegate (MapperConfigurationExpression options, IServiceProvider sp)
                {
                    configAction(sp, options);
                });
            }

            if (assembliesToScan != null)
            {
                assembliesToScan = new HashSet<Assembly>(assembliesToScan.Where((Assembly a) => !a.IsDynamic && a != typeof(Mapper).Assembly));
                services.Configure(delegate (MapperConfigurationExpression options)
                {
                    options.AddMaps(assembliesToScan);
                });
                Type[] openTypes = new Type[5]
                {
                typeof(IValueResolver<, , >),
                typeof(IMemberValueResolver<, , , >),
                typeof(ITypeConverter<, >),
                typeof(IValueConverter<, >),
                typeof(IMappingAction<, >)
                };
                foreach (Type item in assembliesToScan.SelectMany((Assembly a) => from type in a.GetTypes()
                                                                                  where type.IsClass && !type.IsAbstract && Array.Exists(openTypes, (Type openType) => type.GetGenericInterface(openType) != null)
                                                                                  select type))
                {
                    services.TryAddTransient(item);
                }
            }

            if (services.Any((ServiceDescriptor sd) => sd.ServiceType == typeof(IMapper)))
            {
                return services;
            }

            services.AddSingleton((Func<IServiceProvider, IConfigurationProvider>)((IServiceProvider sp) => new MapperConfiguration(sp.GetRequiredService<IOptions<MapperConfigurationExpression>>().Value)));
            services.Add(new ServiceDescriptor(typeof(IMapper), (IServiceProvider sp) => new Mapper(sp.GetRequiredService<IConfigurationProvider>(), sp.GetService), serviceLifetime));
            return services;
        }

        public static Type GetGenericInterface(this Type type, Type genericInterface)
        {
            if (type.IsGenericType(genericInterface))
            {
                return type;
            }

            Type[] interfaces = type.GetInterfaces();
            for (int num = interfaces.Length - 1; num >= 0; num--)
            {
                Type type2 = interfaces[num];
                if (type2.IsGenericType(genericInterface))
                {
                    return type2;
                }
            }

            return null;
        }

        public static bool IsGenericType(this Type type, Type genericType)
        {
            if (type.IsGenericType)
            {
                return type.GetGenericTypeDefinition() == genericType;
            }

            return false;
        }
    }
}
