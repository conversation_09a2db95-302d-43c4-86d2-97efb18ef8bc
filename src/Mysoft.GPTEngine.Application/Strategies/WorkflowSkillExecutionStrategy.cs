using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Planning.Handlebars;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin;

namespace Mysoft.GPTEngine.Application.Strategies
{
    /// <summary>
    /// 工作流技能执行策略
    /// </summary>
    public class WorkflowSkillExecutionStrategy : ISkillExecutionStrategy
    {
        /// <summary>
        /// 执行工作流技能
        /// </summary>
        public async Task<string> ExecuteAsync(IServiceProvider serviceProvider, Kernel kernel, ChatRunDto chatRunDto, IHttpContextAccessor httpContextAccessor,
            IMysoftContextFactory mysoftContextFactory, IMapper mapper, CancellationToken cancellationToken)
        {
            ILoggerFactory loggerFactory = serviceProvider.GetService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<WorkflowSkillExecutionStrategy>();

            // 导入系统插件
            kernel.ImportSystemPlugin(serviceProvider);
            kernel.FunctionInvocationFilters.Add(new FunctionInvocationFilter(httpContextAccessor, mysoftContextFactory, mapper, loggerFactory.CreateLogger<FunctionInvocationFilter>()));

            // 调试：记录所有已注册的插件和函数
            logger.LogInformation("[WorkflowSkillExecutionStrategy] 已注册的插件列表: {plugins}",
                string.Join(", ", kernel.Plugins.Select(p => p.Name)));

            foreach (var plugin in kernel.Plugins)
            {
                logger.LogInformation("[WorkflowSkillExecutionStrategy] 插件 {pluginName} 包含函数: {functions}",
                    plugin.Name, string.Join(", ", plugin.Select(f => f.Name)));
            }

            logger.LogInformation("[WorkflowSkillExecutionStrategy] OrchestrationTemplate: {template}",
                chatRunDto.SkillOrchestration.OrchestrationTemplate);

            // 创建并执行Handlebars模板计划
            var handlebarsPlan = new HandlebarsPlan(chatRunDto.SkillOrchestration.OrchestrationTemplate);
            string result = string.Empty;
            try
            {
                _ = await handlebarsPlan.InvokeAsync(kernel, chatRunDto.ChatArguments, cancellationToken).ConfigureAwait(false);
                result = await chatRunDto.GetArgumentValue<string>(KernelArgumentsConstant.Output) ?? string.Empty;
            }
            catch (KernelFunctionCanceledException)
            {
                // 已处理的异常，不需要额外动作
            }
            return result;
        }
    }
} 