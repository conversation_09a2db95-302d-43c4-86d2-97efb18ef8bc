using System;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Application.Strategies
{
    /// <summary>
    /// Agent技能执行策略
    /// </summary>
    public class AgentSkillExecutionStrategy : ISkillExecutionStrategy
    {
        /// <summary>
        /// 执行Agent技能
        /// </summary>
        public async Task<string> ExecuteAsync(IServiceProvider serviceProvider, Kernel kernel, ChatRunDto chatRunDto, IHttpContextAccessor httpContextAccessor,
            IMysoftContextFactory mysoftContextFactory, I<PERSON>apper mapper, CancellationToken cancellationToken)
        {
            // 在这里实现Agent模式的具体执行逻辑
            // 例如调用Agent服务、处理Agent响应等
            string result = string.Empty;
            try
            {
                // 获取logger实例
                ILoggerFactory loggerFactory = serviceProvider.GetService<ILoggerFactory>();
                var agentPlugin = new AgentSkillDomainService(serviceProvider, chatRunDto, loggerFactory.CreateLogger<AgentSkillDomainService>());
                await agentPlugin.ChatCompletionStream(cancellationToken).ConfigureAwait(false);
                result = await chatRunDto.GetArgumentValue<string>(KernelArgumentsConstant.Output) ?? string.Empty;
            }
            catch (KernelFunctionCanceledException)
            {
                // 已处理的异常，不需要额外动作
            }
            return result;
        }
    }
}
