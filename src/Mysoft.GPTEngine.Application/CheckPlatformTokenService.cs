using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Text;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Jose;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.ApiAuthorization;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.UnifiedApplicationAuthentication.Client.Models;
using Mysoft.UnifiedApplicationAuthentication.Client;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application
{
    public class CheckPlatformTokenService
    {
        private readonly ApplicationRepostory _applicationRepostory;
        private readonly ApplicationReleaseRepostory _applicationReleaseRepostory;
        private readonly ApplicationSiteRepostory _applicationSiteRepostory;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ApplicationSecurityRepostory _applicationSecurityRepostory;
        private IConfigurationService _configurationService;

        private ILogger<CheckPlatformTokenService> _logger;
        
        public CheckPlatformTokenService(ApplicationRepostory applicationRepostory, IConfigurationService configurationService, ApplicationReleaseRepostory applicationReleaseRepostory, 
            ApplicationSiteRepostory applicationSiteRepostory, ApplicationSecurityRepostory applicationSecurityRepostory, 
            ILogger<CheckPlatformTokenService> logger, IHttpContextAccessor httpContextAccessor)
        {
            _applicationRepostory = applicationRepostory;
            _applicationReleaseRepostory = applicationReleaseRepostory;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _applicationSiteRepostory = applicationSiteRepostory;
            _applicationSecurityRepostory = applicationSecurityRepostory;
            _configurationService = configurationService;
        }

        public async Task Check(HttpContext context, string url)
        {
            
            context.Request.Headers["tenantcode"] = "";
            context.Request.Headers["appid"] = "";
            context.Request.Headers["appkey"] = "";
            
            PlatformTokenBase64 platformTokenBase64 = GetPlatformToken(context);
            if (platformTokenBase64 == null || String.IsNullOrEmpty(platformTokenBase64.ApplicationCode) 
                                            || String.IsNullOrEmpty(platformTokenBase64.ReleaseCode)) return ;

            MysoftContext mysoftContext = new MysoftContext();
            mysoftContext.TenantCode = platformTokenBase64.PlatformToken.TenantCode;
            mysoftContext.GptBuilderUrl = url;
            var encrypt = AesHelper.EncryptStringToBytes(JsonConvert.SerializeObject(mysoftContext));

            _httpContextAccessor.HttpContext.Request.Headers[MysoftConstant.MysoftContext] =
                Convert.ToBase64String(encrypt);
            var application = await _applicationRepostory.GetSingleAsync((x => x.ApplicationCode == platformTokenBase64.ApplicationCode))
                .ConfigureAwait(false);

            if (application == null) return;
            
            mysoftContext.AuthType = 0;
            mysoftContext.EnableUserAuthorization = application.EnableUserAuthorization;
            mysoftContext.AuthorizationType = application.AuthorizationType;
            mysoftContext.ApplicationPublisherUserCode = platformTokenBase64.PlatformToken == null || string.IsNullOrEmpty(platformTokenBase64.PlatformToken.UserCode) ? "admin" : platformTokenBase64.PlatformToken.UserCode;

            var applicationRelease = _applicationReleaseRepostory.GetSingleAsync(
                x => x.ApplicationGUID == application.ApplicationGUID 
                     && x.ReleaseCode == platformTokenBase64.ReleaseCode);
            
            if (applicationRelease == null) return;
            
            if (! await CheckSite(context, application.ApplicationGUID)) return ;
            
            if (! await CheckUser(application)) return ;

            // PlatformToken为助手发布时share参数组成部分，由gpt-builder项目gptApplicationUrlParam接口生成
            context.Request.Headers["tenantCode"] = mysoftContext.TenantCode;
            context.Request.Headers["appid"] = platformTokenBase64.PlatformToken.AppId;
            context.Request.Headers["appkey"] = platformTokenBase64.PlatformToken.AppKey;
            
            // 使用统一应用认证
            string jwt = await GetMyApiAuthorization(mysoftContext);
            AccessTokenContent accessTokenContent = _httpContextAccessor.GetItem<AccessTokenContent>(nameof(AccessTokenContent));
            AuthHelper.AppendAuthHeaders(mysoftContext, context.Request.Headers, jwt, accessTokenContent);
        }

        public async Task<String> GetMyApiAuthorization(MysoftContext mysoftContext)
        {
            UnifiedApplicationAuthenticationOptions _options = new UnifiedApplicationAuthenticationOptions
            {
                Enable = true,
                JwkSetJson = _configurationService.GetConfigurationItemByKey("appSecret"),
                ClientId = mysoftContext.AuthInfo == null ? "4200" : mysoftContext.AuthInfo.ClientId
            };

            UnifiedApplicationAuthenticationClient _client = new UnifiedApplicationAuthenticationClient(_options);
            UnifiedApplicationAuthenticationProvider _provider = new UnifiedApplicationAuthenticationProvider(_client);
            var jwt = _client.CreateToken();
            return await Task.FromResult(jwt);
        }

        /// <summary>
        /// 平台转发请求的时候，会讲请求头中的额外参数都剔除，所以这里需要将请求头中的access-token添加到url参数中
        /// 外部应用挂在侧边栏的时候，没有真实的用户信息
        /// </summary>
        /// <param name="context"></param>
        public async Task GetAccessToken(HttpContext context)
        {
            if (!context.Request.Headers.ContainsKey("access-token"))
            {
                _logger.LogDebug( "access-token令牌过不存在");
                return;
            }
            var base64EncodedData = context.Request.Headers["access-token"].ToString();
            var tokenHandler = new JwtSecurityTokenHandler();
            var securityToken = tokenHandler.ReadToken(base64EncodedData) as JwtSecurityToken;
            if (securityToken != null)
            {
                await HandlerRequestBody(context, securityToken);
            }
            else
            {
                Console.WriteLine("Failed to read the token.");
            }
        }
        
        
        public  void SetAccessTokenToQueryParams(HttpContext context)
        {
            if (!context.Request.Headers.ContainsKey("access-token"))
            {
                return;
            }
            var base64EncodedData = context.Request.Headers["access-token"].ToString();
            var accessTokenParams = "x-access-token=" + base64EncodedData;
            var query = context.Request.QueryString.HasValue 
                ? context.Request.QueryString.Value + "&" + accessTokenParams 
                : "?" + accessTokenParams;
            context.Request.QueryString = new QueryString(query);
        }
        

        private async Task HandlerRequestBody(HttpContext context, JwtSecurityToken securityToken)
        {
            context.Request.EnableBuffering();
            // 读取请求体
            using (var reader = new StreamReader(context.Request.Body, Encoding.UTF8, true, 1024, true))
            {
                string body = await reader.ReadToEndAsync();

                // 解析 JSON 数据
                var data = JsonConvert.DeserializeObject<ChatListDto>(body);
                foreach (var claim in securityToken.Claims)
                {   
                    if (data.Arguments == null)
                    {
                        data.Arguments = new List<KeyValueDto>();
                    }
                    data.Arguments.Add(new KeyValueDto()
                    {
                        Key = claim.Type,
                        Value = claim.Value
                    });
                }

                // 将修改后的数据序列化为 JSON 字符串
                string modifiedBody = JsonConvert.SerializeObject(data);
                // 重置请求体位置
                context.Request.Body.Position = 0;
                // 清空请求体
                context.Request.Body = new MemoryStream();
                using (var writer = new StreamWriter(context.Request.Body, Encoding.UTF8, 1024, true))
                {
                    await writer.WriteAsync(modifiedBody);
                    await writer.FlushAsync();
                    context.Request.Body.Position = 0;
                }

                // 设置请求体长度
                context.Request.ContentLength = context.Request.Body.Length;
            }
        }

        public PlatformTokenBase64 GetPlatformToken(HttpContext context)
        {
            if (!context.Request.Headers.ContainsKey("platform-token"))
            {
                _logger.LogDebug( "令牌过不存在");
                return null;
            }
            var base64EncodedData = context.Request.Headers["platform-token"].ToString();
            try
            {
                byte[] data = Convert.FromBase64String(base64EncodedData);
                string base64EncodedDataStr = System.Text.Encoding.UTF8.GetString(data);
                PlatformTokenBase64 platformTokenBase64 = JsonConvert.DeserializeObject<PlatformTokenBase64>(base64EncodedDataStr);
                long unixTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                _logger.LogInformation("令牌过期校验：{0},{1}",platformTokenBase64.ExpireTime, unixTimestamp);
                if (platformTokenBase64.ExpireTime < unixTimestamp)
                {
                    _logger.LogError( "令牌过期或不存在");
                    return null;
                }
                
                var decrypt = AesHelper.Decrypt(platformTokenBase64.Token);
                PlatformToken platformToken = JsonConvert.DeserializeObject<PlatformToken>(decrypt);
                platformTokenBase64.PlatformToken = platformToken;
                return platformTokenBase64;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "令牌过期或不存在");
            }

            return null;
        }

        private async Task<bool> CheckSite(HttpContext context, Guid applicationGUID)
        {
            if (context.Request.Path == "/api/42000101/assistant/application/queryByShareCode")
            {
                var host = GetUrlHost(context);
                _logger.LogInformation( "站点检测：host:{0};  Referer:{1}",context.Request.Host.Host,host);
                if (host == context.Request.Host.Host) return await Task.FromResult(true);
                var siteList = await _applicationSiteRepostory.GetListAsync(x => x.ApplicationGUID  == applicationGUID).ConfigureAwait(false);
                if (siteList == null) return await Task.FromResult(false);
                foreach (var site in siteList)
                {
                    if (site.SiteDomainName == host || site.SiteDomainName.Split(":")[0] == host)
                    {
                        return await Task.FromResult(true);
                    }

                    if (site.SiteDomainName == "*")
                    {
                        return await Task.FromResult(true);
                    }

                    if (site.SiteDomainName.Split(".")[0].Trim() == "*" && IsValidDomain(host, site.SiteDomainName))
                    {
                        return await Task.FromResult(true);
                    }
                }
                
                _logger.LogError( "请求头Referer未匹配上:"+host);
                return await Task.FromResult(false);
            }
            return await Task.FromResult(true);
        }

        private static bool IsValidDomain(string domain, string wildcardRule)
        {
            // 提取通配符规则的关键部分
            var parts = wildcardRule.Split('.');
            var wildcardBase = string.Join(".", parts, 1, parts.Length - 1);

            // 构建正则表达式
            string pattern = "^.*\\." + Regex.Escape(wildcardBase) + "$";

            // 使用正则表达式验证域名
            return Regex.IsMatch(domain, pattern);
        }

        private string GetUrlHost(HttpContext context)
        {
            var url = "";
            if (context.Request.Headers.ContainsKey("Referer"))
            {
                url = context.Request.Headers["Referer"];
            }
            
            if (String.IsNullOrEmpty(url) && context.Request.Headers.ContainsKey("X-Referer"))
            {
                url = context.Request.Headers["X-Referer"];
            }

            if (String.IsNullOrEmpty(url))
            {
                _logger.LogError( "请求头中未获取到来源信息");
                return "";
            }
            
            
            Uri uriResult;
            bool result = Uri.TryCreate(url, UriKind.Absolute, out uriResult)
                          && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);

            if (result)
            {
                return uriResult.Host;
            }
            _logger.LogError( "请求头Referer格式错误:"+url);
            return "";
        }


        private async Task<bool> CheckUser(ApplicationEntity applicationEntity)
        {
            try
            {
                // 助手未开启用户鉴权，跳过用户鉴权
                if (applicationEntity.EnableUserAuthorization != 1)
                {
                    return await  Task.FromResult(true);
                }
                
                // 助手开启用户鉴权，需要校验access_token
                string accessToken = _httpContextAccessor.HttpContext.Request.Headers["access-token"];
            
                var applicationSecurity = await _applicationSecurityRepostory.
                    GetSingleAsync(x => x.ApplicationGUID == applicationEntity.ApplicationGUID)
                    .ConfigureAwait(false);
                
                var accessTokenContent = JwtHelper<AccessTokenContent>.ValidateJwtToken(accessToken, applicationSecurity.AppSecret);

                _httpContextAccessor.AddItem(nameof(AccessTokenContent), accessTokenContent);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "用户授权验证失败");
                return await  Task.FromResult(false);
            }
            
            return await  Task.FromResult(true);

        }
    }

    public class PlatformTokenBase64
    {
        [JsonPropertyName("applicationCode")]
        public string ApplicationCode { get; set; }
        
        [JsonPropertyName("releaseCode")]
        public string ReleaseCode { get; set; }
        
        [JsonPropertyName("expiretime")]
        public long ExpireTime { get; set; }

        [JsonPropertyName("token")]
        public string Token { get; set; }
        
        [JsonPropertyName("platformToken")]
        public PlatformToken PlatformToken { get; set; }
        
    }
    
    public class PlatformToken
    {
        [JsonPropertyName("tenantcode")]
        public string TenantCode { get; set; }
        
        [JsonPropertyName("appid")]
        public string AppId { get; set; }
        
        [JsonPropertyName("appkey")]
        public string AppKey { get; set; }

        [JsonPropertyName("userCode")]
        public string UserCode { get; set; }

    }
}