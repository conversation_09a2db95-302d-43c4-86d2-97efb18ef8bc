using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application.ApprovalDatasource
{
    public class GptSkillDatasourceService : IApprovalDatasource
    {
        
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly IMysoftApiService _mysoftApiService;
        public readonly IMysoftIPassApiService _mysoftIPassApiService;
        public readonly ILogger<GptSkillDatasourceService> _logger;
        public readonly PluginRepostory _pluginRepostory;
        public readonly AgentAppService _agentAppService;

        
        public GptSkillDatasourceService(IMysoftContextFactory mysoftContextFactory, 
            IMysoftApiService mysoftApiService , IMysoftIPassApiService mysoftIPassApiService,
            PluginRepostory pluginRepostory, AgentAppService agentAppService,
            ILogger<GptSkillDatasourceService> logger)
        {
            _mysoftContextFactory = mysoftContextFactory;
            _logger = logger;
            _mysoftApiService = mysoftApiService;
            _mysoftIPassApiService = mysoftIPassApiService;
            _pluginRepostory = pluginRepostory;
            _agentAppService = agentAppService;
        }


        public override async Task RequestBody(PlanDatasourceInstanceDto datasource, PlanInstanceInfoDto planInstanceInfoDto, CancellationToken cancellationToken = default)
        { 
            ChatInputDto chatInput = await FormatChatInputDto(datasource, planInstanceInfoDto);
            var actionResultDto = await _agentAppService.ChatCompletionAsync(chatInput, false, cancellationToken);
            if (actionResultDto.Success == false)
            {
                throw new BusinessException(datasource.SourceId + "获取数据失败：" + JsonConvert.SerializeObject(actionResultDto));
            }
            datasource.RequestBody = ((ActionResultDto<string>)actionResultDto).Data;
            await Task.CompletedTask;
        }

        private async Task<ChatInputDto> FormatChatInputDto(PlanDatasourceInstanceDto datasource, PlanInstanceInfoDto planInstanceInfoDto)
        {
            List<KeyValueDto> arguments = await DefaultAttachments();
            var paramsValueMapping = planInstanceInfoDto.ParamsValueMapping;
            foreach (var kv in paramsValueMapping)
            {
                var id = datasource.PlanDatasourceGUID + "_";
                if (!kv.Key.StartsWith(id))
                {
                    continue;
                }
                var field = kv.Key.Substring(id.Length);
                var v = kv.Value.Value is string ? kv.Value.Value.ToString() : JsonConvert.SerializeObject(kv.Value.Value);
                arguments.Add(new KeyValueDto()
                {
                    Key = field,
                    Value = v
                });
            }
            ChatInputDto chatInput = new ChatInputDto()
            {
                AssistanGUID = Guid.Parse(MysoftConstant.DefaultAssistant),
                SkillGUID = Guid.Parse(datasource.SourceId),
                ChatGUID = Guid.NewGuid(),
                Arguments = arguments
            };
            return await Task.FromResult(chatInput);
        }

        private async Task<List<KeyValueDto>> DefaultAttachments()
        {
            List<KeyValueDto> arguments = new List<KeyValueDto>();
            arguments.Add(new KeyValueDto()
            {
                Key = KernelArgumentsConstant.CurrentOrchestrationGUID,
                Value = Guid.Empty.ToString()
            });
            arguments.Add(new KeyValueDto()
            {
                Key = KernelArgumentsConstant.CurrentNodeGUID,
                Value = Guid.Empty.ToString()
            });
            return await Task.FromResult(arguments);
        }
        
    }
}