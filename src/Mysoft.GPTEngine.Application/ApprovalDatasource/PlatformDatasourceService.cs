using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application.ApprovalDatasource
{
    public class PlatformDatasourceService : IApprovalDatasource
    {
        
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly IMysoftApiService _mysoftApiService;
        public readonly IMysoftIPassApiService _mysoftIPassApiService;
        public readonly ILogger<PlatformDatasourceService> _logger;
        
        public PlatformDatasourceService(IMysoftContextFactory mysoftContextFactory, 
            IMysoftApiService mysoftApiService , IMysoftIPassApiService mysoftIPassApiService,
            ILogger<PlatformDatasourceService> logger)
        {
            _mysoftContextFactory = mysoftContextFactory;
            _logger = logger;
            _mysoftApiService = mysoftApiService;
            _mysoftIPassApiService = mysoftIPassApiService;
        }
        
        public override async Task RequestBody(PlanDatasourceInstanceDto datasource, PlanInstanceInfoDto planInstanceInfoDto, CancellationToken cancellationToken = default)
        {
            var path = datasource.SourceId;
            var requestParams = new PlatformDataObjectRequestDto()
            {
                AppCode = planInstanceInfoDto.WorkSpaceCode,
                SchemaId = datasource.SourceId,
                Params = FormatGetRequestParams(datasource, planInstanceInfoDto)
            };

            var bodyJson = JsonConvert.SerializeObject(requestParams);
            
            datasource.RequestBody = await PlatFormDataApiExec(GPTBuilderRequestPathConst.RequestPlatformDataObject, bodyJson);
        }
        
        protected async Task<string> PlatFormDataApiExec(string uri, string bodyJson)
        {
            var context = _mysoftContextFactory.GetMysoftContext();
            _logger.LogInformation("准备请求了：路径：{0},参数:{1}", context.GptBuilderUrl + uri, bodyJson);
            var result = await _mysoftApiService.PostAsync(context.GptBuilderUrl + uri, bodyJson);
            _logger.LogInformation("拿到返回结果了：{0}", result);
            return await Task.FromResult(JsonConvert.DeserializeObject<ReturnDto<string>>(result).Data);
        }
        
        
    }
}