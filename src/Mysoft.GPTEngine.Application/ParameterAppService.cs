using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel.Memory;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.SemanticKernel.Core.Parsers;


namespace Mysoft.GPTEngine.Application
{
#pragma warning disable CS0618 // 类型或成员已过时
#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0020
#pragma warning disable SKEXP0050
    public class ParameterAppService : AppServiceBase
    {
        private readonly IJsonOutputParsers _jsonOutputParsers;
        private readonly AgentDomainService _agentDomainService;
        private readonly MemoryBuilder _memoryBuilder;
        private readonly MysoftApiService _mysoftApiDomainService;
        private Kernel _kernel;
        private readonly IMysoftContextFactory mysoftContextFactory;
        

        public ParameterAppService(IJsonOutputParsers jsonOutputParsers , Kernel kernel, MemoryBuilder memoryBuilder, AgentDomainService agentDomainService, 
            MysoftApiService mysoftApiDomainService,  IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, IMapper mapper, ILogger<ParameterAppService> logger) : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            this.mysoftContextFactory = mysoftContextFactory;
            _jsonOutputParsers = jsonOutputParsers;
            _kernel = kernel;
            _agentDomainService = agentDomainService;
            _memoryBuilder = memoryBuilder;
            _mysoftApiDomainService = mysoftApiDomainService;
        }
        
        public async Task<ActionResultDto> ParamMapping(ParamMappingRequestDto paramMappingRequestDto)
        {
            //准备数据
            ModelInstanceDto modelInstance = new ModelInstanceDto() { IsDefault = true };
            string question = await ExecAIChat("ParameterPlugin.ParamMapping.txt", JsonConvert.SerializeObject(paramMappingRequestDto), this._kernel, modelInstance);
            question = JsonValidateHelper.ExtractJson(question);
            List<ParamMappingResponseDto> resultFormat = JsonConvert.DeserializeObject<List<ParamMappingResponseDto>>(question);
            return await Succeed(resultFormat);
        }
    }
}
