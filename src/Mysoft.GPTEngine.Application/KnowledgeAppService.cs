using AutoMapper;
using DocumentFormat.OpenXml.Office.PowerPoint.Y2022.M08.Main;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.ML.Tokenizers;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Milvus;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Memory;
using Milvus.Client;
using Mysoft.GPTEngine.Application.Configuration;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Common.Chunker;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.Plugin.System;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Parsers;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using FieldData = Milvus.Client.FieldData;
using Mysoft.GPTEngine.Domain.Repositories.KnowledgeQuestion;
using Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion;
using SqlSugar;
using static Mysoft.GPTEngine.Plugin.System.DocumentAnalysisPlugin;

namespace Mysoft.GPTEngine.Application
{
#pragma warning disable CS0618 // 类型或成员已过时
#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0020
#pragma warning disable SKEXP0050
    public class KnowledgeAppService : AppServiceBase
    {
        private readonly IJsonOutputParsers _jsonOutputParsers;
        private readonly MemoryBuilder _memoryBuilder;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
        private readonly KnowledgeRepository _knowledgeRepository;
        private readonly KnowledgeFileRepository _knowledgeFileRepository;
        private readonly KnowledgeHyperLinkRepostory _knowledgeHyperLinkRepostory;
        private readonly IKnowledgeDomainService _iKnowledgeDomainService;
        private readonly KnowledgeQuestionRepostory _knowledgeQuestionRepostory;
        private ITextEmbeddingGenerationService _embeddingGenerationService;
        private readonly KnowledgeTaskRepostory _knowledgeTaskRepostory;
        private readonly KnowledgeTaskDetailRepostory _knowledgeTaskDetailRepostory;
        private readonly KnowledgeTaskDataDetailRepostory _knowledgeTaskDataDetailRepostory;
        private readonly KnowledgeTaskKeyWordRepostory _knowledgeTaskKeyWordRepostory;
        private readonly KnowledgeTaskRecordRepostory _knowledgeTaskRecordRepostory;
        private Kernel _kernel;
        private readonly SearchParameters _searchParameters = new SearchParameters()
        {
            OutputFields = { "id", "comment", "metadata" }
        };
        private readonly ILogger<KnowledgePlugin> _logger;
        private readonly string metadataFileSection = JsonConvert.SerializeObject(new CollectionMetadataDto("1"));
        private readonly string metadataQuestione = JsonConvert.SerializeObject(new CollectionMetadataDto("2"));
        private readonly IMysoftContextFactory mysoftContextFactory;
        private const string IdFieldName = "id";
        private const string Metadata = "additional_metadata";
        private const string EmbeddingFieldName = "embedding";
        private const string DefaultIndexName = "default";
        private const int DefaultVarcharLength = 65_535;
        private const int _vectorSize = 1536;
        //准备数据
        private ModelInstanceDto modelInstance = new ModelInstanceDto() { IsDefault = true };
        private static readonly Tokenizer s_tokenizer = Tokenizer.CreateTiktokenForModel("gpt-4");
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly QuestionRepository _questionRepository;
        private readonly QuestionRelationRepository _questionRelationRepository;

        public KnowledgeAppService(KnowledgeTaskRecordRepostory knowledgeTaskRecordRepostory,KnowledgeTaskKeyWordRepostory knowledgeTaskKeyWordRepostory, KnowledgeTaskRepostory knowledgeTaskRepostory, KnowledgeTaskDetailRepostory knowledgeTaskDetailRepostory, KnowledgeTaskDataDetailRepostory knowledgeTaskDataDetailRepostory,
            ILogger<KnowledgePlugin> logger,IKnowledgeDomainService knowledgeDomainService, KnowledgeHyperLinkRepostory knowledgeHyperLinkRepostory, KnowledgeRepository knowledgeRepository, IJsonOutputParsers jsonOutputParsers, KnowledgeFileSectionRepository knowledgeFileSectionRepository, KnowledgeQuestionRepostory knowledgeQuestionRepostory
            , QuestionRepository questionRepository, QuestionRelationRepository questionRelationRepository, KnowledgeFileRepository knowledgeFileRepository, Kernel kernel, MemoryBuilder memoryBuilder, 
            MysoftApiService mysoftApiDomainService, IKnowledgeDomainService iKnowledgeDomainService, IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, IMapper mapper) : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            this.mysoftContextFactory = mysoftContextFactory;
            _jsonOutputParsers = jsonOutputParsers;
            _kernel = kernel;
            _memoryBuilder = memoryBuilder;
            _mysoftApiDomainService = mysoftApiDomainService;
            _knowledgeFileSectionRepository = knowledgeFileSectionRepository;
            _knowledgeRepository = knowledgeRepository;
            _knowledgeFileRepository = knowledgeFileRepository;
            _iKnowledgeDomainService = iKnowledgeDomainService;
            _knowledgeQuestionRepostory = knowledgeQuestionRepostory;
            _knowledgeHyperLinkRepostory = knowledgeHyperLinkRepostory;
            _knowledgeDomainService = knowledgeDomainService;
            _knowledgeTaskRepostory = knowledgeTaskRepostory;
            _knowledgeTaskDetailRepostory = knowledgeTaskDetailRepostory;
            _knowledgeTaskDataDetailRepostory = knowledgeTaskDataDetailRepostory;
            _knowledgeTaskKeyWordRepostory = knowledgeTaskKeyWordRepostory;
            _knowledgeTaskRecordRepostory = knowledgeTaskRecordRepostory;
            _questionRepository = questionRepository;
            _questionRelationRepository = questionRelationRepository;
            _logger = logger;
        }

        /// <summary>
        /// 根据表单json解析Word字段
        /// </summary>
        /// <param name="analysisWordDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> AnalysisWordByForm(AnalysisWordDto analysisWordDto)
        {
            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(analysisWordDto.wordDownloadUrl); // 下载文件

                using (MemoryStream stream = new MemoryStream(fileBytes)) // 将文件转换为文件流
                {
                    //打开一个open xml
                    var wordprocessingDocument = WordprocessingDocument.Open(stream, false);
                    try
                    {
                        // 获取文档的主体部分
                        Body body = wordprocessingDocument.MainDocumentPart.Document.Body;

                        // 查找带有下划线的段落
                        var underlinedParagraphs = body.Descendants<DocumentFormat.OpenXml.Wordprocessing.Paragraph>()
                            .Where(p => p.Descendants<Underline>().Any());

                        // 遍历并打印带有下划线的段落文本
                        foreach (var paragraph in underlinedParagraphs)
                        {
                            Console.WriteLine(paragraph.InnerText);
                        }
                        return await Succeed("");
                    }
                    catch (Exception ex)
                    {
                        return await Failed(ex.Message);
                    }
                }
            }
        }

        /// <summary>
        /// 文本提取
        /// </summary>
        /// <param name="type"></param>
        /// <param name="stream"></param>
        /// <returns></returns>
        public async Task<FileContent> TextExtraction(MemoryStream stream, string fileType, ExecutionSetting executionSetting)
        {
            IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(fileType, this._kernel,this._mysoftApiDomainService,this.mysoftContextFactory,this._httpContextAccessor,this.Mapper);
            FileContent fileContent = await documentDecoder.DecodeAsync(stream, executionSetting);
            stream.Dispose();
            GC.Collect();
            //插入超链接
            await InsertHyperLink(fileContent);
            return fileContent;
        }

        /// <summary>
        /// 把解析出来的超链接入库
        /// </summary>
        /// <param name="fileContent"></param>
        /// <returns></returns>
        public async Task InsertHyperLink(FileContent fileContent)
        {
            if (fileContent.HyperLinkList.Count == 0)
            {
                return;
            }
            List<KnowledgeHyperLinkEntity> knowledgeHyperLinkEntities = new List<KnowledgeHyperLinkEntity>();
            foreach (var item in fileContent.HyperLinkList)
            {
                KnowledgeHyperLinkEntity knowledgeHyperLinkEntity = new KnowledgeHyperLinkEntity();
                knowledgeHyperLinkEntity.KnowledgeHyperLinkGUID = item.HyperLinkGUID;
                knowledgeHyperLinkEntity.HyperLinkURL = item.HyperLinkURL;
                knowledgeHyperLinkEntity.Name = item.HyperLinkText;
                knowledgeHyperLinkEntities.Add(knowledgeHyperLinkEntity);
            }
            await _knowledgeHyperLinkRepostory.InsertRangeAsync(knowledgeHyperLinkEntities);
        }

        /// <summary>
        /// 触发前端刷新页面
        /// </summary>
        /// <param name="knowledgeCode"></param>
        /// <returns></returns>
        public async Task knowledgeDoQuery(KnowledgeDoQueryDto knowledgeDoQueryDto)
        {
            if (knowledgeDoQueryDto == null)
            {
                return;
            }
            string gptBuilderUrl = this.MysoftContext.GptBuilderUrl + GPTBuilderRequestPathConst.KnowledgeDoQuery;
            await _mysoftApiDomainService.PostAsync(gptBuilderUrl, JsonConvert.SerializeObject(knowledgeDoQueryDto));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fileContent"></param>
        /// <param name="knowledgeFileGUID"></param>
        /// <returns></returns>
        public async Task<List<TextPartitioningDto>> TextPartitioning(FileContent fileContent, Guid knowledgeFileGUID, SectionConfig sectionConfig)
        {
            // Use a different partitioning strategy depending on the file type
            List<string> partitions = new List<string>();
            List<string> sentences = new List<string>();
            List<TextPartitioningDto> textPartitioningDtos = new List<TextPartitioningDto>();
            int index = 1;
            var overlapTokens = sectionConfig.CustomConfig.OverlappingTokens;
            var maxTokensPerLine = sectionConfig.CustomConfig.MaxTokensPerParagraph > 100 ? 100 : sectionConfig.CustomConfig.MaxTokensPerParagraph;
            var maxTokensPerParagraph = sectionConfig.CustomConfig.MaxTokensPerParagraph;
            string?[] split = sectionConfig.CustomConfig.GetSplitSymbol();

            foreach (FileSection fileSection in fileContent.Sections)
            {
                partitions = new List<string>();
                string content = fileSection.Content;
                sentences = TextChunkerNew.InternalSplitLines(fileSection.Content, maxTokensPerLine, trim: true, split,t=>s_tokenizer.CountTokens(t));
                sentences = TextChunkerNew.InternalSplitTextParagraphs(sentences,maxTokensPerParagraph,overlapTokens,null,
                    (text, maxTokens, tokenCounter) => TextChunkerNew.InternalSplitLines(text,maxTokens,trim: false,split,tokenCounter),
                    t=>s_tokenizer.CountTokens(t));
                partitions.AddRange(sentences);
                textPartitioningDtos.Add(KnowledgeFileSectionHandle(fileSection, partitions, knowledgeFileGUID, index));
                index += partitions.Count;
            }

            return await Task.FromResult(textPartitioningDtos);
        }

        /// <summary>
        /// 解析切片和标题关系
        /// </summary>
        /// <param name="fileSection"></param>
        /// <param name="partitions"></param>
        /// <param name="knowledgeFileGUID"></param>
        /// <returns></returns>
        public TextPartitioningDto KnowledgeFileSectionHandle(FileSection fileSection, List<string> partitions, Guid knowledgeFileGUID, int index)
        {
            TextPartitioningDto textPartitioningDto = new TextPartitioningDto();
            List<KnowledgeFileSectionEntity> knowledgeFileSectionEntities = new List<KnowledgeFileSectionEntity>();

            for (var i = 0; i < partitions.Count; i++)
            {
                KnowledgeFileSectionEntity knowledgeFileSectionEntity = new KnowledgeFileSectionEntity();
                knowledgeFileSectionEntity.KnowledgeFileSectionGUID = Guid.NewGuid();
                knowledgeFileSectionEntity.Content = partitions[i];
                knowledgeFileSectionEntity.KnowledgeFileGUID = knowledgeFileGUID;
                knowledgeFileSectionEntity.SectionSize = partitions[i].Length;
                knowledgeFileSectionEntity.SectionTokens = String.IsNullOrEmpty(knowledgeFileSectionEntity.Content) ? 0 : s_tokenizer.CountTokens(knowledgeFileSectionEntity.Content);
                knowledgeFileSectionEntity.SectionNumber = index;
                knowledgeFileSectionEntity.CreatedTime = TimeZoneUtility.LocalNow();
                knowledgeFileSectionEntity.ThirdId = fileSection.ThirdId;
                knowledgeFileSectionEntity.Metadata = fileSection.Metadata;
                index++;
                knowledgeFileSectionEntities.Add(knowledgeFileSectionEntity);
            }
            textPartitioningDto.KnowledgeFileSections = knowledgeFileSectionEntities;
            textPartitioningDto.ParagraphTitle = fileSection.Title;
            

            return textPartitioningDto;
        }

        /// <summary>
        ///  PDF文件转图片
        /// </summary>
        /// <param name="documentDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> ConvertDocumentToImg(DocumentDto documentDto)
        {
            try
            {
                DocumentDto res = new DocumentDto();
                MemoryStream stream = getFileByUrl(documentDto.DownloadUrl);
                string type = FileTypeConvertHelper.GetFileType(documentDto.FileName);
                IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(type, this._kernel, this._mysoftApiDomainService, this.mysoftContextFactory, this._httpContextAccessor, this.Mapper);
                using (stream)
                {
                    List<ImageUploadCallBackDto> documentUploadCallBack = await documentDecoder.ConvertDocumentToImg(stream);
                    if(documentUploadCallBack != null && documentUploadCallBack.Count > 0)
                    {
                        res.DocumentGUID = documentUploadCallBack[0].documentUploadResult.documentGuid;
                    }
                    else
                    {
                        res.DocumentGUID = documentDto.DocumentGUID;
                    }
                }

                return await Succeed(res);
            }
            catch(Exception ex)
            {
                return await Failed(ex.Message);
            }
        }

        /// <summary>
        /// 删除向量数据
        /// </summary>
        /// <param name="documentFileDto"></param>
        /// <param name="thirds"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> DeleteEmbeddingInfoByFileGUID(DocumentFileDto documentFileDto, List<string> thirds = null)
        {
            try
            { 
                List<KnowledgeFileEntity> knowledgeFileEntities = await _knowledgeFileRepository.GetListAsync(f =>
                    f.KnowledgeFileGUID == documentFileDto.knowledgeFileGUID);
                
                List<Guid> fileIds = knowledgeFileEntities.Select(s => s.KnowledgeFileGUID).ToList();
                
                thirds = thirds ?? new List<string>();
                //查询出所有切片信息
                List<KnowledgeFileSectionEntity> knowledgeFileSectionEntities = await _knowledgeFileSectionRepository
                    .GetListAsync(f => f.KnowledgeFileGUID == documentFileDto.knowledgeFileGUID);
                
                if (fileIds.Count > 0 && thirds.Count > 0)
                {
                    List<KnowledgeFileSectionEntity> knowledgeFileSectionEntitiesByThirds = await _knowledgeFileSectionRepository
                        .GetListAsync(f => thirds.Contains(f.ThirdId) && fileIds.Contains(f.KnowledgeFileGUID));
                    knowledgeFileSectionEntities.AddRange(knowledgeFileSectionEntitiesByThirds);
                }
                
                List<Guid> questionGuids = knowledgeFileSectionEntities.Select(s => s.KnowledgeFileSectionGUID).ToList();
                List<KnowledgeQuestionEntity> knowledgeQuestionEntities = await _knowledgeQuestionRepostory.GetListAsync(f => questionGuids.Contains(f.KnowledgeFileSectionGUID));
                //TODO 这里看看批量删除的方法
                MilvusCollection milvusCollection = InitMilvusCollection(documentFileDto.knowledgeCode);
                //删除提示问
                foreach (var item in knowledgeQuestionEntities)
                {
                    await DeleteEmbeddingAsync(milvusCollection, item.KnowledgeFileSectionQuestionGUID.ToString());
                }
                //删除切片
                foreach (var item in knowledgeFileSectionEntities)
                {
                    await DeleteEmbeddingAsync(milvusCollection, item.KnowledgeFileSectionGUID.ToString());
                }
                await _knowledgeQuestionRepostory.DeleteAsync(d => questionGuids.Contains(d.KnowledgeFileSectionGUID));
                await _knowledgeFileSectionRepository.DeleteAsync(d => d.KnowledgeFileGUID == documentFileDto.knowledgeFileGUID);
                if (fileIds.Count > 0 && thirds.Count > 0)
                {
                    await _knowledgeFileSectionRepository
                        .DeleteAsync(f => thirds.Contains(f.ThirdId) && fileIds.Contains(f.KnowledgeFileGUID));
                }
                await _questionRelationRepository.DeleteAsync(d => questionGuids.Contains(d.KnowledgeSectionGUID));
                return await Succeed();
            }
            catch (Exception ex)
            {
                return await Failed(ex.Message);
            }
        }

        /// <summary>
        /// 删除知识库
        /// </summary>
        /// <param name="documentFileDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> DeleteKnowledge(DocumentFileDto documentFileDto)
        {
            try
            {
                //创建一个milvus client
                MilvusCollection collection = InitMilvusCollection(documentFileDto.knowledgeCode);
                await collection.DropAsync();
                return await Succeed("");
            }
            catch (Exception ex)
            {
                return await Failed(ex.Message);
            }
        }

        private MemoryStream getFileByUrl(string downloadUrl)
        {
            //解析一下文本
            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(downloadUrl); // 下载文件

                MemoryStream stream = new MemoryStream(fileBytes);

                return stream;
            }
        }

        /// <summary>
        /// java文件上传后的回调方法
        /// </summary>
        /// <param name="documentFileDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> SaveEmbeddingFile(DocumentFileDto documentFileDto)
        {
            var knowledgeFile = await _knowledgeFileRepository.GetAsync(f => f.KnowledgeFileGUID == documentFileDto.knowledgeFileGUID);
            var myContext = mysoftContextFactory.GetMysoftContext();
            KnowledgeDoQueryDto knowledgeDoQueryDto = new KnowledgeDoQueryDto();
            knowledgeDoQueryDto.KnowledgeCode = documentFileDto.knowledgeCode;
            knowledgeDoQueryDto.KnowledgeNotificationInfo = KnowledgeNotificationConst.KnowledgeDoQuery;
            try
            {
                // 获取配置
                var executionSetting = ExecutionConfigUtil.GetDefaultConfig(knowledgeFile.ExecutionSetting, knowledgeFile.FileType, knowledgeFile.IsUploadImage);
                await InitEmbeddingService(documentFileDto.knowledgeCode);
                //知识库code 作为Collection名，需要先判断一下是否存在，不存在则创建
                MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient(myContext.TenantCode);
                await CreateCollectionAsync(milvusClient, documentFileDto.knowledgeCode);
                MilvusCollection milvusCollection = milvusClient.GetCollection(documentFileDto.knowledgeCode);
                // 下载文件
                MemoryStream stream = getFileByUrl(documentFileDto.downloadUrl);
                string type = FileTypeConvertHelper.GetFileType(documentFileDto.fileName);
                //1、执行文本提取
                FileContent fileContent = await TextExtraction(stream, type, executionSetting);
                //2、切片
                List<TextPartitioningDto> textPartitioningDtos = await TextPartitioning(fileContent, documentFileDto.knowledgeFileGUID.Value, executionSetting.SectionConfig);
                List<string> thirds = fileContent.Sections.Select(s => s.ThirdId).ToList();
                //兼容一下重新向量化的场景 删除向量+数据库数据
                await DeleteEmbeddingInfoByFileGUID(documentFileDto, thirds);
                //数据库 + 向量 保存
                await SaveTextPartitioningDto(milvusCollection, textPartitioningDtos);
                //6、刷新状态
                knowledgeFile.IndexStatusEnum = (int)IndexStatusEnum.Completed;
                await _knowledgeFileRepository.UpdateAsync(knowledgeFile);
                //await UpdateKnowledgeStatusEnum(knowledgeFile.KnowledgeGUID);
                //刷新页面
                await knowledgeDoQuery(knowledgeDoQueryDto);
                return await Succeed();
            }
            catch (Exception ex)
            {
                knowledgeFile.IndexStatusEnum = (int)IndexStatusEnum.Fail;
                await _knowledgeFileRepository.UpdateAsync(knowledgeFile);
                //await UpdateKnowledgeStatusEnum(knowledgeFile.KnowledgeGUID);
                Console.WriteLine(ex.StackTrace);
                Console.WriteLine(ex.Message);
                Console.WriteLine(ex.InnerException);
                //刷新页面
                await knowledgeDoQuery(knowledgeDoQueryDto);
                return await Failed(ex.Message);
            }
        }

        /// <summary>
        /// 自定义一个向量插入
        /// </summary>
        /// <param name="milvusCollection"></param>
        /// <param name="id"></param>
        /// <param name="content"></param>
        /// <param name="metadata"></param>
        /// <returns></returns>
        public async Task InsertAsync(Milvus.Client.MilvusCollection milvusCollection, string id, string content, string metadata)
        {
            content = await ReplaceImager(content);
            var embedding = await this._embeddingGenerationService.GenerateEmbeddingAsync(content, this._kernel).ConfigureAwait(false);
            await milvusCollection.InsertAsync(new FieldData[]
                {
                    FieldData.CreateFloatVector(EmbeddingFieldName, new ReadOnlyMemory<float>[] { embedding }),
                    FieldData.Create(IdFieldName, new List<string>{id }),
                    FieldData.CreateJson(Metadata, new List<string>{metadata})
                });
        }

        /// <summary>
        /// 删除一个向量
        /// </summary>
        /// <param name="milvusCollection"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task DeleteEmbeddingAsync(MilvusCollection milvusCollection, string id)
        {
            await milvusCollection.DeleteAsync($@"id in [""{id}""]");
        }

        /// <summary>
        /// 初始化milvus集合
        /// </summary>
        /// <param name="knowledgeCode"></param>
        /// <returns></returns>
        public MilvusCollection InitMilvusCollection(string knowledgeCode)
        {
            var myContext = mysoftContextFactory.GetMysoftContext();
            MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient(myContext.TenantCode);
            MilvusCollection milvusCollection = milvusClient.GetCollection(knowledgeCode);
            return milvusCollection;
        }

        private async Task<string> ReplaceImager(string content)
        {
            if (String.IsNullOrEmpty(content))
            {
                return await Task.FromResult(content); 
            }
            string imagePattern = @"!\[(.*?)\]\((.*?)\)";
            MatchCollection matches = Regex.Matches(content, imagePattern);
            
            foreach (Match match in matches)
            {
                string image = match.Groups[0].Value;
                content = content.Replace(image, "");
            }

            return await Task.FromResult(content);
        }

        /// <inheritdoc />
        public async Task CreateCollectionAsync(Milvus.Client.MilvusClient milvusClient, string collectionName, CancellationToken cancellationToken = default)
        {
            var exists = await milvusClient.HasCollectionAsync(collectionName, cancellationToken: cancellationToken).ConfigureAwait(false);
            if (!exists)
            {
                CollectionSchema schema = new CollectionSchema()
                {
                    Fields =
                {
                    FieldSchema.CreateVarchar(IdFieldName, maxLength: DefaultVarcharLength, isPrimaryKey: true, autoId: false),
                    FieldSchema.CreateFloatVector(EmbeddingFieldName, _vectorSize),
                    FieldSchema.CreateJson(Metadata)
                },
                    EnableDynamicFields = true
                };

                MilvusCollection collection = await milvusClient.CreateCollectionAsync(collectionName, schema, ConsistencyLevel.Session, cancellationToken: cancellationToken).ConfigureAwait(false);

                await collection.CreateIndexAsync(EmbeddingFieldName, metricType: SimilarityMetricType.Ip, indexName: DefaultIndexName, cancellationToken: cancellationToken).ConfigureAwait(false);
                await collection.WaitForIndexBuildAsync("float_vector", DefaultIndexName, cancellationToken: cancellationToken).ConfigureAwait(false);

                await collection.LoadAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                await collection.WaitForCollectionLoadAsync(waitingInterval: TimeSpan.FromMilliseconds(100), timeout: TimeSpan.FromMinutes(1), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        /// <param name="documentFileDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> RefreshKnowledgeFileSection(DocumentFileDto documentFileDto)
        {
            var knowledgeFile = await _knowledgeFileRepository.GetAsync(f => f.KnowledgeFileGUID == documentFileDto.knowledgeFileGUID);
            try
            {
                await InitEmbeddingService(documentFileDto.knowledgeCode);
                //加载collection
                Milvus.Client.MilvusCollection milvusCollection = InitMilvusCollection(documentFileDto.knowledgeCode);
                //1、切片guid不为空，刷新单条，不然按文件guid刷新
                if (documentFileDto.knowledgeFileSectionGUID != null)
                {
                    KnowledgeFileSectionEntity knowledgeFileSectionEntity = await _knowledgeFileSectionRepository.GetAsync(s => s.KnowledgeFileSectionGUID == documentFileDto.knowledgeFileSectionGUID.Value);
                    await DeleteEmbeddingAsync(milvusCollection, documentFileDto.knowledgeFileSectionGUID.ToString());
                    await InsertAsync(milvusCollection, documentFileDto.knowledgeFileSectionGUID.ToString(), knowledgeFileSectionEntity.Content, metadataFileSection);
                }
                else if (documentFileDto.knowledgeFileSectionGUIDs != null)
                {
                    foreach (var guid in documentFileDto.knowledgeFileSectionGUIDs)
                    {
                        KnowledgeFileSectionEntity knowledgeFileSectionEntity =
                            await _knowledgeFileSectionRepository.GetAsync(s =>
                                s.KnowledgeFileSectionGUID == guid);
                        if (knowledgeFileSectionEntity != null)
                        {
                            var content = knowledgeFileSectionEntity.Content;
                            if (!String.IsNullOrEmpty(knowledgeFileSectionEntity.ParagraphTitle))
                            {
                                content = knowledgeFileSectionEntity.ParagraphTitle + " " + content +
                                    " " + knowledgeFileSectionEntity.ParagraphTitle;
                            }
                            await InsertAsync(milvusCollection, knowledgeFileSectionEntity.KnowledgeFileSectionGUID.ToString(), content, metadataFileSection);
                        }
                    }
                }
                else if (documentFileDto.knowledgeFileGUID != Guid.Empty)
                {
                    //找到文件所有切片信息
                    List<KnowledgeFileSectionEntity> knowledgeFileSectionEntities = await _knowledgeFileSectionRepository.GetListAsync(g => g.KnowledgeFileGUID == documentFileDto.knowledgeFileGUID);
                    foreach (KnowledgeFileSectionEntity knowledgeFileSectionEntity in knowledgeFileSectionEntities)
                    {
                        await DeleteEmbeddingAsync(milvusCollection, knowledgeFileSectionEntity.KnowledgeFileSectionGUID.ToString());
                        await InsertAsync(milvusCollection, knowledgeFileSectionEntity.KnowledgeFileSectionGUID.ToString(), knowledgeFileSectionEntity.Content, metadataFileSection);
                    }
                }
                knowledgeFile.IndexStatusEnum = (int)IndexStatusEnum.Completed;
                await _knowledgeFileRepository.UpdateAsync(knowledgeFile);
                return await Succeed("");
                //await UpdateKnowledgeStatusEnum(knowledgeFile.KnowledgeGUID);
            }
            catch (Exception ex)
            {
                knowledgeFile.IndexStatusEnum = (int)IndexStatusEnum.Fail;
                await _knowledgeFileRepository.UpdateAsync(knowledgeFile);
                return await Failed(ex.Message);
                //await UpdateKnowledgeStatusEnum(knowledgeFile.KnowledgeGUID);
            }
        }

        /// <summary>
        /// 刷新整个知识库
        /// </summary>
        /// <param name="documentFileDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> RefreshKnowledgeTotal(DocumentFileDto documentFileDto)
        {
            if(documentFileDto.KnowledgeFileGUIDs == null || documentFileDto.KnowledgeFileGUIDs.Count == 0)
            {
                return await Succeed("");
            }
            var myContext = mysoftContextFactory.GetMysoftContext();
            Milvus.Client.MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient(myContext.TenantCode);
            await InitEmbeddingService(documentFileDto.knowledgeCode);
            await CreateCollectionAsync(milvusClient, documentFileDto.knowledgeCode);
            //加载collection
            Milvus.Client.MilvusCollection milvusCollection = milvusClient.GetCollection(documentFileDto.knowledgeCode);
            //一次查询所有文件的切片以及文件
            List<KnowledgeFileSectionEntity> knowledgeFileSectionEntities = await _knowledgeFileSectionRepository.GetListAsync(g => documentFileDto.KnowledgeFileGUIDs.Contains(g.KnowledgeFileGUID));
            List<KnowledgeFileEntity> knowledgeFiles = await _knowledgeFileRepository.GetListAsync(g => documentFileDto.KnowledgeFileGUIDs.Contains(g.KnowledgeFileGUID));
            foreach(var knowledgeFile in knowledgeFiles) {
                try
                {
                    List<KnowledgeFileSectionEntity> knowledgeFileSections = knowledgeFileSectionEntities.Where(s => s.KnowledgeFileGUID == knowledgeFile.KnowledgeFileGUID).ToList();
                    foreach (KnowledgeFileSectionEntity knowledgeFileSectionEntity in knowledgeFileSections)
                    {
                        await DeleteEmbeddingAsync(milvusCollection, knowledgeFileSectionEntity.KnowledgeFileSectionGUID.ToString());
                        await InsertAsync(milvusCollection, knowledgeFileSectionEntity.KnowledgeFileSectionGUID.ToString(), knowledgeFileSectionEntity.Content, metadataFileSection);
                    }

                    knowledgeFile.IndexStatusEnum = (int)IndexStatusEnum.Completed;
                }
                catch (Exception ex)
                {
                    knowledgeFile.IndexStatusEnum = (int)IndexStatusEnum.Fail;
                    Console.WriteLine("刷新知识库向量报错：" + ex.Message);
                }
            }
            await _knowledgeFileRepository.UpdateRangeAsync(knowledgeFiles);
            return await Succeed("");
        }

        /// <summary>
        /// 维护知识库的状态
        /// </summary>
        /// <param name="knowledgeGUID"></param>
        /// <returns></returns>
        public async Task UpdateKnowledgeStatusEnum(Guid knowledgeGUID)
        {
            var isComplete = await _knowledgeFileRepository.IsAnyAsync(f => f.KnowledgeGUID == knowledgeGUID && f.IndexStatusEnum != (int)IndexStatusEnum.Completed);
            KnowledgeEntity knowledgeEntity = await _knowledgeRepository.GetFirstAsync(f => f.KnowledgeGUID == knowledgeGUID);
            knowledgeEntity.StatusEnum = isComplete ? (int)IndexStatusEnum.Fail : (int)IndexStatusEnum.Completed;

            await _knowledgeRepository.UpdateAsync(knowledgeEntity);
        }

        private void InitCreateInfo(KnowledgeFileSectionEntity beforeEntity, KnowledgeFileSectionEntity afterEntity)
        {
            afterEntity.CreatedGUID = beforeEntity.CreatedGUID;
            afterEntity.CreatedTime = beforeEntity.CreatedTime;
            afterEntity.CreatedName = beforeEntity.CreatedName;
        }

        private void InitKnowledgeFileSectionEntity(KnowledgeFileSectionEntity knowledgeFileSectionEntity,KnowledgeFileSectionDto knowledgeFileSectionDto)
        {
            knowledgeFileSectionEntity.KnowledgeFileSectionGUID = knowledgeFileSectionDto.KnowledgeFileSectionGUID;
            knowledgeFileSectionEntity.Content = knowledgeFileSectionDto.Content;
            knowledgeFileSectionEntity.KnowledgeFileGUID = knowledgeFileSectionDto.KnowledgeFileGUID;
            knowledgeFileSectionEntity.SectionNumber = knowledgeFileSectionDto.SectionNumber;
            knowledgeFileSectionEntity.SectionSize = knowledgeFileSectionDto.SectionSize;
            knowledgeFileSectionEntity.SectionTokens = String.IsNullOrEmpty(knowledgeFileSectionDto.Content) ? 0 : s_tokenizer.CountTokens(knowledgeFileSectionDto.Content);
            knowledgeFileSectionEntity.Disable = 0;
            knowledgeFileSectionEntity.ParagraphTitle = knowledgeFileSectionDto.ParagraphTitle;
            knowledgeFileSectionEntity.FileSourceEnum = knowledgeFileSectionDto.FileSourceEnum;
            knowledgeFileSectionEntity.ThirdId = knowledgeFileSectionDto.ThirdId;
            knowledgeFileSectionEntity.Disable = knowledgeFileSectionDto.Disable;
        }

        public async Task<ActionResultDto> SaveKnowledgeFileSectionAndQuestion(KnowledgeFileSectionDto knowledgeFileSectionDto)
        {
            //查询是否存在走新增还是修改  
            KnowledgeFileSectionEntity existKnowledgeFileSectionEntity = await _knowledgeFileSectionRepository.GetAsync(s => s.KnowledgeFileSectionGUID == knowledgeFileSectionDto.KnowledgeFileSectionGUID);

            List<KnowledgeQuestionEntity> questionEntitys = await _knowledgeQuestionRepostory.GetListAsync((s =>
                s.KnowledgeFileSectionGUID == knowledgeFileSectionDto.KnowledgeFileSectionGUID));
            KnowledgeFileEntity knowledgeFileEntity = await _knowledgeFileRepository.GetAsync(s => s.KnowledgeFileGUID == knowledgeFileSectionDto.KnowledgeFileGUID);
            KnowledgeEntity knowledgeEntity = await _knowledgeRepository.GetAsync(s => s.KnowledgeGUID == knowledgeFileEntity.KnowledgeGUID);
            String knowledgeCode = knowledgeEntity.Code;
            await InitEmbeddingService(knowledgeCode);

            Milvus.Client.MilvusCollection milvusCollection = InitMilvusCollection(knowledgeCode);
            //片段existKnowledgeFileSectionEntity是否为空
            KnowledgeFileSectionEntity knowledgeFileSectionEntity = new KnowledgeFileSectionEntity();
            InitKnowledgeFileSectionEntity(knowledgeFileSectionEntity, knowledgeFileSectionDto);
            var content = knowledgeFileSectionEntity.Content;
            if (!String.IsNullOrEmpty(knowledgeFileSectionEntity.ParagraphTitle))
            {
                content = knowledgeFileSectionEntity.ParagraphTitle + " " + content +
                          " " + knowledgeFileSectionEntity.ParagraphTitle;
            }
            if (existKnowledgeFileSectionEntity == null)
            {
                
                //新增
                await InsertAsync(milvusCollection, knowledgeFileSectionDto.KnowledgeFileSectionGUID.ToString(), content, metadataFileSection);
                
                await _knowledgeFileSectionRepository.InsertAsync(knowledgeFileSectionEntity);
            }
            else
            {
                InitCreateInfo(existKnowledgeFileSectionEntity, knowledgeFileSectionEntity);
                //修改
                await DeleteEmbeddingAsync(milvusCollection, existKnowledgeFileSectionEntity.KnowledgeFileSectionGUID.ToString());
                string metadataFileSectionDisable = JsonConvert.SerializeObject(new CollectionMetadataDto("1", knowledgeFileSectionDto.Disable.ToString()));
                await InsertAsync(milvusCollection, knowledgeFileSectionDto.KnowledgeFileSectionGUID.ToString(), content, metadataFileSectionDisable);
                await _knowledgeFileSectionRepository.UpdateAsync(knowledgeFileSectionEntity);
            }

            foreach (var knowledgeQuestionEntity in questionEntitys)
            {
                await DeleteEmbeddingAsync(milvusCollection, knowledgeQuestionEntity.KnowledgeFileSectionQuestionGUID.ToString());
                await _knowledgeQuestionRepostory.DeleteAsync(knowledgeQuestionEntity);
            }
            //先保留原始逻辑，用于历史数据处理
            foreach (var knowledgeQuestion in knowledgeFileSectionDto.Questions)
            {
                await InsertAsync(milvusCollection, knowledgeQuestion.KnowledgeFileSectionQuestionGUID.ToString(), knowledgeQuestion.Question, metadataQuestione);
                KnowledgeQuestionEntity knowledgeQuestionEntity = new KnowledgeQuestionEntity();
                knowledgeQuestionEntity.KnowledgeFileSectionQuestionGUID = knowledgeQuestion.KnowledgeFileSectionQuestionGUID;
                knowledgeQuestionEntity.KnowledgeFileSectionGUID = knowledgeQuestion.KnowledgeFileSectionGUID;
                knowledgeQuestionEntity.Question = knowledgeQuestion.Question;
                knowledgeQuestionEntity.Source = KnowledgeQuestionEnum.Custom.ToString();
                await _knowledgeQuestionRepostory.InsertAsync(knowledgeQuestionEntity);
            }
            //新的知识库问题的处理
            if(knowledgeFileSectionDto.newQuestions.Count > 0)
            {
                //兼容编辑场景，先找到已经存在的问题
                List<QuestionDTO> existQuestions = knowledgeFileSectionDto.newQuestions.FindAll(f => f.KnowledgeQuestionGUID.HasValue);
                List<Guid> questionRelationGUIDs = existQuestions.Select(s => s.KnowledgeQuestionRelationGUID).ToList();
                List<Guid> questionGUIDs = existQuestions.Select(s => s.KnowledgeQuestionGUID.Value).ToList();
                List<QuestionEntity> questionEntitiesExist = await _questionRepository.GetListAsync(f=> questionGUIDs.Contains(f.KnowledgeQuestionGUID));
                //判断是否有问题文本做过变更
                for(var i = questionEntitiesExist.Count - 1; i >=0; i--)
                {
                    var questionDto = existQuestions.Find(f=>f.KnowledgeQuestionGUID.Value == questionEntitiesExist[i].KnowledgeQuestionGUID);
                    if(questionDto.Question == questionEntitiesExist[i].Question)
                    {
                        questionEntitiesExist.Remove(questionEntitiesExist[i]);
                    }
                    else
                    {
                        questionEntitiesExist[i].Question = questionDto.Question;
                    }
                }
                List<QuestionDTO> addQuestion = knowledgeFileSectionDto.newQuestions.FindAll(f => f.KnowledgeQuestionGUID == null);
                await _questionRelationRepository.DeleteAsync(d => d.KnowledgeSectionGUID == knowledgeFileSectionDto.KnowledgeFileSectionGUID 
                    && !questionRelationGUIDs.Contains(d.KnowledgeQuestionRelationGUID));
                List<QuestionRelationEntity> questionRelationEntities = new List<QuestionRelationEntity>();
                List<QuestionEntity> questionEntities = new List<QuestionEntity>();
                //组装数据
                foreach (var questionDto in addQuestion){
                    QuestionEntity questionEntity = new QuestionEntity();
                    questionEntity.KnowledgeQuestionGUID = Guid.NewGuid();
                    questionEntity.Question = questionDto.Question;
                    questionEntity.KnowledgeGUID = knowledgeFileEntity.KnowledgeGUID;
                    questionEntities.Add(questionEntity);

                    QuestionRelationEntity questionRelationEntity = new QuestionRelationEntity();
                    questionRelationEntity.KnowledgeQuestionRelationGUID = questionDto.KnowledgeQuestionRelationGUID;
                    questionRelationEntity.KnowledgeSectionGUID = knowledgeFileSectionDto.KnowledgeFileSectionGUID;
                    questionRelationEntity.KnowledgeQuestionGUID = questionEntity.KnowledgeQuestionGUID;
                    questionRelationEntity.KnowledgeGUID = knowledgeFileEntity.KnowledgeGUID;
                    questionRelationEntity.Disable = existKnowledgeFileSectionEntity == null ? 0 : existKnowledgeFileSectionEntity.Disable;
                    questionRelationEntities.Add(questionRelationEntity);
                }
                //先插入MySQL 再新增向量
                await _questionRepository.InsertRangeAsync(questionEntities);
                await _questionRelationRepository.InsertRangeAsync(questionRelationEntities);
                //如果有问题内容的变更，刷新业务表，刷新向量库
                if(questionEntitiesExist.Count > 0)
                {
                    await _questionRepository.UpdateRangeAsync(questionEntitiesExist);
                    foreach (var questionEntity in questionEntitiesExist)
                    {
                        await DeleteEmbeddingAsync(milvusCollection, questionEntity.KnowledgeQuestionGUID.ToString());
                        await InsertAsync(milvusCollection, questionEntity.KnowledgeQuestionGUID.ToString(), questionEntity.Question, metadataQuestione);
                    }

                }
                foreach (var questionEntity in questionEntities)
                {
                    //新增
                    await InsertAsync(milvusCollection, questionEntity.KnowledgeQuestionGUID.ToString(), questionEntity.Question, metadataQuestione);
                }
            }
            else
            {
                await _questionRelationRepository.DeleteAsync(d => d.KnowledgeSectionGUID == knowledgeFileSectionDto.KnowledgeFileSectionGUID);
            }
           

            return await Succeed();
        }

        public async Task<ActionResultDto> DelKnowledgeFileSection(DocumentFileDto documentFileDto)
        {
            //加载向量化方法
            this._embeddingGenerationService = this._kernel.GetRequiredService<ITextEmbeddingGenerationService>();
            //加载collection
            Milvus.Client.MilvusCollection milvusCollection = InitMilvusCollection(documentFileDto.knowledgeCode);
            foreach (var knowledgeFileSectionGUID in documentFileDto.knowledgeFileSectionGUIDs)
            {
                KnowledgeFileSectionEntity knowledgeFileSectionEntity = await _knowledgeFileSectionRepository.GetAsync(s => s.KnowledgeFileSectionGUID == knowledgeFileSectionGUID);
                List<KnowledgeQuestionEntity> knowledgeQuestionEntitys = await _knowledgeQuestionRepostory.GetListAsync(s =>
                    s.KnowledgeFileSectionGUID == knowledgeFileSectionGUID);
                await DeleteEmbeddingAsync(milvusCollection, knowledgeFileSectionEntity.KnowledgeFileSectionGUID.ToString());
                await DeleteEmbeddingAsync(milvusCollection, knowledgeFileSectionEntity.ParagraphTitle);
                await _knowledgeFileSectionRepository.DeleteAsync(knowledgeFileSectionEntity);
                foreach (var knowledgeQuestionEntity in knowledgeQuestionEntitys)
                {
                    await _knowledgeQuestionRepostory.DeleteAsync(knowledgeQuestionEntity);
                    await DeleteEmbeddingAsync(milvusCollection, knowledgeQuestionEntity.KnowledgeFileSectionQuestionGUID.ToString());
                }
            }
            return await Succeed();
        }


        public async Task<List<KnowledgeFileSectionDto>> GetKnowledgeFileSections(List<Guid> sectionGuids)
        {
            return (await _iKnowledgeDomainService.GetKnowledgeFileSections(sectionGuids)).knowledgeFileSectionDtos;
        }

        /// <summary>
        /// 查询知识库
        /// </summary>
        /// <param name="documentFileDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> SearchKnowledge(KnowledgeSearchDto knowledgeSearchDto)
        {
            List<DocumentSectionDto> memoryQueryResults = new List<DocumentSectionDto>();

            var topSectionList = await _knowledgeDomainService.GetQueryTopResult(knowledgeSearchDto.KnowledgeCode, knowledgeSearchDto.Input, knowledgeSearchDto.TopK, knowledgeSearchDto.MinScore);
            var totalIdList = topSectionList.Select(x => x.id).ToList();
            var sectionEntities = await _knowledgeFileSectionRepository.GetListAsync(x => totalIdList.Contains(x.KnowledgeFileSectionGUID) && x.Disable == 0);
            var fileGUIDs = sectionEntities.Select(s => s.KnowledgeFileGUID).ToList();
            foreach (var sectionEntity in sectionEntities)
            {
                DocumentSectionDto documentSectionDto = new DocumentSectionDto();
                documentSectionDto.KnowledgeFileSectionGUID = sectionEntity.KnowledgeFileSectionGUID.ToString();
                var memoryResult = topSectionList.FirstOrDefault(f => f.id == sectionEntity.KnowledgeFileSectionGUID);
                documentSectionDto.Score = memoryResult == null ? 0 : memoryResult.score * 10;
                memoryQueryResults.Add(documentSectionDto);
            }

            memoryQueryResults = memoryQueryResults.OrderByDescending(o => o.Score).ToList();
            return await Succeed(memoryQueryResults);
        }

        /// <summary>
        /// 生成提示问
        /// </summary>
        /// <param name="knowledgeFileDtos"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> QuestionGenerate(KnowledgeFileGUIDsDto knowledgeFileGUIDDto)
        {
            //1、拿到文件GUID
            List<Guid> knowledgeFileGUIDs = knowledgeFileGUIDDto.knowledgeFileGUIDs;
            //文件集合
            List<KnowledgeFileEntity> knowledgeFileEntities = await _knowledgeFileRepository.GetListAsync(f => knowledgeFileGUIDs.Contains(f.KnowledgeFileGUID));
            KnowledgeDoQueryDto knowledgeDoQueryDto = new KnowledgeDoQueryDto();
            knowledgeDoQueryDto.KnowledgeCode = knowledgeFileGUIDDto.knowledgeCode;
            knowledgeDoQueryDto.KnowledgeNotificationInfo = KnowledgeNotificationConst.KnowledgeDoQuery;
            try
            {
                await InitEmbeddingService(knowledgeFileGUIDDto.knowledgeCode);
                //找到collection
                Milvus.Client.MilvusCollection milvusCollection = InitMilvusCollection(knowledgeFileGUIDDto.knowledgeCode);
                List<KnowledgeQuestionEntity> knowledgeQuestionEntities = new List<KnowledgeQuestionEntity>();

                //切片集合
                List<KnowledgeFileSectionEntity> knowledgeFileSectionEntities = await _knowledgeFileSectionRepository.GetListAsync(f => knowledgeFileGUIDs.Contains(f.KnowledgeFileGUID));
                List<Guid> knowledgeFileSectionGUIDs = knowledgeFileSectionEntities.Select(s => s.KnowledgeFileSectionGUID).ToList();

                List<KnowledgeQuestionEntity> knowledgeQuestionDel = await _knowledgeQuestionRepostory.GetListAsync(f => knowledgeFileSectionGUIDs.Contains(f.KnowledgeFileSectionGUID));

                //TODO 这里看看批量删除的方法
                foreach (var item in knowledgeQuestionDel)
                {
                    await DeleteEmbeddingAsync(milvusCollection, item.KnowledgeFileSectionQuestionGUID.ToString());
                }
                //2、根据切片guid，删除历史问题
                await _knowledgeQuestionRepostory.DeleteAsync(d => knowledgeFileSectionGUIDs.Contains(d.KnowledgeFileSectionGUID));
                //3、拿到知识库信息
                //循环处理
                //4、新加入的业务场景，在向量化前加一个问题生成，返回的是字符串数组
                foreach (var item in knowledgeFileSectionEntities)
                {
                    string content = await ReplaceImager(item.Content);
                    string question = await ExecAIChat("QuestionGeneratePlugin.FixedGenerate.txt", content, this._kernel, modelInstance);
                    List<string> questions = _jsonOutputParsers.StringArrayParse(question);
                    //组装一下数据
                    questions.ForEach(f =>
                    {
                        KnowledgeQuestionEntity knowledgeQuestionEntity = new KnowledgeQuestionEntity();
                        knowledgeQuestionEntity.KnowledgeFileSectionQuestionGUID = Guid.NewGuid();
                        knowledgeQuestionEntity.KnowledgeFileSectionGUID = item.KnowledgeFileSectionGUID;
                        knowledgeQuestionEntity.Question = f;
                        knowledgeQuestionEntity.Source = KnowledgeQuestionEnum.Question.ToString();
                        knowledgeQuestionEntities.Add(knowledgeQuestionEntity);
                    });
                }
                //问题实体入mysql库
                bool isSuccessQuestion = await _knowledgeQuestionRepostory.InsertRangeAsync(knowledgeQuestionEntities);
                //问题实体入向量库
                foreach (var item in knowledgeQuestionEntities)
                {
                    await InsertAsync(milvusCollection, item.KnowledgeFileSectionQuestionGUID.ToString(), item.Question, metadataQuestione);
                }
                foreach (var item in knowledgeFileEntities)
                {
                    item.QuestionGenerateEnum = (int)QuestionGenerateEnum.Success;
                }                //刷新状态
                await _knowledgeFileRepository.UpdateRangeAsync(knowledgeFileEntities);
                //刷新页面
                await knowledgeDoQuery(knowledgeDoQueryDto);
                return await Succeed();
            }
            catch (Exception ex)
            {
                foreach (var item in knowledgeFileEntities)
                {
                    item.QuestionGenerateEnum = (int)QuestionGenerateEnum.Fail;
                }                //刷新状态
                await _knowledgeFileRepository.UpdateRangeAsync(knowledgeFileEntities);
                //刷新页面
                await knowledgeDoQuery(knowledgeDoQueryDto);
                return await Failed(ex.Message);
            }
        }

        /// <summary>
        /// 执行知识库测试
        /// </summary>
        /// <param name="knowledgeExcuteTestDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> KnowledgeExcuteTest(KnowledgeExcuteTestDto knowledgeExcuteTestDto)
        {
            List<KnowledgeRecordDetailDto> knowledgeRecordDetailDtos = new List<KnowledgeRecordDetailDto>();
            try
            {
                var topSectionList = await _knowledgeDomainService.GetQueryTopResult(knowledgeExcuteTestDto.knowledgeCodes, knowledgeExcuteTestDto.query, knowledgeExcuteTestDto.limit, knowledgeExcuteTestDto.minScore);
                var totalIdList = topSectionList.Select(x => x.id).ToList();
                var sectionEntities = await _knowledgeFileSectionRepository.GetListAsync(x => totalIdList.Contains(x.KnowledgeFileSectionGUID) && x.Disable == 0);
                var fileGUIDs = sectionEntities.Select(s => s.KnowledgeFileGUID).ToList();
                //根据切片信息组装数据
                var fileEntities = await _knowledgeFileRepository.GetListAsync(x => fileGUIDs.Contains(x.KnowledgeFileGUID));
                var knowledgeGUIDs = fileEntities.Select(s => s.KnowledgeGUID).ToList();
                var knowledgeEntities = await _knowledgeRepository.GetListAsync(x => knowledgeGUIDs.Contains(x.KnowledgeGUID));
                foreach (var sectionEntity in sectionEntities)
                {
                    var fileEntity = fileEntities.Find(f => f.KnowledgeFileGUID == sectionEntity.KnowledgeFileGUID);
                    if (fileEntity == null)
                    {
                        continue;
                    }
                    var knowledgeEntity = knowledgeEntities.Find(f => f.KnowledgeGUID == fileEntity.KnowledgeGUID);
                    var memoryResult = topSectionList.FirstOrDefault(f => f.id == sectionEntity.KnowledgeFileSectionGUID);
                    KnowledgeRecordDetailDto knowledgeRecordDetailDto = new KnowledgeRecordDetailDto();
                    knowledgeRecordDetailDto.Score = memoryResult == null ? 0 : memoryResult.score * 10;
                    knowledgeRecordDetailDto.SectionNumber = sectionEntity.SectionNumber;
                    knowledgeRecordDetailDto.Content = sectionEntity.Content;
                    knowledgeRecordDetailDto.FileName = $"#分片{sectionEntity.SectionNumber} [{fileEntity.FileName}]";
                    knowledgeRecordDetailDto.KnowledgeName = knowledgeEntity.Name;
                    knowledgeRecordDetailDto.KnowledgeRecordGUID = sectionEntity.KnowledgeFileSectionGUID;
                    knowledgeRecordDetailDto.KnowledgeGUID = knowledgeEntity.KnowledgeGUID;
                    knowledgeRecordDetailDto.KnowledgeFileGUID = fileEntity.KnowledgeFileGUID;
                    knowledgeRecordDetailDto.FileSourceEnum = fileEntity.FileSourceEnum;
                    knowledgeRecordDetailDto.OriginFileName = fileEntity.FileName;
                    knowledgeRecordDetailDto.Title = sectionEntity.ParagraphTitle;

                    knowledgeRecordDetailDtos.Add(knowledgeRecordDetailDto);
                }

                knowledgeRecordDetailDtos = knowledgeRecordDetailDtos.OrderByDescending(s => s.Score).ToList();
                return await Succeed(knowledgeRecordDetailDtos);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return await Failed(ex.Message);
            }
        }

        public async Task<ActionResultDto> CreateCollection(DocumentFileDto documentFileDto)
        {
            var myContext = mysoftContextFactory.GetMysoftContext();
            MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient(myContext.TenantCode);
            await CreateCollectionAsync(milvusClient, documentFileDto.knowledgeCode);
            return await Succeed();
        }

        public async Task<ActionResultDto> RefreshQuestionDisableStatus(QuestionEmbeddingDto questionEmbeddingDto)
        {
            try
            {
                MilvusCollection milvusCollection = GetMilvusCollection(questionEmbeddingDto.KnowledgeCode);
                QueryParameters queryParameters = new QueryParameters();
                queryParameters.OutputFields.Add("*");
                queryParameters.ConsistencyLevel = ConsistencyLevel.Strong;

                if (questionEmbeddingDto.QuestionDTOS.Count(c=>c.Disable == 1) > 0)
                {
                    string expr = "id in " + JsonConvert.SerializeObject(questionEmbeddingDto.QuestionDTOS.Where(w => w.Disable == 1)
                        .Select(s => s.KnowledgeQuestionGUID.Value.ToString()).ToList());
                    IReadOnlyList<FieldData> queryResult = await milvusCollection.QueryAsync(
                        expr, queryParameters);
                    await DisableDataHandler(milvusCollection, queryResult,1);
                }

                if (questionEmbeddingDto.QuestionDTOS.Count(c => c.Disable == 0) > 0)
                {
                    string expr = "id in " + JsonConvert.SerializeObject(questionEmbeddingDto.QuestionDTOS.Where(w => w.Disable == 0)
                        .Select(s => s.KnowledgeQuestionGUID.Value.ToString()).ToList());
                    IReadOnlyList<FieldData> queryResult = await milvusCollection.QueryAsync(
                        expr, queryParameters);
                    await DisableDataHandler(milvusCollection, queryResult, 0);
                }

                return await Succeed();
            }
            catch(Exception ex)
            {
                return await Failed(ex.Message);
            }
        }

        private async Task DisableDataHandler(MilvusCollection milvusCollection,IReadOnlyList<FieldData> queryResult, int disable)
        {
            List<FieldData> newFieldData = new List<FieldData>();
            foreach (var fieldData in queryResult)
            {
                string fieldName = fieldData.FieldName;
                if (Metadata == fieldName)
                {
                    FieldData<string> stringFieldData = (FieldData<string>)fieldData;
                    IReadOnlyList<string> array = stringFieldData.Data;
                    List<string> newValues = new List<string>();
                    foreach (var str in array)
                    {
                        if (str != null)
                        {
                            Dictionary<string, object> dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(str);
                            dictionary["disable"] = disable.ToString();
                            newValues.Add(JsonConvert.SerializeObject(dictionary));
                        }
                        else
                        {
                            newValues.Add("");
                        }
                    }
                    newFieldData.Add(FieldData.CreateJson(Metadata, newValues));
                }
                else
                {
                    newFieldData.Add(fieldData);
                }
            }

            await milvusCollection.UpsertAsync(newFieldData);
        }

        public async Task<ActionResultDto> KnowledgeFileSectionDisable(DocumentFileDto documentFileDto)
        {
            int disable = documentFileDto.Disable.Value;
            Guid? knowledgeFileSectionGUID = documentFileDto.knowledgeFileSectionGUID;
            if (knowledgeFileSectionGUID == null ||
                ((int)IsDisbaleEnum.No != disable && (int)IsDisbaleEnum.Yes != disable))
            {
                return await Failed("非法参数");
            }

            var knowledgeQuestionList = await _knowledgeQuestionRepostory.GetListAsync(x =>
                x.KnowledgeFileSectionGUID == documentFileDto.knowledgeFileSectionGUID);
            List<string> knowledgeQuestionGUIDs = new List<string>();
            if (knowledgeQuestionList != null && knowledgeQuestionList.Count > 0)
            {
                knowledgeQuestionGUIDs = knowledgeQuestionList.Select(s => s.KnowledgeFileSectionQuestionGUID.ToString()).ToList();
            }
            knowledgeQuestionGUIDs.Add(documentFileDto.knowledgeFileSectionGUID.ToString());

            MilvusCollection milvusCollection = GetMilvusCollection(documentFileDto.knowledgeCode);

            string expr = "id in " + JsonConvert.SerializeObject(knowledgeQuestionGUIDs);

            QueryParameters queryParameters = new QueryParameters();
            queryParameters.OutputFields.Add("*");

            IReadOnlyList<FieldData> queryResult = await milvusCollection.QueryAsync(
                expr, queryParameters);

            if (queryResult.Count <= 0)
            {
                return await Succeed();
            }

            List<FieldData> newFieldData = new List<FieldData>();
            foreach (var fieldData in queryResult)
            {
                string fieldName = fieldData.FieldName;
                if (Metadata == fieldName)
                {
                    FieldData<string> stringFieldData = (FieldData<string>)fieldData;
                    IReadOnlyList<string> array = stringFieldData.Data;
                    List<string> newValues = new List<string>();
                    foreach (var str in array)
                    {
                        if (str != null)
                        {
                            Dictionary<string, object> dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(str);
                            dictionary["disable"] = disable.ToString();
                            newValues.Add(JsonConvert.SerializeObject(dictionary));
                        }
                        else
                        {
                            newValues.Add("");
                        }
                    }
                    newFieldData.Add(FieldData.CreateJson(Metadata, newValues));
                }
                else
                {
                    newFieldData.Add(fieldData);
                }
            }

            await milvusCollection.UpsertAsync(newFieldData);

            await _knowledgeFileSectionRepository.UpdateAsync(e => new KnowledgeFileSectionEntity
            {
                Disable = disable
            }, f => f.KnowledgeFileSectionGUID == knowledgeFileSectionGUID);

            await _questionRelationRepository.UpdateAsync(e => new QuestionRelationEntity
            {
                Disable = disable
            }, f => f.KnowledgeSectionGUID == knowledgeFileSectionGUID);

            return await Succeed();
        }

        public async Task<ActionResultDto> KnowledgeFileSectionDisableALL(DocumentFileDto documentFileDto)
        {
            foreach (var knowledgeFileSectionGUID in documentFileDto.knowledgeFileSectionGUIDs)
            {
                documentFileDto.knowledgeFileSectionGUID = knowledgeFileSectionGUID;
                await KnowledgeFileSectionDisable(documentFileDto);
            }
            return await Succeed();
        }
        public async Task<ActionResultDto> SaveKnowledgeFileSectionQuestion(KnowledgeQuestionDto knowledgeQuestionDto)
        {
            MilvusCollection milvusCollection = GetMilvusCollection(knowledgeQuestionDto.KnowledgeCode);
            KnowledgeQuestionEntity knowledgeQuestionEntity = new KnowledgeQuestionEntity();
            knowledgeQuestionEntity.KnowledgeFileSectionQuestionGUID = Guid.NewGuid();
            knowledgeQuestionEntity.KnowledgeFileSectionGUID = knowledgeQuestionDto.KnowledgeFileSectionGUID;
            knowledgeQuestionEntity.Question = knowledgeQuestionDto.Question;
            knowledgeQuestionEntity.Source = KnowledgeQuestionEnum.Custom.ToString();
            await _knowledgeQuestionRepostory.InsertAsync(knowledgeQuestionEntity);
            await InsertAsync(milvusCollection, knowledgeQuestionEntity.KnowledgeFileSectionQuestionGUID.ToString(), knowledgeQuestionEntity.Question, metadataQuestione);
            return await Succeed();
        }

        public async Task<ActionResultDto> DeleteKnowledgeFileSectionQuestion(KnowledgeQuestionDto knowledgeQuestionDto)
        {
            Milvus.Client.MilvusCollection milvusCollection = InitMilvusCollection(knowledgeQuestionDto.KnowledgeCode);
            await DeleteEmbeddingAsync(milvusCollection, knowledgeQuestionDto.KnowledgeFileSectionQuestionGUID.ToString());
            await _knowledgeQuestionRepostory.DeleteAsync(x => x.KnowledgeFileSectionQuestionGUID == knowledgeQuestionDto.KnowledgeFileSectionQuestionGUID);
            return await Succeed();
        }

        private MilvusCollection GetMilvusCollection(string knowledgeCode)
        {
            var myContext = mysoftContextFactory.GetMysoftContext();
            MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient(myContext.TenantCode);
            return milvusClient.GetCollection(knowledgeCode);
        }

        public async Task<List<KnowledgeFileSectionEntity>> SaveTextPartitioningDto(MilvusCollection milvusCollection, List<TextPartitioningDto> textPartitioningDtoList)
        {
            List<KnowledgeFileSectionEntity> knowledgeFileSectionEntityList = new List<KnowledgeFileSectionEntity>();
            if (textPartitioningDtoList == null ||
                textPartitioningDtoList.Count <= 0)
            {
                return await Task.FromResult(knowledgeFileSectionEntityList);
            }

            // List<KnowledgeQuestionEntity> knowledgeQuestionEntities = new List<KnowledgeQuestionEntity>();
            foreach (var textPartitioningDto in textPartitioningDtoList)
            {
                foreach (var knowledgeFileSections in textPartitioningDto.KnowledgeFileSections)
                {
                    //为了兼容建模前端连续多个空格只会展示一个空格的问题，这里处理一下
                    knowledgeFileSections.ParagraphTitle = Regex.Replace(textPartitioningDto.ParagraphTitle, @"\s+", " ");
                    knowledgeFileSectionEntityList.Add(knowledgeFileSections);

                    // if (!String.IsNullOrEmpty(textPartitioningDto.ParagraphTitle))
                    // {
                    //     KnowledgeQuestionEntity knowledgeQuestionEntity = new KnowledgeQuestionEntity();
                    //     knowledgeQuestionEntity.KnowledgeFileSectionQuestionGUID = Guid.NewGuid();
                    //     knowledgeQuestionEntity.KnowledgeFileSectionGUID = knowledgeFileSections.KnowledgeFileSectionGUID;
                    //     knowledgeQuestionEntity.Question = textPartitioningDto.ParagraphTitle;
                    //     knowledgeQuestionEntity.Source = KnowledgeQuestionEnum.ParagraphTitle.ToString();
                    //     knowledgeQuestionEntities.Add(knowledgeQuestionEntity);
                    // }

                }
            }

            await _knowledgeFileSectionRepository.InsertRangeAsync(knowledgeFileSectionEntityList);
            //await _knowledgeQuestionRepostory.InsertRangeAsync(knowledgeQuestionEntities);
            //5、切片之后插入向量表
            foreach (var item in knowledgeFileSectionEntityList)
            {
                await InsertAsync(milvusCollection, item.KnowledgeFileSectionGUID.ToString(),item.ParagraphTitle + " " + item.Content + " " + item.ParagraphTitle, metadataFileSection);
            }
            //问题实体入向量库
            //foreach (var item in knowledgeQuestionEntities)
            //{
            //    await InsertAsync(milvusCollection, item.KnowledgeFileSectionQuestionGUID.ToString(), item.Question, metadataQuestione);
            //}
            return await Task.FromResult(knowledgeFileSectionEntityList);
        }

        /// <summary>
        /// 初始化EmbeddingService
        /// </summary>
        /// <returns></returns>
        private async Task InitEmbeddingService(string knowledgeCode)
        {
            //准备数据
            //加载知识库对应的向量模型编号，如果没有配置返回null，走默认模型
            string serviceKey = await _iKnowledgeDomainService.GetEmbeddingModelCodeByKnowledgeCode(knowledgeCode);
            _logger.LogInformation($"向量模型为：{(serviceKey == null ? "默认" : serviceKey )}");
            //加载向量化方法
            this._embeddingGenerationService = this._kernel.GetRequiredService<ITextEmbeddingGenerationService>(serviceKey);
            await Task.CompletedTask;
        }

        /// <summary>
        /// 执行评测任务
        /// </summary>
        /// <param name="knowledgeEvaluatingTaskDto"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> KnowledgeEvaluatingTaskExcute(KnowledgeEvaluatingTaskDto knowledgeEvaluatingTaskDto)
        {
            try
            {
                KnowledgeDoQueryDto knowledgeDoQueryDto = new KnowledgeDoQueryDto();
                knowledgeDoQueryDto.KnowledgeNotificationInfo = KnowledgeNotificationConst.EvaluatingTaskDoQuery;
                //做一下校验
                Verify.NotNull(knowledgeEvaluatingTaskDto.EvaluatingTaskDetailGUID);
                var taskDetail = await _knowledgeTaskDetailRepostory.GetAsync(f => f.KnowledgeTaskDetailGUID == knowledgeEvaluatingTaskDto.EvaluatingTaskDetailGUID);
                if (taskDetail == null)
                {
                    throw new Exception("该任务明细不存在");
                }
                var task = await _knowledgeTaskRepostory.GetAsync(f => f.KnowledgeEvaluatingTaskGUID == taskDetail.KnowledgeEvaluatingTaskGUID);
                if (task == null)
                {
                    throw new Exception("该任务不存在");
                }
                var knowledge = await _knowledgeRepository.GetAsync(f => f.KnowledgeGUID == task.KnowledgeGUID);
                if (knowledge == null)
                {
                    throw new Exception("该知识库不存在");
                }
                //根据知识库code初始化EmbeddingService
                await InitEmbeddingService(knowledge.Code);
                //查询所有的关键词
                var keyWordList = await _knowledgeTaskKeyWordRepostory.GetListAsync(r => r.KnowledgeTaskDetailGUID == taskDetail.KnowledgeTaskDetailGUID);
                //清空上次的关键词匹配记录
                keyWordList.ForEach(f =>
                {
                    f.CompareResult = "";
                });
                //清空之前的记录
                await _knowledgeTaskRecordRepostory.DeleteAsync(d => d.KnowledgeTaskDetailGUID == knowledgeEvaluatingTaskDto.EvaluatingTaskDetailGUID);
                //查询切片guid
                var queryResultList = await _iKnowledgeDomainService.GetQueryTopResult(knowledge.Code, taskDetail.Question, taskDetail.TopK, taskDetail.MinScore/10);
                var sectionGUIDList = queryResultList.Select(s => s.id).ToList();
                //拿到切片信息，跟关键词做比较
                var sectionList = await _knowledgeFileSectionRepository.GetListAsync(f => sectionGUIDList.Contains(f.KnowledgeFileSectionGUID) && f.Disable == 0);
                //查询之后，重新排个序
                sectionList = sectionList.OrderBy(o => sectionGUIDList.IndexOf(o.KnowledgeFileSectionGUID)).ToList();
                for (var i = 0; i < sectionList.Count; i++)
                {
                    foreach (var keyWordEntity in keyWordList)
                    {
                        if (sectionList[i].Content.Contains(keyWordEntity.KeyWord.Trim()))
                        {
                            if (i < 3)
                            {
                                keyWordEntity.CompareResult +=  $" TOP{i + 1}";
                            }
                            keyWordEntity.CompareResult += $" (#分片{sectionList[i].SectionNumber})";
                        }
                    }
                }
                keyWordList.ForEach(f =>
                {
                    f.CompareResult = f.CompareResult == "" ? "无" : f.CompareResult;
                });
                var fileGUIDList = sectionList.Select(s => s.KnowledgeFileGUID);
                var fileList = await _knowledgeFileRepository.GetListAsync(f => fileGUIDList.Contains(f.KnowledgeFileGUID));
                var recordEntities = SectionListToRecordList(sectionList,fileList,queryResultList,knowledge.Name, knowledgeEvaluatingTaskDto.EvaluatingTaskDetailGUID);
                if(keyWordList.Count != 0)
                {
                    taskDetail.IsConfirm = 1;
                    taskDetail.EvaluatingResultEnum = CalculateEvaluatingResult(keyWordList);
                }
                else
                {
                    taskDetail.IsConfirm = 0;
                    taskDetail.EvaluatingResultEnum = (int)EvaluatingResultEnum.None;
                }
                await _knowledgeTaskKeyWordRepostory.UpdateManyAsync(keyWordList);
                await _knowledgeTaskRecordRepostory.InsertRangeAsync(recordEntities);
                taskDetail.StatusEnum = (int)IndexStatusEnum.Completed;
                await _knowledgeTaskDetailRepostory.UpdateAsync(taskDetail);
                //同步一下任务主表的数据，如果执行完成，那么刷新主表数据
                int uncompleteCount = (await _knowledgeTaskDetailRepostory.GetListAsync(f => f.StatusEnum == (int)IndexStatusEnum.Running 
                && f.KnowledgeEvaluatingTaskGUID == taskDetail.KnowledgeEvaluatingTaskGUID)).Count;
                if(uncompleteCount == 0 && task.EvaluatingStatusEnum == (int)EvaluatingStatusEnum.Running)
                {
                    task.EvaluatingStatusEnum = (int)EvaluatingStatusEnum.Confirming;
                    await _knowledgeTaskRepostory.UpdateAsync(task);
                    //发送消息刷新前端页面
                    await knowledgeDoQuery(knowledgeDoQueryDto);
                }
                return await Succeed();
            }
            catch (Exception ex)
            {
                return await Failed(ex.Message);
            }
        }

        /// <summary>
        /// 计算综合评价
        /// </summary>
        /// <returns></returns>
        private int CalculateEvaluatingResult(List<KnowledgeEvaluatingKeyWordEntity> keyWordList)
        {
            int topHitCount = keyWordList.Count(s => s.CompareResult.Contains("TOP"));
            double pert = topHitCount * 1.0 / keyWordList.Count;
            if(pert >= 0.8)
            {
                return (int)EvaluatingResultEnum.Good;
            }
            else if (pert >= 0.5)
            {
                return (int)EvaluatingResultEnum.Normal;
            }
            else
            {
                return (int)EvaluatingResultEnum.Bad;
            }
        }

        //组装评测记录表数据
        private List<KnowledgeEvaluatingTaskRecordEntity> SectionListToRecordList(List<KnowledgeFileSectionEntity> sectionList,List<KnowledgeFileEntity> fileList,List<QueryResultDto> queryResultDtos,string knowledgeName,Guid taskDetailGUID)
        {
            List<KnowledgeEvaluatingTaskRecordEntity> taskRecordEntities = new List<KnowledgeEvaluatingTaskRecordEntity>();
            sectionList.ForEach(f => {
                KnowledgeEvaluatingTaskRecordEntity recordEntity = new KnowledgeEvaluatingTaskRecordEntity();
                recordEntity.Content = f.Content;
                var file = fileList.Find(i => i.KnowledgeFileGUID == f.KnowledgeFileGUID);
                if(file != null)
                {
                    recordEntity.FileName = $"#分片{f.SectionNumber} [{file.FileName}]";
                    recordEntity.OriginFileName = file.FileName;
                    recordEntity.KnowledgeFileGUID = file.KnowledgeFileGUID;
                }
                recordEntity.KnowledgeName = knowledgeName;
                recordEntity.KnowledgeTaskRecordGUID = Guid.NewGuid();
                recordEntity.KnowledgeSectionGUID = f.KnowledgeFileSectionGUID;
                recordEntity.KnowledgeTaskDetailGUID = taskDetailGUID;
                var queryResultDto = queryResultDtos.Find(q => q.id == f.KnowledgeFileSectionGUID);
                if(queryResultDto != null) {
                    recordEntity.Score = queryResultDto.score * 10;
                }
                recordEntity.SectionNumber = f.SectionNumber;
                recordEntity.Icon = f.SectionNumber.ToString();
                recordEntity.Title = f.ParagraphTitle;
                taskRecordEntities.Add(recordEntity);
            });
            return taskRecordEntities;
        }

        /// <summary>
        /// 保存单条数据到向量库
        /// </summary>
        /// <param name="embeddingContent"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> SaveEmbeddingContent(EmbeddingContentDto embeddingContent)
        {
            try
            {
                //根据知识库code初始化EmbeddingService
                await InitEmbeddingService(embeddingContent.KnowledgeCode);
                MilvusCollection milvusCollection = GetMilvusCollection(embeddingContent.KnowledgeCode);
                //先删除历史数据
                await DeleteEmbeddingAsync(milvusCollection, embeddingContent.Id);
                //查询知识库问题
                QuestionEntity questionEntity = await _questionRepository.FindAsync(f=>f.KnowledgeQuestionGUID == new Guid(embeddingContent.Id));
                if(questionEntity != null)
                {
                    List<QuestionRelationEntity> relationEntities = await _questionRelationRepository.GetListAsync(f=>f.KnowledgeQuestionGUID == questionEntity.KnowledgeQuestionGUID);
                    int disableCount = relationEntities.Where(w => w.Disable == 1).Count();
                    if (questionEntity.Disable == 1 && (relationEntities.Count == 0 || disableCount < relationEntities.Count))
                    {
                        questionEntity.Disable = 0;
                    }
                    if(questionEntity.Disable == 0 && relationEntities.Count > 0 && relationEntities.Count == disableCount)
                    {
                        questionEntity.Disable = 1;
                    }
                    CollectionMetadataDto collectionMetadataDto = new CollectionMetadataDto("2", questionEntity.Disable.ToString());
                    await InsertAsync(milvusCollection, embeddingContent.Id, embeddingContent.Content, JsonConvert.SerializeObject(collectionMetadataDto));
                }

                return await Succeed(questionEntity.Disable);
            }
            catch(Exception ex)
            {
                return await Failed(ex.Message);
            }
        }

        public async Task<ActionResultDto> DeleteEmbeddingContent(EmbeddingContentDto embeddingContent)
        {
            try
            {
                //根据知识库code初始化EmbeddingService
                await InitEmbeddingService(embeddingContent.KnowledgeCode);
                MilvusCollection milvusCollection = GetMilvusCollection(embeddingContent.KnowledgeCode);
                foreach (string id in embeddingContent.DeleteIds)
                {
                    await DeleteEmbeddingAsync(milvusCollection, id);
                }
                return await Succeed();
            }
            catch (Exception ex)
            {
                return await Failed(ex.Message);
            }
        }
        
        public async Task<ActionResultDto> TestEmbeddingDatabase()
        {
            try
            {
                var myContext = mysoftContextFactory.GetMysoftContext();
                MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient();
                var version = await milvusClient.GetVersionAsync();
                return await Succeed(version);
            }
            catch(Exception ex)
            {
                return await Failed(ex.Message);
            }
        }
        
        public async Task<ActionResultDto> TextEmbeddings(KnowledgeEmbeddingsParamDto knowledgeEmbeddingsParamDto)
        {
            try
            {
                var data = await _knowledgeDomainService.GetEmbeddingsText(
                    knowledgeEmbeddingsParamDto.ModelInstanceCode, knowledgeEmbeddingsParamDto.Input);
                return await Succeed(data);
            }
            catch(Exception ex){
               return await Failed(ex.Message);
            }
        }

    }

    public class CollectionMetadataDto
    {
        [JsonPropertyName("sourceTypeEnum")]
        public string sourceTypeEnum { get; set; }

        [JsonPropertyName("disable")]
        public string disable { get; set; } = ((int)IsDisbaleEnum.No).ToString();

        public CollectionMetadataDto(string sourceTypeEnum)
        {
            this.sourceTypeEnum = sourceTypeEnum;
        }

        public CollectionMetadataDto(string sourceTypeEnum,string disable)
        {
            this.sourceTypeEnum = sourceTypeEnum;
            this.disable = disable;
        }
    }
}
