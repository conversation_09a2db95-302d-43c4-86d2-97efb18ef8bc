using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Primitives;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared.Extensions;

namespace Mysoft.GPTEngine.Application
{
    public class PromptAppService : AppServiceBase
    {
        private readonly Kernel _kernel;
        private readonly PromptDomainService _promptDomainService;
        private readonly PromptTestSceneRepostory _promptTestSceneRepostory;
        private readonly IServiceProvider _serviceProvider;
        public PromptAppService(IServiceProvider serviceProvider, IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, IMapper mapper
            , Kernel kernel
            , PromptTestSceneRepostory promptTestSceneRepostory
            , PromptDomainService PromptDomainService
            , ILogger<PromptAppService> logger)
            : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            _kernel = kernel;
            _promptDomainService = PromptDomainService;
            _promptTestSceneRepostory = promptTestSceneRepostory;
            _serviceProvider = serviceProvider;
        }
        /// <summary>
        /// 测试提示词场景
        /// </summary>
        /// <param name="promptTestSceneGUID"></param>
        /// <returns></returns>
        public async Task TestPromptScene(Guid promptTestSceneGUID, CancellationToken cancellationToken)
        {
            var promptTestScene = await _promptTestSceneRepostory.GetAsync(x => x.PromptTestSceneGUID == promptTestSceneGUID);
            Verify.NotNull(promptTestSceneGUID);

            // 验证输入参数不为null
            Verify.NotNull(promptTestSceneGUID);
            // 数据准备
            var promptDto = await _promptDomainService.TestPromptSceneProcess(promptTestScene);
            var chatRunDto = new ChatRunDto();
            if (!string.IsNullOrEmpty(promptDto.ModelInstanceCode)) chatRunDto.ModelInstance = new ModelInstanceDto(){InstanceCode = promptDto.ModelInstanceCode};
            _httpContextAccessor.AddItem(nameof(ChatRunDto), chatRunDto);
            // 获取依赖的Kernel实例
            _kernel.Plugins.AddFromType<PromptPlugin>(nameof(PromptPlugin), _serviceProvider);
            var function = _kernel.Plugins.GetFunction(nameof(PromptPlugin), nameof(PromptPlugin.TestScene));
            // 输出结果
            promptTestScene.OutputContent = (await _kernel.InvokeAsync(function, new KernelArguments { { "prompt", promptDto } }, cancellationToken)).GetValue<string>();
            // 评估结果

            var estimateFunction = _kernel.Plugins.GetFunction(nameof(PromptPlugin), nameof(PromptPlugin.Estimate));
            var estimate = (await _kernel.InvokeAsync(estimateFunction, new KernelArguments {
                { "expectContent", promptTestScene.ExpectContent },
                { "outputContent", promptTestScene.OutputContent },
                { "promptTemplate", promptDto.PromptTemplate },
                { "serviceId", promptDto.ModelInstanceCode },
                { "executionSetting", promptDto.ExecutionSetting },
            }, cancellationToken)).GetValue<EstimateDto>();

            promptTestScene.EstimateContent = estimate?.Comments ?? string.Empty;
            promptTestScene.EstimateRecord = estimate?.CombinedScore ?? decimal.Zero;

            await _promptTestSceneRepostory.UpdateAsync(promptTestScene);

        }


    }
}
