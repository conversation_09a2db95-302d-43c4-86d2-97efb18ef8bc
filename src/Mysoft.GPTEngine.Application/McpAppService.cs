using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Application
{
    /// <summary>
    /// MCP应用服务
    /// </summary>
    public class McpAppService : AppServiceBase
    {
        private readonly McpCustomService _mcpCustomService;
        private readonly McpServiceRepository _mcpServiceRepository;
        private readonly McpServiceToolRepository _mcpServiceToolRepository;

        public McpAppService(
            IHttpContextAccessor httpContextAccessor,
            IMysoftContextFactory mysoftContextFactory,
            IMapper mapper,
            ILogger<McpAppService> logger,
            McpCustomService mcpCustomService,
            McpServiceRepository mcpServiceRepository,
            McpServiceToolRepository mcpServiceToolRepository)
            : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            _mcpCustomService = mcpCustomService;
            _mcpServiceRepository = mcpServiceRepository;
            _mcpServiceToolRepository = mcpServiceToolRepository;
        }

        /// <summary>
        /// 通过服务GUID获取MCP工具列表
        /// </summary>
        /// <param name="serviceGuid">MCP服务GUID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>工具列表</returns>
        public async Task<ActionResultDto<List<McpToolInfo>>> GetMcpToolsByServiceGuidAsync(
            string serviceGuid,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("[McpAppService] 通过ServiceGUID获取MCP工具列表: {serviceGuid}", serviceGuid);

                if (string.IsNullOrWhiteSpace(serviceGuid))
                {
                    _logger.LogWarning("[McpAppService] ServiceGUID不能为空");
                    return await Failed("ServiceGUID不能为空") as ActionResultDto<List<McpToolInfo>>;
                }

                // 查询MCP服务信息
                var mcpService = await _mcpServiceRepository.GetFirstAsync(x => x.ServiceGUID == serviceGuid && x.Status == 1);
                if (mcpService == null)
                {
                    _logger.LogWarning("[McpAppService] 未找到ServiceGUID对应的MCP服务: {serviceGuid}", serviceGuid);
                    return await Failed("未找到对应的MCP服务") as ActionResultDto<List<McpToolInfo>>;
                }

                _logger.LogInformation("[McpAppService] 找到MCP服务: {serviceName}, URL: {serviceUrl}",
                    mcpService.ServiceName, mcpService.ServiceURL);

                // 设置自定义请求头
                if (!string.IsNullOrEmpty(mcpService.CustomHeaders))
                {
                    var customHeaders = ParseCustomHeaders(mcpService.CustomHeaders);
                    _mcpCustomService.SetCustomHeaders(customHeaders);
                }

                // 连接到MCP服务器
                var connected = await _mcpCustomService.ConnectAsync(mcpService.ServiceURL, cancellationToken);
                if (!connected)
                {
                    _logger.LogWarning("[McpAppService] 连接MCP服务器失败: {serviceUrl}", mcpService.ServiceURL);
                    return await Failed("连接MCP服务器失败") as ActionResultDto<List<McpToolInfo>>;
                }

                // 获取MCP服务器的工具列表
                var mcpTools = await _mcpCustomService.GetAllToolsAsync(cancellationToken);
                _logger.LogInformation("[McpAppService] 从MCP服务器获取到 {count} 个工具", mcpTools.Count);

                // 获取数据库中的工具信息
                var dbTools = await _mcpServiceToolRepository.GetListAsync(x => x.ServiceGUID == serviceGuid && x.Status == 1);
                _logger.LogInformation("[McpAppService] 从数据库获取到 {count} 个工具记录", dbTools.Count);

                // 合并MCP服务器工具和数据库工具信息
                var mergedTools = new List<McpToolInfo>();

                foreach (var mcpTool in mcpTools)
                {
                    // 查找对应的数据库记录
                    var dbTool = dbTools.FirstOrDefault(x => x.ToolName == mcpTool.Name);

                    var toolInfo = new McpToolInfo
                    {
                        // 来自MCP服务器的信息
                        Name = mcpTool.Name,
                        Description = mcpTool.Description,
                        InputSchema = mcpTool.InputSchema,
                        Parameters = mcpTool.Parameters,

                        // 来自数据库的信息
                        ToolGUID = dbTool?.ToolGUID,
                        Title = dbTool?.ToolTitle ?? mcpTool.Name,
                        ServiceGUID = serviceGuid,
                        OutputSchema = dbTool?.OutputSchema,
                        LastTestInput = dbTool?.LastTestInput,
                        LastTestOutput = dbTool?.LastTestOutput,
                        Status = dbTool?.Status ?? 1
                    };

                    mergedTools.Add(toolInfo);

                    _logger.LogDebug("[McpAppService] 合并工具信息: {toolName}, ToolGUID: {toolGuid}",
                        mcpTool.Name, dbTool?.ToolGUID ?? "未找到");
                }

                _logger.LogInformation("[McpAppService] 成功合并 {count} 个MCP工具信息", mergedTools.Count);
                return await Succeed(mergedTools);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpAppService] 通过ServiceGUID获取MCP工具列表失败: {serviceGuid}", serviceGuid);
                return await Failed($"获取MCP工具列表失败: {ex.Message}") as ActionResultDto<List<McpToolInfo>>;
            }
        }

        /// <summary>
        /// 获取MCP服务器的所有工具列表
        /// </summary>
        /// <param name="mcpServerUrl">MCP服务器URL</param>
        /// <param name="headers">自定义请求头</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>工具列表</returns>
        public async Task<ActionResultDto<List<McpToolInfo>>> GetMcpToolsAsync(
            string mcpServerUrl, 
            Dictionary<string, string> headers = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("[McpAppService] 获取MCP工具列表，服务器: {serverUrl}", mcpServerUrl);

                if (string.IsNullOrWhiteSpace(mcpServerUrl))
                {
                    return await Succeed<List<McpToolInfo>>(new List<McpToolInfo>());
                }

                // 设置自定义请求头
                if (headers?.Count > 0)
                {
                    _mcpCustomService.SetCustomHeaders(headers);
                }

                // 连接到MCP服务器
                var connected = await _mcpCustomService.ConnectAsync(mcpServerUrl, cancellationToken);
                if (!connected)
                {
                    _logger.LogWarning("[McpAppService] 连接MCP服务器失败: {serverUrl}", mcpServerUrl);
                    return await Failed("连接MCP服务器失败") as ActionResultDto<List<McpToolInfo>>;
                }

                // 获取工具列表
                var tools = await _mcpCustomService.GetAllToolsAsync(cancellationToken);
                
                _logger.LogInformation("[McpAppService] 成功获取 {count} 个MCP工具", tools.Count);
                return await Succeed(tools);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpAppService] 获取MCP工具列表失败");
                return await Failed($"获取MCP工具列表失败: {ex.Message}") as ActionResultDto<List<McpToolInfo>>;
            }
        }

        /// <summary>
        /// 通过服务GUID执行MCP工具
        /// </summary>
        /// <param name="request">执行请求</param>
        /// <param name="serviceGuid">MCP服务GUID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public async Task<ActionResultDto<McpExecuteResponse>> ExecuteMcpToolByServiceGuidAsync(
            McpExecuteRequest request,
            string serviceGuid,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var toolIdentifier = !string.IsNullOrWhiteSpace(request.ToolGUID) ? $"ToolGUID: {request.ToolGUID}" : $"ToolName: {request.ToolName}";
                _logger.LogInformation("[McpAppService] 通过ServiceGUID执行MCP工具: {toolIdentifier}, ServiceGUID: {serviceGuid}",
                    toolIdentifier, serviceGuid);

                if (string.IsNullOrWhiteSpace(serviceGuid))
                {
                    var errorResponse = new McpExecuteResponse
                    {
                        Success = false,
                        ErrorMessage = "ServiceGUID不能为空",
                        ToolName = request.ToolName ?? request.ToolGUID
                    };
                    return await Succeed(errorResponse);
                }

                // 如果提供了ToolGUID，需要从数据库查找对应的ToolName
                string actualToolName = request.ToolName;
                if (!string.IsNullOrWhiteSpace(request.ToolGUID))
                {
                    var dbTool = await _mcpServiceToolRepository.GetFirstAsync(x => x.ToolGUID == request.ToolGUID && x.Status == 1);
                    if (dbTool == null)
                    {
                        _logger.LogWarning("[McpAppService] 未找到ToolGUID对应的工具: {toolGuid}", request.ToolGUID);
                        var errorResponse = new McpExecuteResponse
                        {
                            Success = false,
                            ErrorMessage = "未找到对应的工具",
                            ToolName = request.ToolGUID
                        };
                        return await Succeed(errorResponse);
                    }

                    actualToolName = dbTool.ToolName;
                    _logger.LogInformation("[McpAppService] 通过ToolGUID找到工具名称: {toolGuid} -> {toolName}",
                        request.ToolGUID, actualToolName);
                }

                if (string.IsNullOrWhiteSpace(actualToolName))
                {
                    var errorResponse = new McpExecuteResponse
                    {
                        Success = false,
                        ErrorMessage = "工具名称不能为空",
                        ToolName = request.ToolGUID ?? request.ToolName
                    };
                    return await Succeed(errorResponse);
                }

                // 查询MCP服务信息
                var mcpService = await _mcpServiceRepository.GetFirstAsync(x => x.ServiceGUID == serviceGuid && x.Status == 1);
                if (mcpService == null)
                {
                    _logger.LogWarning("[McpAppService] 未找到ServiceGUID对应的MCP服务: {serviceGuid}", serviceGuid);
                    var errorResponse = new McpExecuteResponse
                    {
                        Success = false,
                        ErrorMessage = "未找到对应的MCP服务",
                        ToolName = request.ToolName
                    };
                    return await Succeed(errorResponse);
                }

                _logger.LogInformation("[McpAppService] 找到MCP服务: {serviceName}, URL: {serviceUrl}",
                    mcpService.ServiceName, mcpService.ServiceURL);

                // 设置自定义请求头
                if (!string.IsNullOrEmpty(mcpService.CustomHeaders))
                {
                    var customHeaders = ParseCustomHeaders(mcpService.CustomHeaders);
                    _mcpCustomService.SetCustomHeaders(customHeaders);
                }

                // 连接到MCP服务器
                var connected = await _mcpCustomService.ConnectAsync(mcpService.ServiceURL, cancellationToken);
                if (!connected)
                {
                    _logger.LogWarning("[McpAppService] 连接MCP服务器失败: {serviceUrl}", mcpService.ServiceURL);
                    var errorResponse = new McpExecuteResponse
                    {
                        Success = false,
                        ErrorMessage = "连接MCP服务器失败",
                        ToolName = request.ToolName
                    };
                    return await Succeed(errorResponse);
                }

                // 创建实际的执行请求
                var actualRequest = new McpExecuteRequest
                {
                    ToolName = actualToolName,
                    Arguments = request.Arguments,
                    Headers = request.Headers
                };

                // 执行工具
                var result = await _mcpCustomService.ExecuteToolAsync(actualRequest, cancellationToken);

                // 设置返回结果中的工具名称
                result.ToolName = !string.IsNullOrWhiteSpace(request.ToolGUID) ? request.ToolGUID : actualToolName;

                _logger.LogInformation("[McpAppService] MCP工具执行完成: {toolIdentifier}, 成功: {success}, 耗时: {elapsed}ms",
                    toolIdentifier, result.Success, result.ExecutionTimeMs);

                return await Succeed(result);
            }
            catch (Exception ex)
            {
                var toolIdentifier = !string.IsNullOrWhiteSpace(request.ToolGUID) ? $"ToolGUID: {request.ToolGUID}" : $"ToolName: {request.ToolName}";
                _logger.LogError(ex, "[McpAppService] 通过ServiceGUID执行MCP工具失败: {toolIdentifier}, ServiceGUID: {serviceGuid}",
                    toolIdentifier, serviceGuid);

                var errorResponse = new McpExecuteResponse
                {
                    Success = false,
                    ErrorMessage = $"执行MCP工具失败: {ex.Message}",
                    ToolName = request.ToolGUID ?? request.ToolName
                };

                return await Succeed(errorResponse);
            }
        }

        /// <summary>
        /// 执行MCP工具
        /// </summary>
        /// <param name="request">执行请求</param>
        /// <param name="mcpServerUrl">MCP服务器URL</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public async Task<ActionResultDto<McpExecuteResponse>> ExecuteMcpToolAsync(
            McpExecuteRequest request, 
            string mcpServerUrl, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("[McpAppService] 执行MCP工具: {toolName}, 服务器: {serverUrl}", 
                    request.ToolName, mcpServerUrl);

                if (string.IsNullOrWhiteSpace(mcpServerUrl))
                {
                    var errorResponse = new McpExecuteResponse
                    {
                        Success = false,
                        ErrorMessage = "MCP服务器URL不能为空",
                        ToolName = request.ToolName
                    };
                    return await Succeed(errorResponse);
                }

                if (string.IsNullOrWhiteSpace(request.ToolName))
                {
                    var errorResponse = new McpExecuteResponse
                    {
                        Success = false,
                        ErrorMessage = "工具名称不能为空",
                        ToolName = request.ToolName
                    };
                    return await Succeed(errorResponse);
                }

                // 设置自定义请求头
                if (request.Headers?.Count > 0)
                {
                    _mcpCustomService.SetCustomHeaders(request.Headers);
                }

                // 连接到MCP服务器
                var connected = await _mcpCustomService.ConnectAsync(mcpServerUrl, cancellationToken);
                if (!connected)
                {
                    _logger.LogWarning("[McpAppService] 连接MCP服务器失败: {serverUrl}", mcpServerUrl);
                    var errorResponse = new McpExecuteResponse
                    {
                        Success = false,
                        ErrorMessage = "连接MCP服务器失败",
                        ToolName = request.ToolName
                    };
                    return await Succeed(errorResponse);
                }

                // 执行工具
                var result = await _mcpCustomService.ExecuteToolAsync(request, cancellationToken);
                
                _logger.LogInformation("[McpAppService] MCP工具执行完成: {toolName}, 成功: {success}, 耗时: {elapsed}ms", 
                    request.ToolName, result.Success, result.ExecutionTimeMs);
                
                return await Succeed(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpAppService] 执行MCP工具失败: {toolName}", request.ToolName);
                
                var errorResponse = new McpExecuteResponse
                {
                    Success = false,
                    ErrorMessage = $"执行MCP工具失败: {ex.Message}",
                    ToolName = request.ToolName
                };
                
                return await Succeed(errorResponse);
            }
        }

        /// <summary>
        /// 通过服务GUID测试MCP服务器连接
        /// </summary>
        /// <param name="serviceGuid">MCP服务GUID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接测试结果</returns>
        public async Task<ActionResultDto<bool>> TestMcpConnectionByServiceGuidAsync(
            string serviceGuid,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("[McpAppService] 通过ServiceGUID测试MCP服务器连接: {serviceGuid}", serviceGuid);

                if (string.IsNullOrWhiteSpace(serviceGuid))
                {
                    _logger.LogWarning("[McpAppService] ServiceGUID不能为空");
                    return await Succeed(false);
                }

                // 查询MCP服务信息
                var mcpService = await _mcpServiceRepository.GetFirstAsync(x => x.ServiceGUID == serviceGuid && x.Status == 1);
                if (mcpService == null)
                {
                    _logger.LogWarning("[McpAppService] 未找到ServiceGUID对应的MCP服务: {serviceGuid}", serviceGuid);
                    return await Succeed(false);
                }

                _logger.LogInformation("[McpAppService] 找到MCP服务: {serviceName}, URL: {serviceUrl}",
                    mcpService.ServiceName, mcpService.ServiceURL);

                // 设置自定义请求头
                if (!string.IsNullOrEmpty(mcpService.CustomHeaders))
                {
                    var customHeaders = ParseCustomHeaders(mcpService.CustomHeaders);
                    _mcpCustomService.SetCustomHeaders(customHeaders);
                }

                // 测试连接
                var connected = await _mcpCustomService.ConnectAsync(mcpService.ServiceURL, cancellationToken);

                _logger.LogInformation("[McpAppService] MCP服务器连接测试结果: {serviceUrl}, 成功: {success}",
                    mcpService.ServiceURL, connected);

                return await Succeed(connected);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpAppService] 通过ServiceGUID测试MCP服务器连接失败: {serviceGuid}", serviceGuid);
                return await Succeed(false);
            }
        }

        /// <summary>
        /// 测试MCP服务器连接
        /// </summary>
        /// <param name="mcpServerUrl">MCP服务器URL</param>
        /// <param name="headers">自定义请求头</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接测试结果</returns>
        public async Task<ActionResultDto<bool>> TestMcpConnectionAsync(
            string mcpServerUrl, 
            Dictionary<string, string> headers = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("[McpAppService] 测试MCP服务器连接: {serverUrl}", mcpServerUrl);

                if (string.IsNullOrWhiteSpace(mcpServerUrl))
                {
                    return await Succeed(false);
                }

                // 设置自定义请求头
                if (headers?.Count > 0)
                {
                    _mcpCustomService.SetCustomHeaders(headers);
                }

                // 测试连接
                var connected = await _mcpCustomService.ConnectAsync(mcpServerUrl, cancellationToken);
                
                _logger.LogInformation("[McpAppService] MCP服务器连接测试结果: {serverUrl}, 成功: {success}", 
                    mcpServerUrl, connected);
                
                return await Succeed(connected);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpAppService] 测试MCP服务器连接失败: {serverUrl}", mcpServerUrl);
                return await Succeed(false);
            }
        }

        /// <summary>
        /// 解析自定义请求头
        /// 支持两种格式：
        /// 1. JSON格式：{"Content-Type": "application/json", "Authorization": "Bearer token"}
        /// 2. 多行文本格式：
        ///    "Content-Type": "application/json"
        ///    "Authorization": "Bearer your-token"
        /// </summary>
        /// <param name="customHeadersText">自定义请求头文本</param>
        /// <returns>解析后的请求头字典</returns>
        private Dictionary<string, string> ParseCustomHeaders(string customHeadersText)
        {
            var headers = new Dictionary<string, string>();

            if (string.IsNullOrWhiteSpace(customHeadersText))
            {
                return headers;
            }

            // 首先尝试JSON格式解析
            try
            {
                var jsonHeaders = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(customHeadersText);
                if (jsonHeaders != null)
                {
                    _logger.LogInformation("[ParseCustomHeaders] 使用JSON格式解析自定义请求头");
                    return jsonHeaders;
                }
            }
            catch
            {
                // JSON解析失败，尝试多行文本格式
                _logger.LogInformation("[ParseCustomHeaders] JSON格式解析失败，尝试多行文本格式");
            }

            // 多行文本格式解析
            var lines = customHeadersText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (string.IsNullOrEmpty(trimmedLine))
                    continue;

                // 查找冒号分隔符
                var colonIndex = trimmedLine.IndexOf(':');
                if (colonIndex > 0 && colonIndex < trimmedLine.Length - 1)
                {
                    var key = trimmedLine.Substring(0, colonIndex).Trim().Trim('"');
                    var value = trimmedLine.Substring(colonIndex + 1).Trim().Trim('"');

                    if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(value))
                    {
                        headers[key] = value;
                        _logger.LogDebug("[ParseCustomHeaders] 解析请求头: {key} = {value}", key, value);
                    }
                }
            }

            _logger.LogInformation("[ParseCustomHeaders] 使用多行文本格式解析自定义请求头，共解析 {count} 个", headers.Count);
            return headers;
        }
    }
}
