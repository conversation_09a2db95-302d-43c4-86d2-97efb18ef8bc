using System;
using System.Net.Http;
using System.Text;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application
{
    public class AccessTokenService
    {
        private readonly ApplicationRepostory _applicationRepostory;
        private readonly ApplicationReleaseRepostory _applicationReleaseRepostory;
        private readonly ApplicationSiteRepostory _applicationSiteRepostory;
        private readonly ApplicationSecurityRepostory _applicationSecurityRepostory;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private IConfigurationService _configurationService;

        private static int AccessTokenExpireDay = 7;

        private ILogger<AccessTokenService> _logger;

        private HttpClient _httpClient;

        public AccessTokenService(IHttpClientFactory clientFactory, IConfigurationService configurationService, ApplicationRepostory applicationRepostory, ApplicationReleaseRepostory applicationReleaseRepostory, ApplicationSiteRepostory applicationSiteRepostory,
            ApplicationSecurityRepostory applicationSecurityRepostory, ILogger<AccessTokenService> logger, IHttpContextAccessor httpContextAccessor)
        {
            _httpClient = clientFactory.CreateClient("Mysoft_CustomerService");
            _applicationRepostory = applicationRepostory;
            _applicationReleaseRepostory = applicationReleaseRepostory;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _applicationSiteRepostory = applicationSiteRepostory;
            _applicationSecurityRepostory = applicationSecurityRepostory;
            _configurationService = configurationService;
        }


        public async Task<ActionResultDto> GetAccessToken(AccessTokenParams accessTokenParams, CancellationToken cancellationToken)
        {
            MysoftContext mysoftContext = new MysoftContext();
            mysoftContext.TenantCode = accessTokenParams.TenantCode;
            mysoftContext.GptBuilderUrl = _configurationService.GetBuilderUrl();
            var encrypt = AesHelper.EncryptStringToBytes(JsonConvert.SerializeObject(mysoftContext));
            _httpContextAccessor.HttpContext.Request.Headers[MysoftConstant.MysoftContext] =
                Convert.ToBase64String(encrypt);

            var application = await _applicationRepostory.GetSingleAsync(x => x.ApplicationCode == accessTokenParams.AppId)
                .ConfigureAwait(false);

            if (application == null)
            {
                return await Task.FromResult(new ActionResultDto() { Success = false, Message = "应用不存在" });
            }

            var applicationSecurity = await _applicationSecurityRepostory.
                GetSingleAsync(x => x.ApplicationGUID == application.ApplicationGUID && x.AppSecret == accessTokenParams.AppSecret)
                .ConfigureAwait(false);

            if (applicationSecurity == null)
            {
                return await Task.FromResult(new ActionResultDto() { Success = false, Message = "应用密钥不正确" });
            }

            // 获取当前时间
            DateTime now = TimeZoneUtility.LocalNow();
            // 计算7天后的时间
            DateTime futureDate = now.AddDays(AccessTokenExpireDay);
            var expire = ((DateTimeOffset)futureDate).ToUnixTimeSeconds();

            var jwt = "";
            if (String.IsNullOrWhiteSpace(applicationSecurity.UserCheckUrl) || application.EnableUserAuthorization != 1)
            {
                jwt = JwtHelper<AccessTokenContent>.GenerateJwtToken(accessTokenParams.PayLoad, accessTokenParams.AppSecret, expire);
            }
            else
            {
                var body = JsonConvert.SerializeObject(accessTokenParams.PayLoad);
                var httpContent = new StringContent(body, Encoding.UTF8, "application/json");

                using var request = new HttpRequestMessage(HttpMethod.Post, applicationSecurity.UserCheckUrl) { Content = httpContent }; ;
                try
                {
                    using var response = await _httpClient.SendWithSuccessCheckAsync(request, cancellationToken).ConfigureAwait(false);
                    var responseBody = await response.Content.ReadAsStringWithExceptionMappingAsync().ConfigureAwait(false);
                    var result = JsonConvert.DeserializeObject<CheckUserResult>(responseBody);
                    if (!result.Success)
                    {
                        return await Task.FromResult(new ActionResultDto() { Success = false, Message = result.Message });
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "用户验证失败");
                    return await Task.FromResult(new ActionResultDto() { Success = false, Message = "用户验证失败" });
                }

                jwt = JwtHelper<AccessTokenContent>.GenerateJwtToken(accessTokenParams.PayLoad, accessTokenParams.AppSecret, expire);
            }

            return await Task.FromResult(new ActionResultDto<TokenResult>() { Success = true, Data = new TokenResult() { AccessToken = jwt } });
        }


    }


}