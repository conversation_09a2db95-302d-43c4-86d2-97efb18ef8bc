using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Application.Configuration
{
    public static class ImageHandleParam
    {
        public static double Height { get; set; } = 100;
        public static double Width { get; set; } = 100;

        public static double OCRLimitHeight { get; set; } = 300;
        public static double OCRLimitWidth { get; set; } = 300;

        /// <summary>
        /// 文本占位符
        /// </summary>
        public static string ImagePlaceholder { get; set; } = "{{{{image:{0}}}}}";

        /// <summary>
        /// 保存时候的图片名称
        /// </summary>
        public static string ImageName { get; set; } = "图片{0}.png";
    }
}
