using Microsoft.Extensions.Configuration;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class ConfigurationHelper
    {
        public static VectordbInstancesItem GetVectordbInstancesItem(IConfiguration configuration)
        {
            if (configuration[nameof(VectordbUsed)] == null)
            {
                return null;
            }
            VectordbUsed vectordbUsed = JsonConvert.DeserializeObject<VectordbUsed>(configuration[nameof(VectordbUsed)]);
            if (vectordbUsed == null)
            {
                return null;
            }
            string instanceId = vectordbUsed.items.First(f => f.Code == EMCConfigConst.MilvusCode).InstanceId;
            string vectordbInstanceStr = configuration[nameof(VectordbInstances)];
            if (string.IsNullOrEmpty(vectordbInstanceStr))
            {
                return null;
            }
            VectordbInstances vectordbInstances = JsonConvert.DeserializeObject<VectordbInstances>(vectordbInstanceStr);
            return vectordbInstances.items.First(f => f.Id == instanceId);
        }
    }

    #region EMC相关配置类
    public class VectordbUsed
    {
        public List<VectordbUsedItem> items { get; set; }
    }

    public class VectordbUsedItem
    {
        public string Code { get; set; }

        public string InstanceId { get; set; }

        public string DbName { get; set; }
    }

    public class VectordbInstances
    {
        public List<VectordbInstancesItem> items { get; set; }
    }

    public class VectordbInstancesItem
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Host { get; set; }
        public int Port { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string Type { get; set; }
    }
    #endregion
    
}
