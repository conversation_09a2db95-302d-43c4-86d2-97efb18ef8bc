using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class JsonValidateHelper
    {
        public static bool IsValidJson(string jsonString)
        {
            jsonString = jsonString.Trim();
            if (jsonString.StartsWith("{") && jsonString.EndsWith("}"))
            {
                try
                {
                    var jsonObject = JsonConvert.DeserializeObject(jsonString);
                    return jsonObject != null;
                }
                catch (JsonReaderException)
                {
                    return false;
                }
            }
            else if (jsonString.StartsWith("[") && jsonString.EndsWith("]"))
            {
                try
                {
                    var jsonArray = JsonConvert.DeserializeObject(jsonString);
                    return jsonArray != null;
                }
                catch (JsonReaderException)
                {
                    return false;
                }
            }
            return false;
        }
        public static bool IsValidJsonObject(string jsonString)
        {
            jsonString = jsonString.Trim();
            if (jsonString.StartsWith("{") && jsonString.EndsWith("}"))
            {
                try
                {
                    var jsonObject = JsonConvert.DeserializeObject(jsonString);
                    return jsonObject != null;
                }
                catch (JsonReaderException)
                {
                    return false;
                }
            }
            return false;
        }
        public static bool IsValidArrayString(string arrayString)
        {
            try
            {
                List<string> stringList = JsonConvert.DeserializeObject<List<string>>(arrayString);

                if (stringList == null)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public static string ExtractJson(string text)
        {
            text = text.Trim();
            if (text.StartsWith("```json"))
            {
                text = text.Substring(7);
            }

            if (text.EndsWith("```"))
            {
                text = text.Substring(0, text.Length - 3);
            }

            return text;
        }

        public static string CleanUpJsonIdentifiers(string jsonStr)
        {
            // 使用正则表达式提取 JSON 块，支持跨行匹配
            var pattern = @"```(?:json)?\s*({(?:[^{}]|(?<open>{)|(?<-open>}))*})\s*```";
            var match = Regex.Match(jsonStr, pattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);
            if (match.Success)
            {
                return match.Groups[1].Value.Trim();
            }

            // 如果没有找到完整代码块，尝试简单截取
            if (jsonStr.StartsWith("json", StringComparison.OrdinalIgnoreCase))
            {
                jsonStr = jsonStr.Substring("json".Length);
            }
            if (jsonStr.Contains("```"))
            {
                jsonStr = jsonStr.Substring(0, jsonStr.IndexOf("```", StringComparison.Ordinal));
            }

            return jsonStr.Trim();
        }

        public static string CleanUpJson(string jsonStr)
        {
            jsonStr = jsonStr.Replace("{{", "{")
                     .Replace("}}", "}")
                     .Replace("\"[{\\r\\n", "[{")
                     .Replace("}]\"", "}]")
                     .Replace("\\", " ")
                     .Replace("\\n", " ")
                     .Replace("\n", " ")
                     .Replace("\r", "")
                     .Trim();

            jsonStr = CleanUpJsonIdentifiers(jsonStr);

            // 移除可能存在的非法引号包裹
            if (jsonStr.StartsWith("\"") && jsonStr.EndsWith("\""))
            {
                jsonStr = jsonStr.Substring(1, jsonStr.Length - 2);
            }

            // 自动补全大括号和中括号（优先补全数组再补对象）
            int leftBrace = jsonStr.Split('{').Length - 1;
            int rightBrace = jsonStr.Split('}').Length - 1;
            int leftBracket = jsonStr.Split('[').Length - 1;
            int rightBracket = jsonStr.Split(']').Length - 1;

            // 先补全右中括号
            while (rightBracket < leftBracket)
            {
                jsonStr += "]";
                rightBracket++;
            }
            // 再补全右大括号
            while (rightBrace < leftBrace)
            {
                jsonStr += "}";
                rightBrace++;
            }

            return jsonStr;
        }
        
        public static T TryParseJsonObject<T>(string input)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<T>(input);
            }
            catch (Exception)
            {
                Console.WriteLine("Warning: Error decoding faulty json, attempting repair");
            }

            string pattern = @"\{(.*)\}";
            Match match = Regex.Match(input, pattern);
            input = match.Success ? ("{" + match.Groups[1].Value + "}") : input;
            input = CleanUpJson(input);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            Console.WriteLine("Repaired json: " + input);
            return System.Text.Json.JsonSerializer.Deserialize<T>(input, options);
        }
        public static bool IsIntegerWithTrailingDot(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
            {
                return false;
            }

            input = input.Trim();

            // 定义正则表达式模式
            string pattern = @"^\d+\.$";

            // 使用Regex类进行匹配，并返回结果
            return Regex.IsMatch(input, pattern);
        }
    }
}
