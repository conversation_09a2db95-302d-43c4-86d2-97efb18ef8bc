using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Mysoft.GPTEngine.Common.Helper
{
    public class SchemaItem
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public int Selected { get; set; }
        public int Attachment { get; set; } = 0;
        public List<SchemaItem> Children { get; set; }
    }
    
    public class AttachmentInfoDto
    {
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        
        [JsonPropertyName("documentGuid")]
        public string DocumentGuid { get; set; }
        
        [JsonPropertyName("size")]
        public int? Size { get; set; }
    }
    
    public class SchemaData
    {
        public string Data { get; set; }

        public List<AttachmentInfoDto> AttachmentInfoList { get; set; }
    }
    
    public class BeanSchemaHelper
    {
        public static bool IsSimpleType(JToken token)
        {
            switch (token.Type)
            {
                case JTokenType.String:
                case JTokenType.Integer:
                case JTokenType.Float:
                case JTokenType.Boolean:
                case JTokenType.Date:
                case JTokenType.Guid:
                case JTokenType.Uri:
                case JTokenType.TimeSpan:
                    return true;
                default:
                    return false;
            }
        }
        
        public static SchemaData ConvertJsonWithSchema(string jsonData, List<SchemaItem> schema, int source)
        {
            List<AttachmentInfoDto> attachmentInfoList = new List<AttachmentInfoDto>();
            if (string.IsNullOrEmpty(jsonData))
            {
                return new SchemaData()
                {
                    Data = "",
                    AttachmentInfoList = attachmentInfoList
                };
            }
            var data = JToken.Parse(jsonData);
            JToken convertedData;
            if (data.Type == JTokenType.Array)
            {
                convertedData = ConvertArray(schema, data, attachmentInfoList, source);
            }else if (((JObject)data).TryGetValue("body", out var jsonBody))
            {
                if (IsSimpleType(jsonBody))
                {
                    var name = string.IsNullOrEmpty(schema[0].Description) ? schema[0].Name : schema[0].Description;
                    convertedData = new JObject() { [name] = jsonBody };
                }
                else
                {
                    convertedData = ConvertSchema(schema, jsonBody, attachmentInfoList, source);
                }
                
            } else
            {
                convertedData = ConvertSchema(schema, data, attachmentInfoList, source);
            }
            
            return new SchemaData()
            {
                Data = JsonConvert.SerializeObject(convertedData),
                AttachmentInfoList = attachmentInfoList
            };
        }

        private static JToken ConvertSchema(List<SchemaItem> schema, JToken data, List<AttachmentInfoDto> attachmentInfoList, int source)
        {
            var result = new JObject();
            foreach (var item in schema)
            {
                if (item.Selected == 1 && data != null)
                {
                    
                    var key = !string.IsNullOrEmpty(item.Name) ? item.Name : item.Description;
                    var name = !string.IsNullOrEmpty(item.Description) ? item.Description : item.Name;

                    if (TryAddAttachment(attachmentInfoList, item, data[key], source))
                    {
                        continue;
                    }
                    switch (item.Type)
                    {
                        case "array":
                            result.TryAdd(name, ConvertArray(item.Children, data[key], attachmentInfoList, source));
                            break;
                        case "object":
                            result.TryAdd(name, ConvertObject(item.Children, data[key] as JObject, attachmentInfoList, source));
                            break;
                        default:
                            result.TryAdd(name, GetValue(data, key, attachmentInfoList));
                            break;
                    }
                }
            }
            return result;
        }

        private static JToken GetValue(JToken data, string propertyName, List<AttachmentInfoDto> attachmentInfoList)
        {
            if (data[propertyName] != null)
            {
                return data[propertyName];
            }
            return JValue.CreateNull(); // 返回 null 值
        }

        private static JToken ConvertArray(List<SchemaItem> children, JToken token, List<AttachmentInfoDto> attachmentInfoList, int source)
        {
            if (token is JArray array)
            {
                var convertedList = new JArray();
                foreach (var item in array.Children())
                {
                    if (item is JObject obj)
                    {
                        convertedList.Add(ConvertSchema(children, obj, attachmentInfoList, source));
                    }
                    else
                    {
                        convertedList.Add(item);
                    }
                }
                return convertedList;
            }
            return JValue.CreateNull(); // 如果不是数组，返回 null
        }

        private static JToken ConvertObject(List<SchemaItem> children, JObject obj, List<AttachmentInfoDto> attachmentInfoList, int source)
        {
            return ConvertSchema(children, obj, attachmentInfoList, source);
        }

        private static bool TryAddAttachment(List<AttachmentInfoDto> attachmentInfoList, SchemaItem item, JToken obj, int source)
        {
            if (item.Attachment == 1 && obj != null)
            {
                var jsonData = obj.ToString();
                List<AttachmentInfoDto> tempList;
                if (source == 0)
                {
                    tempList = ConvertJsonToFlowAttachment(jsonData);
                }
                else
                {
                    if (item.Type != "string")
                    {
                        jsonData = JsonConvert.SerializeObject(obj);
                    }
                    tempList = ConvertJsonToAttachment(jsonData);
                }

                 
                if (tempList != null)
                {
                    foreach (var attachmentInfoDto in tempList)
                    {
                        var  dto = attachmentInfoList.Find(x => x.DocumentGuid == attachmentInfoDto.DocumentGuid);
                        if (dto == null)
                        {
                            attachmentInfoList.Add(attachmentInfoDto);
                        }
                    }
                    return true;
                }
            }
            return false;
        }
        
        public static List<AttachmentInfoDto> ConvertJsonToAttachment(string jsonData)
        {
            try
            {
                AttachmentInfoDto attachmentInfo = JsonConvert.DeserializeObject<AttachmentInfoDto>(jsonData);
                if (attachmentInfo != null && !string.IsNullOrEmpty(attachmentInfo.DocumentGuid))
                {
                    List<AttachmentInfoDto> attachmentInfoList = new List<AttachmentInfoDto>();
                    attachmentInfoList.Add(attachmentInfo);
                    return attachmentInfoList;
                }
            }
            catch
            { 
                try
                {
                    List<AttachmentInfoDto> attachmentInfoList = JsonConvert.DeserializeObject<List<AttachmentInfoDto>>(jsonData);
                    if (attachmentInfoList != null && attachmentInfoList.Count > 0 && !string.IsNullOrEmpty(attachmentInfoList[0].DocumentGuid))
                    {
                        return attachmentInfoList;
                    }
                }
                catch
                {
                    try
                    {
                        jsonData = jsonData.Replace("\\n", "\n").Replace("\\\"", "\"").Replace("\\\\", "\\");
                        List<AttachmentInfoDto> attachmentInfoList = JsonConvert.DeserializeObject<List<AttachmentInfoDto>>(jsonData);
                        if (attachmentInfoList != null && attachmentInfoList.Count > 0 && !string.IsNullOrEmpty(attachmentInfoList[0].DocumentGuid))
                        {
                            return attachmentInfoList;
                        }
                    }
                    catch
                    {
                        Console.WriteLine("不是附件类型：" + jsonData);
                    }
                }
            }
            return null;
        }
        
        public static List<AttachmentInfoDto> ConvertJsonToFlowAttachment(string jsonData)
        {
            if (string.IsNullOrEmpty(jsonData))
            {
                return null;
            }
            List<AttachmentInfoDto> list = new List<AttachmentInfoDto>();
            string[] stringArray = jsonData.Split(',');
            foreach (string fruit in stringArray)
            {
                AttachmentInfoDto attachmentInfo = new AttachmentInfoDto()
                {
                    DocumentGuid = fruit,
                    Name = ""
                };
                list.Add(attachmentInfo);
            }
        
            return list;
        }

        
    }
}