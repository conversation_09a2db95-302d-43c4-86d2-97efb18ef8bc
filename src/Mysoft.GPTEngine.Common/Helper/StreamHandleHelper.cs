using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class StreamHandleHelper
    {
        public static byte[] StreamToByteArray(Stream stream)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                stream.CopyTo(memoryStream);
                return memoryStream.ToArray();
            }
        }
    }
}
