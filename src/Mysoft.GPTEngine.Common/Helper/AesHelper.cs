using System;  
using System.IO;
using System.Linq;
using System.Security.Cryptography;  
using System.Text;

namespace Mysoft.GPTEngine.Common.Helper
{
    public class AesHelper
    {
        private static readonly byte[] Key = Encoding.UTF8.GetBytes("gZv789uL0Hmf1hHW");  
        private static readonly byte[] Iv = Encoding.UTF8.GetBytes("J2ugwavUgL8pS4sh");  
  
        public static string Decrypt(string encryptedText)  
        {  
            byte[] encryptedBytes = Convert.FromBase64String(encryptedText);  
  
            using (Aes aes = Aes.Create())  
            {  
                aes.Key = Key;  
                aes.IV = Iv;  
                aes.Mode = CipherMode.CBC;  
                aes.Padding = PaddingMode.PKCS7;  
  
                using (MemoryStream ms = new MemoryStream())  
                {  
                    using (CryptoStream cs = new CryptoStream(ms, aes.CreateDecryptor(), CryptoStreamMode.Write))  
                    {  
                        cs.Write(encryptedBytes, 0, encryptedBytes.Length);  
                        cs.Close();  
                    }  
  
                    byte[] decryptedBytes = ms.ToArray();  
                    return Encoding.UTF8.GetString(decryptedBytes);  
                }  
            }  
        } 
        
        public static byte[] EncryptStringToBytes(string plainText)  
        {  
            if (plainText == null || plainText.Length <= 0)  
                throw new ArgumentNullException("plainText");   
            byte[] encrypted;  
  
            using (Aes aesAlg = Aes.Create())  
            {  
                aesAlg.Key = Key;  
                aesAlg.IV = Iv;  
  
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);  
  
                using (MemoryStream msEncrypt = new MemoryStream())  
                {  
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))  
                    {  
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))  
                        {  
                            swEncrypt.Write(plainText);  
                        }  
                        encrypted = msEncrypt.ToArray();  
                    }  
                }  
            }  
  
            return encrypted;  
        } 
        
        
        public static string Encrypt(string text, string password)
        {
            using (var cipher = GetAesCipher(password))
            {
                return Encrypt(text, cipher);
            }
        }

        private static AesManaged GetAesCipher(string password)
        {
            password += "Mysoft!@#$%^&";
            byte[] pwdBytes = Encoding.UTF8.GetBytes(password);
        
            using (var md5 = MD5.Create())
            using (var sha1 = SHA1.Create())
            {
                byte[] iv = GetByteArray(md5.ComputeHash(pwdBytes), 16);
                byte[] key = GetByteArray(sha1.ComputeHash(pwdBytes), 32);

                var aes = new AesManaged
                {
                    Mode = CipherMode.CBC,
                    Padding = PaddingMode.PKCS7,
                    IV = iv,
                    Key = key
                };

                return aes;
            }
        }

        private static string Encrypt(string source, AesManaged cipher)
        {
            if (string.IsNullOrEmpty(source) || cipher == null)
            {
                return source;
            }

            // AES encryption
            ICryptoTransform encryptor = cipher.CreateEncryptor(cipher.Key, cipher.IV);  
  
            byte[] encrypted;  
            
            using (MemoryStream msEncrypt = new MemoryStream())  
            {  
                using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))  
                {  
                    using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))  
                    {  
                        swEncrypt.Write(source);  
                    }  
                    encrypted = msEncrypt.ToArray();  
                }  
            } 

            // Base64 encoding
            return Convert.ToBase64String(encrypted);
        }
        
        
        private static byte[] GetByteArray(byte[] sourceBytes, int destLen)
        {
            byte[] dest = new byte[destLen];
            int p = 0;

            while (p < destLen)
            {
                foreach (byte b in sourceBytes)
                {
                    if (p >= destLen)
                    {
                        return dest;
                    }
                    dest[p++] = b;
                }
            }

            return dest;
        }

       
    }
}