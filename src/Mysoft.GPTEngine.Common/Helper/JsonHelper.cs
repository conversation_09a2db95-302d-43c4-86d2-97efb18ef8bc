using System;
using System.Collections.Generic;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class JsonHelper
    {
        // 添加字段到JSON对象数组
        public static string AddFieldToJsonArray(string jsonArray, string fieldName, string fieldValue)
        {
            if (!JsonValidateHelper.IsValidJson(jsonArray))
            {
                return "";
            }
            // 将JSON字符串转换为JsonNode数组
            var array = JsonNode.Parse(jsonArray) as JsonArray;

            if (array == null)
            {
                throw new ArgumentException("Provided JSON is not an array.");
            }

            foreach (var item in array)
            {
                if (item is JsonObject obj)
                {
                    // 向每个对象添加新的字段
                    obj[fieldName] = JsonValue.Create(fieldValue);
                }
            }

            // 创建序列化选项，禁止Unicode转义
            var options = new JsonSerializerOptions
            {
                Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                WriteIndented = true // 可选：美化输出
            };

            // 返回修改后的JSON字符串
            return System.Text.Json.JsonSerializer.Serialize(array, options);
        }

        // 从JSON对象数组中移除字段
        public static string RemoveFieldFromJsonArray(string jsonArray, string fieldName)
        {
            if (!JsonValidateHelper.IsValidJson(jsonArray))
            {
                return "";
            }
            // 将JSON字符串转换为JsonNode数组
            var array = JsonNode.Parse(jsonArray) as JsonArray;

            if (array == null)
            {
                throw new ArgumentException("Provided JSON is not an array.");
            }

            foreach (var item in array)
            {
                if (item is JsonObject obj && obj.ContainsKey(fieldName))
                {
                    // 从每个对象中移除指定的字段
                    obj.Remove(fieldName);
                }
            }

            // 创建序列化选项，禁止Unicode转义
            var options = new JsonSerializerOptions
            {
                Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                WriteIndented = true // 可选：美化输出
            };

            // 返回修改后的JSON字符串
            return JsonSerializer.Serialize(array, options);
        }

        public static string JsonSerialize(string key, object value)
        {
            // 配置序列化设置，禁用字符串转义
            var options = new JsonSerializerOptions
            {
                Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var dictionary = new Dictionary<string, object>
            {
                { key, value }
            };

            // 序列化为JSON字符串
            return JsonSerializer.Serialize(dictionary, options);
        }
    }
}
