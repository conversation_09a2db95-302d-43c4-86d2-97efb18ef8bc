using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;

namespace Mysoft.GPTEngine.Common.Helper
{
    /// <summary>
    /// 
    /// </summary>
    public static class AsposeHelper
    {
        private static Assembly _assembly;

        /// <summary>
        /// 初始化Aspose的License
        /// </summary>
        public static void InitAspose()
        {
            try
            {
                string licName = "Resources.Aspose.Total.lic";
                var license = GetLicense(licName);
                // Console.WriteLine(license.ToString());
                //初始化PDFlicense
                Aspose.Pdf.License pdfLicense = new Aspose.Pdf.License();
                pdfLicense.SetLicense(new MemoryStream(license));
                //初始化EXCEL license
                Aspose.Cells.License cellLicense = new Aspose.Cells.License();
                cellLicense.SetLicense(new MemoryStream(license));
            }
            catch(Exception ex)
            {
                Console.WriteLine(ex.Message + "，堆栈" + ex.StackTrace);
            }
        }

        public static void InitAsposeWord()
        {
            try
            {
                string licName = "Resources.Aspose.Words.NET.lic";
                var license = GetLicense(licName);
                // Console.WriteLine(license.ToString());
                Aspose.Words.License wordLicense = new Aspose.Words.License();
                wordLicense.SetLicense(new MemoryStream(license));
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + "，堆栈" + ex.StackTrace);
            }
        }

        /// <summary>
        /// 根据license文件路径获取license
        /// </summary>
        /// <param name="licName"></param>
        /// <returns></returns>
        private static byte[] GetLicense(string licName)
        {
            if (_assembly == null)
            {
                _assembly = Assembly.GetExecutingAssembly();
            }
            var resourceName = $"{_assembly.GetName().Name}.{licName}";
            Stream stream = _assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
                throw new ApplicationException($"{licName}加载失败");
            using (var memoryStream = new MemoryStream())
            {
                stream.CopyTo(memoryStream);
                var license = memoryStream.ToArray();
                return license;
            }
        }
    }
}
