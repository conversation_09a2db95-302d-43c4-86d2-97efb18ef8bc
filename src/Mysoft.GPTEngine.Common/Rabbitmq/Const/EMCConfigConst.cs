using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Common.Rabbitmq.Const
{
    public class EMCConfigConst
    {
        public const string SKYLINE_NACOS_GROUP = "SKYLINE_NACOS_GROUP";
        public const string SKYLINE_NACOS_SERVER_ADDRESSES = "SKYLINE_NACOS_SERVER_ADDRESSES";
        public const string SKYLINE_NACOS_NAMESPACE_ID = "SKYLINE_NACOS_NAMESPACE_ID";
        public const string SKYLINE_NACOS_USERNAME = "SKYLINE_NACOS_USERNAME";
        public const string SKYLINE_NACOS_PASSWORD = "SKYLINE_NACOS_PASSWORD";
        public const string SKYLINE_NACOS_SECRET = "SKYLINE_NACOS_SECRET";
        public const string SKYLINE_NACOS_ENCRYPTION_ALGORITHM = "SKYLINE_NACOS_ENCRYPTION_ALGORITHM";
        public const string SKYLINE_NACOS_PORT = "SKYLINE_NACOS_PORT";


        // config key
        public const string IpassUrl = "ipaas.url";
        public const string MilvusCode = "gpt-platform";
        public const string CategoryType = "tenant";
        public const string DbCode = "apaas";
        public const string ApassUrl = "apaas.url";
        public const string ApassInsideUrl = "apaas.insideUrl";
        public const string BpmUrl = "bpm.url";
        public const string DpUrl = "dp.url";
        public const string DpInsideHost = "dp-bi-server.insideHost";
    }
}
