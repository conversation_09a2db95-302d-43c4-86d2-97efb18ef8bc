using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Common.CustomerException
{
    public class JsonFormatException : Exception
    {
        // 默认构造函数
        public JsonFormatException() : base() { }

        // 带有消息的构造函数
        public JsonFormatException(string message) : base(message) { }

        // 带有消息和内部异常的构造函数
        public JsonFormatException(string message, Exception innerException)
            : base(message, innerException) { }
    }
}
