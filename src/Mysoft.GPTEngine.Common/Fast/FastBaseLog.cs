using System;
using Newtonsoft.Json;
using Serilog.Context;

namespace Mysoft.GPTEngine.Common.fast
{
    public abstract class FastBaseLog
    {
        /// <summary>
        /// 租户Code
        /// </summary>
        public string TenantCode { get; set; } = "tenantCode";
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// 用户Code
        /// </summary>
        public string UserCode { get; set; } = "userCode";

        /// <summary>
        /// 日志记录创建时间
        /// </summary>
        public DateTime? Time { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 所属模块
        /// </summary>
        public string Model { get; set; }
        
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
        
        public abstract string GetName();
        
    }
}