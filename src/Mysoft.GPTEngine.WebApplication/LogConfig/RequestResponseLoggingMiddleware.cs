using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.fast;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;


namespace Mysoft.GPTEngine.WebApplication.LogConfig
{
    public class RequestResponseLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<RequestResponseLoggingMiddleware> _logger;

        private readonly IMysoftContextFactory _mysoftContextFactory;

        public RequestResponseLoggingMiddleware(RequestDelegate next, ILogger<RequestResponseLoggingMiddleware> logger, IMysoftContextFactory mysoftContextFactory)
        {
            _next = next;
            _logger = logger;
            _mysoftContextFactory = mysoftContextFactory;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            MysoftContext mysoftContext = _mysoftContextFactory.GetMysoftContext();
            FastRequestLog log = new FastRequestLog();
            log.TenantCode = mysoftContext.TenantCode;
            log.UserCode = mysoftContext.UserContext.UserCode;
            log.Cookie = string.Join(", ", context.Request.Cookies.ToDictionary(c => c.Key, c => c.Value).Select(kvp => $"{kvp.Key}={kvp.Value}"));
            log.Mothed = context.Request.Method;
            log.ResponseUrl = context.Request.Path;
            log.StartTime = TimeZoneUtility.LocalNow();

            // 记录请求开始
            log.RequestBody = await FormatRequestAsync(context.Request);

            // 用于捕获响应体的流
            Stream originalResponseBody = context.Response.Body;
            string responseBody = string.Empty;

            try
            {
                // 检查是否为流式响应（text/event-stream），如果是则跳过响应体拦截
                bool isStreamingResponse = context.Request.Path.Value?.Contains("StreamingChatCompletion") == true ||
                                         context.Response.ContentType?.Contains("text/event-stream") == true;

                if (isStreamingResponse)
                {
                    // 对于流式响应，直接执行下一个中间件，不拦截响应体
                    await _next(context);
                    responseBody = "[Streaming Response - Content not logged to preserve real-time streaming]";
                }
                else
                {
                    // 对于非流式响应，使用内存流来捕获响应内容
                    using (var responseBodyStream = new MemoryStream())
                    {
                        context.Response.Body = responseBodyStream;

                        // 继续执行下一个Middleware
                        await _next(context);

                        // 安全地读取响应体（只有在响应未开始时）
                        if (!context.Response.HasStarted)
                        {
                            responseBody = await SafeFormatResponseAsync(context.Response, responseBodyStream);

                            // 将捕获的内容写回原始响应流
                            responseBodyStream.Seek(0, SeekOrigin.Begin);
                            await responseBodyStream.CopyToAsync(originalResponseBody);
                        }
                        else
                        {
                            // 响应已开始，直接复制内容到原始流
                            responseBodyStream.Seek(0, SeekOrigin.Begin);
                            await responseBodyStream.CopyToAsync(originalResponseBody);
                            responseBody = "[Stream Response - Content not captured due to response already started]";
                        }
                    }
                }

                log.ResponseBody = responseBody;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "RequestResponseLoggingMiddleware：");
                throw;
            }
            finally
            {
                // 恢复原始响应流
                context.Response.Body = originalResponseBody;

                log.EndTime = TimeZoneUtility.LocalNow();
                log.Duration = (log.EndTime - log.StartTime).TotalSeconds;
                log.StatusCode = context.Response.StatusCode.ToString();

                FastLogger.Write(log);
            }
        }

        private async Task<string> FormatRequestAsync(HttpRequest request)
        {
            request.EnableBuffering();
            var buffer = new byte[Convert.ToInt32(request.ContentLength)];
            await request.Body.ReadAsync(buffer, 0, buffer.Length);
            request.Body.Seek(0, SeekOrigin.Begin); // 重置请求体指针以便后续Middleware使用
            return Encoding.UTF8.GetString(buffer);
        }

        /// <summary>
        /// 安全地格式化响应内容，避免在响应已开始后修改响应头
        /// </summary>
        /// <param name="response">HTTP响应</param>
        /// <param name="responseBodyStream">响应体流</param>
        /// <returns>响应体内容</returns>
        private async Task<string> SafeFormatResponseAsync(HttpResponse response, MemoryStream responseBodyStream)
        {
            try
            {
                // 检查响应是否已经开始
                if (response.HasStarted)
                {
                    return "[Response already started - content not captured]";
                }

                // 重置内存流以便读取
                responseBodyStream.Seek(0, SeekOrigin.Begin);

                // 读取响应体内容
                using (var reader = new StreamReader(responseBodyStream, leaveOpen: true))
                {
                    string responseBody = await reader.ReadToEndAsync();
                    return responseBody;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法读取响应体内容");
                return "[Error reading response content]";
            }
        }

        private async Task<string> FormatResponseAsync(HttpResponse response)
        {
            var originalResponseBody = response.Body; // 备份原始响应体流

            try
            {
                // 使用内存流临时存储响应体，以便多次读取
                using (var responseBodyStream = new MemoryStream())
                {
                    response.Body = responseBodyStream; // 替换响应体流

                    // 重置内存流以便读取
                    responseBodyStream.Seek(0, SeekOrigin.Begin);

                    // 读取并记录响应体内容
                    string responseBody = await new StreamReader(responseBodyStream).ReadToEndAsync();
                    return responseBody;
                }
            }
            finally
            {
                // 恢复原始响应体流，以便框架可以正常写出响应
                response.Body = originalResponseBody;
            }
        }
    }
}