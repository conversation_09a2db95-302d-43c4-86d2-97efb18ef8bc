using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.fast;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;

namespace Mysoft.GPTEngine.WebApplication.LogConfig
{
    public class LoggingHttpMessageHandlerBuilderFilter : IHttpMessageHandlerBuilderFilter
    {
        private readonly ILoggerFactory _loggerFactory;

        private readonly IMysoftContextFactory _mysoftContextFactory;

        public LoggingHttpMessageHandlerBuilderFilter(ILoggerFactory loggerFactory, IMysoftContextFactory mysoftContextFactory)
        {
            _loggerFactory = loggerFactory;
            _mysoftContextFactory = mysoftContextFactory;
        }

        public Action<HttpMessageHandlerBuilder> Configure(Action<HttpMessageHandlerBuilder> next)
        {
            return (builder) =>
            {
                // 跳过 MCP 工具客户端，避免 HTTP 上下文依赖问题
                if (builder.Name == "McpToolsClient")
                {
                    // 对于 MCP 工具客户端，直接调用下一个过滤器，不添加日志处理器
                    next(builder);
                    return;
                }

                // 创建日志拦截器,并将其添加到处理程序链中
                var loggingHandler = new LoggingHttpMessageHandler(
                    _loggerFactory.CreateLogger<LoggingHttpMessageHandler>(),
                    builder.PrimaryHandler, _mysoftContextFactory);
                builder.PrimaryHandler = loggingHandler;

                // 调用下一个过滤器
                next(builder);
            };
        }
    }

    public class LoggingHttpMessageHandler : DelegatingHandler
    {
        private readonly ILogger<LoggingHttpMessageHandler> _logger;
        private readonly IMysoftContextFactory _mysoftContextFactory;

        public LoggingHttpMessageHandler(ILogger<LoggingHttpMessageHandler> logger, HttpMessageHandler innerHandler, IMysoftContextFactory mysoftContextFactory)
            : base(innerHandler)
        {
            _logger = logger;
            _mysoftContextFactory = mysoftContextFactory;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, System.Threading.CancellationToken cancellationToken)
        {
            // 记录请求信息
            MysoftContext mysoftContext = _mysoftContextFactory.GetMysoftContext();
            FastRequestLog log = new FastRequestLog();
            log.TenantCode = mysoftContext.TenantCode;
            log.UserCode = mysoftContext.UserContext.UserCode;
            log.Handler = GetRequestHeaders(request);
            log.Cookie = LogRequestCookies(request);
            log.Mothed = request.Method.ToString();
            log.ResponseUrl = request.RequestUri.ToString();
            log.StartTime = TimeZoneUtility.LocalNow();
            log.RequestBody = await ReadRequestContent(request);

            var response = new HttpResponseMessage();
            try
            {
                // 发送请求并获取响应
                response = await base.SendAsync(request, cancellationToken);
            }
            catch (Exception e)
            {
                _logger.LogError("LoggingHttpMessageHandler：", e);
                throw;
            }
            finally
            {
                log.EndTime = TimeZoneUtility.LocalNow();
                log.Duration = (log.EndTime - log.StartTime).TotalSeconds;
                log.StatusCode = response.StatusCode.ToString();
                log.ResponseBody = await ReadResponseContent(response);

                FastLogger.Write(log);
            }



            return response;
        }

        private async Task<string> ReadRequestContent(HttpRequestMessage request)
        {
            if (request.Content != null)
            {
                return await request.Content.ReadAsStringAsync();
            }
            return string.Empty;
        }

        private async Task<string> ReadResponseContent(HttpResponseMessage response)
        {
            if (response.Content != null)
            {
                return await response.Content.ReadAsStringAsync();
            }
            return string.Empty;
        }

        private string LogRequestCookies(HttpRequestMessage request)
        {
            if (request.Headers.TryGetValues("Cookie", out var cookieValues))
            {
                return string.Join(", ", cookieValues);
            }

            return "";
        }

        private string GetRequestHeaders(HttpRequestMessage request)
        {
            var requestHeaders = new Dictionary<string, string>();
            foreach (var header in request.Content.Headers)
            {
                requestHeaders[header.Key] = string.Join(", ", header.Value);
            }

            return JsonSerializer.Serialize(requestHeaders, new JsonSerializerOptions { WriteIndented = true });
        }

    }

}