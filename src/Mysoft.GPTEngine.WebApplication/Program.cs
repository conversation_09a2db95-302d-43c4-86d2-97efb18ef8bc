using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Mysoft.GPTEngine.Common.Encryption;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.WebApplication.LogConfig;
using Nacos.Microsoft.Extensions.Configuration;
using Nacos.V2;
using Newtonsoft.Json;
using Serilog;

namespace Mysoft.GPTEngine.WebApplication
{
    public class Program
    {
        public static void Main(string[] args)
        {
            LogSetting.Init();

            Log.Information("=== GPT引擎应用启动 ===");
            Log.Information("[MCP Server] 正在启动MCP服务器...");

            var host = CreateHostBuilder(args).Build();

            // 记录应用启动信息
            var server = host.Services.GetService(typeof(Microsoft.AspNetCore.Hosting.Server.IServer)) as Microsoft.AspNetCore.Hosting.Server.IServer;
            var urls = server?.Features.Get<Microsoft.AspNetCore.Hosting.Server.Features.IServerAddressesFeature>()?.Addresses;

            Log.Information("[MCP Server] GPT引擎应用已启动");
            Log.Information("[MCP Server] MCP端点可通过以下地址访问:");
            if (urls != null)
            {
                foreach (var url in urls)
                {
                    Log.Information("[MCP Server] - {url}/mcp_server/mcp", url);
                }
            }
            else
            {
                Log.Information("[MCP Server] - http://localhost:5000/mcp_server/mcp (默认地址)");
            }

            host.Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                // 构建初始配置
                var builtConfig = config.Build();
                var nacosConfig = new
                {
                    Group = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_GROUP) ?? builtConfig["NacosConfig:GroupName"],
                    ServerAddresses = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_SERVER_ADDRESSES) ?? builtConfig["NacosConfig:ServerAddresses:0"],
                    Namespace = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_NAMESPACE_ID) ?? builtConfig["NacosConfig:DefaultNamespace"],
                    UserName = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_USERNAME) ?? builtConfig["NacosConfig:UserName"],
                    Password = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_PASSWORD) ?? builtConfig["NacosConfig:Password"],
                    Port = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_PORT) ?? "8848"
                };
                Log.Debug("nacosConfig：{0}",JsonConvert.SerializeObject(nacosConfig));
                config.AddNacosV2Configuration((source) =>
                {
                    var envGroupName = nacosConfig.Group;
                    var envServerAddresses = nacosConfig.ServerAddresses;
                    var envDefaultNamespace = nacosConfig.Namespace;
                    var envUserName = nacosConfig.UserName;
                    var envPassword = nacosConfig.Password;

                    source.ServerAddresses = new List<string>() { envServerAddresses };
                    source.Namespace = envDefaultNamespace;
                    source.UserName = envUserName;
                    source.Password = envPassword;
                    source.ConfigUseRpc = false;
                    source.NamingUseRpc = false;

                    source.Listeners = source.Listeners ?? new List<ConfigListener>();
                    source.Listeners.Add(new ConfigListener()
                    {
                        Group = "DEFAULT_GROUP",
                        DataId = "global.properties"
                    });
                    source.Listeners.Add(new ConfigListener()
                    {
                        Group = "DEFAULT_GROUP",
                        DataId = "global_url.properties"
                    });
                    source.Listeners.Add(new ConfigListener()
                    {
                        Group = "DEFAULT_GROUP",
                        DataId = "tenants.properties"
                    });
                    source.Listeners.Add(new ConfigListener()
                    {
                        Group = "DEFAULT_GROUP",
                        DataId = "resource_instance.properties"
                    });
                    source.Listeners.Add(new ConfigListener()
                    {
                        Group = "DEFAULT_GROUP",
                        DataId = "resource_info.properties"
                    });

                    source.NacosConfigurationParser = new ThreadSafeYamlConfigurationStringParser();
                });
            })

                .UseSerilog()
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });
    }

    public class ThreadSafeYamlConfigurationStringParser : INacosConfigurationParser
    {
        public string Decrypt(string input)
        {
            var algorithm = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_ENCRYPTION_ALGORITHM);
            if (string.Equals("SM4", algorithm, StringComparison.CurrentCultureIgnoreCase))
            {
                var encryptionSecret = Environment.GetEnvironmentVariable(EMCConfigConst.SKYLINE_NACOS_SECRET);
                var plainText = SM4Helper.Decrypt_CBC(encryptionSecret, false, encryptionSecret, input, 2);
                return plainText;
            }
            else
            {
                return input;
            }
        }

        public IDictionary<string, string> Parse(string keyValueData)
        {
            // 使用正则表达式匹配属性配置  
            Dictionary<string, string> properties = new Dictionary<string, string>();
            if (string.IsNullOrWhiteSpace(keyValueData))
            {
                return properties;
            }
            string[] lines = keyValueData.Split('\n');
            foreach (string line in lines)
            {
                string trimmedLine = line.Trim();
                if (!string.IsNullOrEmpty(trimmedLine) && !trimmedLine.StartsWith("#") && trimmedLine.Contains("="))
                {
                    string[] pair = trimmedLine.Split('=');
                    if (pair.Length == 0) continue;
                    if (pair.Length == 1)
                    {
                        var key = pair[0].Trim();
                        properties[key] = String.Empty;
                    }
                    else
                    {
                        var key = pair[0].Trim();
                        var value = string.Join("=", pair.Skip(1)).Trim();
                        // 处理值中的单引号和转义字符
                        value = value.Replace("\\'", "'"); // 去掉转义单引号

                        //value解密
                        try
                        {
                            value = this.Decrypt(value);
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("解密失败：" + e);
                            throw;
                        }
                        properties[key] = value;
                    }
                }
            }

            foreach (var kvp in properties)
            {
                var skipKeys = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { "license", "tenantsConfigs" };
                if (skipKeys.Contains(kvp.Key) || kvp.Key.Contains("Instances", StringComparison.OrdinalIgnoreCase))                {
                    continue;
                }

                Log.Debug("GPT: {0}：{1}", kvp.Key, kvp.Value);
            }

            return properties;
        }
    }

}
