{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {}}, "Mysoft.GPTEngine.WebApplication": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"GPT_BUILDER_PATH": "http://modelingplatform:9300/", "ASPNETCORE_HOSTINGSTARTUPASSEMBLIES": "FastTracker.Agent.AspNetCore;Mysoft.GPTEngine.Diagnostics.Extend", "FastTracker_Enable": "True", "FastTracker_ServiceName": "gpt-engine", "FastTracker_Logging_Level": "INFO", "FastTracker_Logging_FilePath": "logs/fast-tracker/", "FastTracker_Transport_Report_Endpoint": "https://fast-logstore.mypaas.com/report/BW_rRa3NX7Db5jVfFqx50PPQxB-QwQHeycflkiYwxOuJj1s_TT9j32WtLOYEu4Fb?env=jm-apaas-test", "LOG_LEVEL": "Debug", "PYTHONNET_PYDLL": "PythonLibrary\\python310.dll"}, "applicationUrl": "http://gptengine:5000"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_HTTP_PORTS": "5000"}, "publishAllPorts": false, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:47600", "sslPort": 44356}}}