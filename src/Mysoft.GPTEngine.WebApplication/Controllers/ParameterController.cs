using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
#pragma warning disable SKEXP0010
    /// <summary>
    /// 参数解析控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class ParameterController : ControllerBase
    {
        private ParameterAppService _service;
        public ParameterController(ParameterAppService service) { _service = service; }
        
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ParamMapping(ParamMappingRequestDto paramMappingRequestDto)
        {
            return await _service.ParamMapping(paramMappingRequestDto);
        }
    }
}
