using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// 提示词控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class PromptController : ControllerBase
    {
        private PromptAppService _service;
        public PromptController(PromptAppService service) { _service = service; }


        /// <summary>
        /// 测试提示词场景
        /// </summary>
        /// <param name="promptTestSceneGUID">测试场景GUID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task TestPromptScene(Guid promptTestSceneGUID, CancellationToken cancellationToken)
        {
            await _service.TestPromptScene(promptTestSceneGUID, cancellationToken);
        }
    }
}
