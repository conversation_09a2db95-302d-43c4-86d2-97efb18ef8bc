using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Common.fast;
using Mysoft.GPTEngine.Domain.DTO;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// MCP服务控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class McpController : ControllerBase
    {
        private readonly McpAppService _mcpAppService;
        private readonly ILogger<McpController> _logger;

        public McpController(McpAppService mcpAppService, ILogger<McpController> logger)
        {
            _mcpAppService = mcpAppService;
            _logger = logger;
        }

        /// <summary>
        /// 获取MCP服务器的所有工具列表详细信息
        /// </summary>
        /// <param name="request">获取工具列表请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>工具列表</returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto<List<McpToolInfo>>> GetMcpTools(
            [FromBody] GetMcpToolsRequest request,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("[McpController] 获取MCP工具列表请求: {serviceGUID}", request.ServiceGUID);

            return await _mcpAppService.GetMcpToolsByServiceGuidAsync(
                request.ServiceGUID,
                cancellationToken);
        }

        /// <summary>
        /// 执行指定的MCP工具方法
        /// </summary>
        /// <param name="request">执行工具请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto<McpExecuteResponse>> ExecuteMcpTool(
            [FromBody] ExecuteMcpToolRequest request,
            CancellationToken cancellationToken = default)
        {
            var toolIdentifier = !string.IsNullOrWhiteSpace(request.ExecuteRequest.ToolGUID)
                ? $"ToolGUID: {request.ExecuteRequest.ToolGUID}"
                : $"ToolName: {request.ExecuteRequest.ToolName}";
            _logger.LogInformation("[McpController] 执行MCP工具请求: {toolIdentifier}, 服务GUID: {serviceGUID}",
                toolIdentifier, request.ServiceGUID);

            return await _mcpAppService.ExecuteMcpToolByServiceGuidAsync(
                request.ExecuteRequest,
                request.ServiceGUID,
                cancellationToken);
        }

        /// <summary>
        /// 测试MCP服务器连接
        /// </summary>
        /// <param name="request">连接测试请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接测试结果</returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto<bool>> TestMcpConnection(
            [FromBody] TestMcpConnectionRequest request,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("[McpController] 测试MCP服务器连接请求: {serviceGUID}", request.ServiceGUID);

            return await _mcpAppService.TestMcpConnectionByServiceGuidAsync(
                request.ServiceGUID,
                cancellationToken);
        }
    }

    /// <summary>
    /// 获取MCP工具列表请求
    /// </summary>
    public class GetMcpToolsRequest
    {
        /// <summary>
        /// MCP服务GUID
        /// </summary>
        public string ServiceGUID { get; set; }
    }

    /// <summary>
    /// 执行MCP工具请求
    /// </summary>
    public class ExecuteMcpToolRequest
    {
        /// <summary>
        /// MCP服务GUID
        /// </summary>
        public string ServiceGUID { get; set; }

        /// <summary>
        /// 执行请求详情
        /// </summary>
        public McpExecuteRequest ExecuteRequest { get; set; }
    }

    /// <summary>
    /// 测试MCP连接请求
    /// </summary>
    public class TestMcpConnectionRequest
    {
        /// <summary>
        /// MCP服务GUID
        /// </summary>
        public string ServiceGUID { get; set; }
    }
}
