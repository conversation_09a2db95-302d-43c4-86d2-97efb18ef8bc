using Microsoft.AspNetCore.Http;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Serilog;

namespace Mysoft.GPTEngine.WebApplication.Middleware
{
    /// <summary>
    /// MCP请求日志记录中间件
    /// </summary>
    public class McpLoggingMiddleware
    {
        private readonly RequestDelegate _next;

        public McpLoggingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 检查是否是MCP相关的请求
            if (context.Request.Path.StartsWithSegments("/mcp_server"))
            {
                await LogMcpRequest(context);
            }

            await _next(context);
        }

        private async Task LogMcpRequest(HttpContext context)
        {
            var request = context.Request;
            var method = request.Method;
            var path = request.Path;
            var queryString = request.QueryString;
            var userAgent = request.Headers["User-Agent"].ToString();
            var contentType = request.ContentType;

            Log.Information("[MCP Request] {method} {path}{queryString} - ContentType: {contentType}, UserAgent: {userAgent}", 
                method, path, queryString, contentType, userAgent);

            // 如果是POST请求，记录请求体内容（仅用于调试，生产环境可能需要移除）
            if (method == "POST" && request.ContentLength > 0)
            {
                try
                {
                    request.EnableBuffering();
                    var buffer = new byte[Convert.ToInt32(request.ContentLength)];
                    await request.Body.ReadAsync(buffer, 0, buffer.Length);
                    var bodyAsText = Encoding.UTF8.GetString(buffer);
                    request.Body.Position = 0;

                    // 只记录前500个字符，避免日志过长
                    var logBody = bodyAsText.Length > 500 ? bodyAsText.Substring(0, 500) + "..." : bodyAsText;
                    Log.Debug("[MCP Request Body] {body}", logBody);
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "[MCP Request] 读取请求体失败");
                }
            }

            // 记录请求开始时间
            context.Items["McpRequestStartTime"] = DateTime.UtcNow;
        }
    }
}
