using AutoMapper;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.WebApplication.Profiles
{
    public class AssistantProfile : Profile
    {
        public AssistantProfile()
        {
            CreateMap<ChatEntity, ChatDto>().ReverseMap();
            CreateMap<ChatArgumentsEntity, KeyValueDto>().ReverseMap();
            CreateMap<ChatMessageEntity, ChatMessageDto>().ReverseMap();
            CreateMap<ChatMessageFileEntity, ChatMessageFileDto>().ReverseMap();
            CreateMap<ParamDto, FormConfigDto>().ReverseMap();

            CreateMap<KnowledgeFileSectionEntity, KnowledgeFileSectionDto>().ReverseMap();
            CreateMap<KnowledgeFileEntity, KnowledgeFileSectionDto>().ReverseMap();

            CreateMap<QuestionEntity, KnowledgeQuestionDto>().ReverseMap();

            CreateMap<ModelInstanceEntity, ModelInstanceDto>().ReverseMap();
            CreateMap<ChatMessageKnowledgeNodeLogEntity, ChatMessageKnowledgeNodeLogDto>().ReverseMap();

            CreateMap<ChatMessageNodeLogEntity, ChatMessageNodeLogDto>()
                .ForMember(dest => dest.StartTimeMillisecond, opt => opt.Ignore())
                .ForMember(dest => dest.EndTimeMillisecond, opt => opt.Ignore());

            CreateMap<ChatMessageNodeLogDto, ChatMessageNodeLogEntity>().ReverseMap();
        }
    }
}
