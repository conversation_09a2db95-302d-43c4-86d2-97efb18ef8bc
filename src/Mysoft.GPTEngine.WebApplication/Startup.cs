using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Extensions;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.WebApplication.LogConfig;
using Serilog;
using Yarp.ReverseProxy.Model;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Domain.Shared.Constants;

namespace Mysoft.GPTEngine.WebApplication
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var gptBuilderPath = Configuration[EMCConfigConst.ApassInsideUrl];
            Console.WriteLine("gptBuilderPath.apaas.insideurl:" + gptBuilderPath);
            if (!String.IsNullOrWhiteSpace(gptBuilderPath))
            {
                Configuration["ReverseProxy:Clusters:gpt_builder:Destinations:destination:Address"] = gptBuilderPath;
                ApaasInfoConst.ApaasUrlValue = gptBuilderPath;
            }

            services.AddReverseProxy().LoadFromConfig(Configuration.GetSection("ReverseProxy"));
            services
                .AddService()
                .AddSemanticKernelServices(Configuration)
                .AddControllers(options =>
                {
                    //  options.Filters.Add<SemanticKernelFilter>();
                });
            services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
            services.AddSingleton(Log.Logger);

            services.AddHeartbeatService();

            ConfigureCors(services);
            InitTimezone();
        }

        private void InitTimezone()
        {
            if (!string.IsNullOrEmpty(Configuration["timezone"]))
            {
                var tzi = TimeZoneInfo.FindSystemTimeZoneById(Configuration["timezone"]);
                TimeZoneUtility.CurrentTimeZone = tzi;
            }
        }

        private void ConfigureCors(IServiceCollection services)
        {
            services.AddCors(options =>
            {
                options.AddDefaultPolicy(builder =>
                {
                    builder
                        .WithOrigins("*")
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                });
            });

        }
        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {


            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseCors();

            app.UseMiddleware<HttpContextEnricherMiddleware>();

            app.UseMiddleware<RequestResponseLoggingMiddleware>();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapReverseProxy(proxyPipeline =>
                {
                    proxyPipeline.Use(async (context, next) =>
                    {
                        var routeData = context.GetEndpoint()?.Metadata.GetMetadata<RouteModel>();
                        if (routeData != null && (routeData.Config.RouteId == "api_source_route" 
                                                 || routeData.Config.RouteId == "api_plan_source_route"
                                                 || routeData.Config.RouteId == "api_privacyAgreement_source_route"))
                        {
                            CheckPlatformTokenService checkPlatformTokenService = context.RequestServices.GetRequiredService<CheckPlatformTokenService>();
                            var configurationSection = Configuration.GetSection("ReverseProxy:Routes:api_source_route:AccessTokenApi");
                            var handlerAccessToken = configurationSection.GetChildren().Any(c => c.Value == context.Request.Path.ToString());
                            if (handlerAccessToken)
                            {
                                await checkPlatformTokenService.GetAccessToken(context);
                            }
                            await checkPlatformTokenService.Check(context, routeData.Cluster.Destinations["destination"].Model.Config.Address);

                            checkPlatformTokenService.SetAccessTokenToQueryParams(context);
                        }

                        if (routeData != null && routeData.Config.RouteId == "static_source_route" && CheckNotCachePage(context))
                        {
                            context.Response.OnStarting(() =>
                            {
                                context.Response.Headers.Remove("ETag");
                                context.Response.Headers.Remove("Last-Modified");
                                context.Response.Headers.Remove("Content-Security-Policy");
                                return Task.CompletedTask;
                            });
                        }

                        await next();
                    });
                });
                endpoints.MapControllers();
            });

        }

        private bool CheckNotCachePage(HttpContext context)
        {
            return context.Request.Path.Value.Contains(".html");
        }
    }

}
