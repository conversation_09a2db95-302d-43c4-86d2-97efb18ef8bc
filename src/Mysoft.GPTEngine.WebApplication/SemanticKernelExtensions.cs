using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Milvus;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.SemanticKernel.Memory;
using Milvus.Client;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Baidu;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Extensions;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen;
using Mysoft.GPTEngine.SemanticKernel.Core.Xunfei;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.WebApplication
{
    public static class SemanticKernelExtensions
    {
        /// <summary>
        /// Add Semantic Kernel services
        /// </summary>
        public static IServiceCollection AddSemanticKernelServices(this IServiceCollection services, IConfiguration configuration)
        {
            var memoryBuilder = new MemoryBuilder();

            services.AddSingleton(memoryBuilder);

            services.AddKernelBuilder(configuration);
            return services;
        }

        public static IServiceCollection AddKernelBuilder(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<Kernel>(provider =>
            {
                var kernelBuilder = Kernel.CreateBuilder();


                var modelInstances = GetModelInstancesAll(provider);
                List<Domain.Shared.Enums.ServiceTypeEnum> textServiceTypeEnumList = new List<Domain.Shared.Enums.ServiceTypeEnum>()
                {
                    Domain.Shared.Enums.ServiceTypeEnum.TextGeneration,
                    Domain.Shared.Enums.ServiceTypeEnum.FileComprehend,
                    Domain.Shared.Enums.ServiceTypeEnum.ImageComprehend
                };
                var textModelInstances = modelInstances.Where(x => textServiceTypeEnumList.Contains(x.ServiceTypeEnum)).ToList();
                var embeddingInstances = modelInstances.Where(x => x.ServiceTypeEnum == Domain.Shared.Enums.ServiceTypeEnum.Embedding).ToList();
                var ocrRecognizeServiceInstances = modelInstances.Where(x => x.ServiceTypeEnum == Domain.Shared.Enums.ServiceTypeEnum.OcrRecognizeService).ToList();
                var contentReviewServiceInstances = modelInstances.Where(x => x.ServiceTypeEnum == Domain.Shared.Enums.ServiceTypeEnum.ContentReview).ToList();

                kernelBuilder.AddChatCompletionServices(textModelInstances).GetAwaiter().GetResult();
                kernelBuilder.AddOcrRecognizeServices(ocrRecognizeServiceInstances).GetAwaiter().GetResult();
                kernelBuilder.AddContentReviewServices(contentReviewServiceInstances).GetAwaiter().GetResult();
                AddEmbeddingServices(kernelBuilder, provider, embeddingInstances, configuration).GetAwaiter().GetResult();

                var kernel = kernelBuilder.Build();
                return kernel;
            });

            return services;
        }
        //public static IKernelBuilder
        private static SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private static List<ModelInstanceDto> GetModelInstancesAll(IServiceProvider provider)
        {
            var modelInstanceService = provider.GetRequiredService<ModelInstanceAppService>();
            var modelInstances = modelInstanceService.GetModelInstancesAsync().GetAwaiter().GetResult().Where(w => w.IsAvailable).ToList();
            foreach (var modelInstance in modelInstances)
            {
                if (!string.IsNullOrEmpty(modelInstance.ApiKey))
                {
                    modelInstance.ApiKey = AesHelper.Decrypt(modelInstance.ApiKey);
                }
            }

            return modelInstances;
        }

        private static async Task AddEmbeddingServices(IKernelBuilder kernelBuilder, IServiceProvider provider, List<ModelInstanceDto> modelInstances, IConfiguration configuration)
        {
            try
            {
                //设置支持非HTTPS
                AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2UnencryptedSupport", true);
                //设置System.Drawing跨平台
                AppContext.SetSwitch("System.Drawing.EnableUnixSupport", true);
                if (modelInstances?.Count == 0) return;
                var mysoftContextFactory = provider.GetService<IMysoftContextFactory>();
                var memoryBuilder = provider.GetService<MemoryBuilder>();
                var myContext = mysoftContextFactory.GetMysoftContext();

                //租户code 作为数据库名，需要先判断一下数据库是否存在，不存在则创建
                //创建一个mv MemoryBuilder
                MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient(isThrowException:false);
                if (milvusClient != null)
                {
                    IReadOnlyList<string> dataBases = await milvusClient.ListDatabasesAsync();
                    if (dataBases == null || !dataBases.Contains(myContext.TenantCode))
                    {
                        await milvusClient.CreateDatabaseAsync(myContext.TenantCode);
                    }
                }
#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0020 
                memoryBuilder.WithMemoryStore(myContext.MemoryStore.CreateMilvusMemoryStore(myContext.TenantCode));
                modelInstances.ForEach(modelInstance =>
                {
                    string chatCompletionType = null;
                    var serviceId = modelInstance.IsDefault ? chatCompletionType : modelInstance.InstanceCode;

                    switch (modelInstance.ModelType)
                    {
                        case ModelTypeEnum.AzureOpenAI:
                            kernelBuilder.AddAzureOpenAIEmbedding(deploymentName: modelInstance.DeploymentName, modelId: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.OpenAI:
                            kernelBuilder.AddOpenAIEmbedding(modelId: modelInstance.ModelCode, apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.Ali:
                            kernelBuilder.AddQwenEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.Xunfei:
                            kernelBuilder.AddXunfeiEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.Baidu:
                            kernelBuilder.AddBaiduEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, clientId: modelInstance.ClientID, serviceId: serviceId);
                            break;
                        default:
                            string modelInstanceString = JsonConvert.SerializeObject(modelInstance);
                            CustomRedirectHttpDto customRedirectHttpDto = JsonConvert.DeserializeObject<CustomRedirectHttpDto>(modelInstanceString);
                            MysoftKernelBuilderExtensions.AddMysoftEmbedding(kernelBuilder, deploymentName: modelInstance.DeploymentName, modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, clientId: modelInstance.ClientID, vendor: modelInstance.Vendor, serviceId: serviceId, httpClient: new HttpClient(new CustomRedirectingHandler(customRedirectHttpDto)));
                            break;
                    }
                });
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }
    }
}
