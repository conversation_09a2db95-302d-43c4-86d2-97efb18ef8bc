using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.ContentReview;

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Plugin.Activities
{
    /// <summary>
    /// 开始节点
    /// </summary>
    public class StartActivity : SemanticKernelActivity
    {
        private readonly SensitiveWordsRepostory _senitiveWordsRepostory;
        private readonly SkillRepostory _skillRepostory;
        private readonly ContentReviewFactory _contentReviewFactory;
        private readonly MysoftMemoryCache _mysoftMemoryCache;

        public StartActivity(IServiceProvider serviceProvider, MysoftMemoryCache mysoftMemoryCache, ContentReviewFactory contentReviewFactory, SkillRepostory skillRepostory, SensitiveWordsRepostory sensitiveWordsRepostory, ILogger<StartActivity> logger) : base(serviceProvider, logger)
        {
            _senitiveWordsRepostory = sensitiveWordsRepostory;
            _skillRepostory = skillRepostory;
            _contentReviewFactory = contentReviewFactory;
            _mysoftMemoryCache = mysoftMemoryCache;
        }
        [KernelFunction(nameof(StartActivity))]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }

        /// <summary>
        /// 敏感词校验
        /// </summary>
        private async Task SensitiveWordsCheck(string input,string outputsStr = "")
        {
            SkillEntity skill = await _skillRepostory.GetAsync(f => f.SkillGUID == _chatRunDto.SkillGUID);
            if (skill == null)
            {
                return;
            }
            List<SensitiveWordsEntity> sensitiveWordsEntities = _mysoftMemoryCache.GetSensitiveWordsCache(skill.SpaceGUID.ToString());
            if(sensitiveWordsEntities == null)
            {
                sensitiveWordsEntities = await _senitiveWordsRepostory.GetListAsync(f => f.SpaceGUID == skill.SpaceGUID && f.IsEnabled == 1);
                _mysoftMemoryCache.SetSensitiveWordsCache(sensitiveWordsEntities, skill.SpaceGUID.ToString());
            }

            // 检查是否包含DataQueryActivity，用于决定是否跳过AddMessage
            bool hasDataQueryActivity = HasDataQueryActivity();

            sensitiveWordsEntities.ForEach(f =>
            {
                var splitLines = f.SensitiveWords.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries).Select(w=> w.Trim()).ToList();
                if((!string.IsNullOrEmpty(input) && splitLines.Any(a => input.Contains(a))) || (!string.IsNullOrEmpty(outputsStr) && splitLines.Any(a => outputsStr.Contains(a))))
                {
                    TextEvent(f.Reply, _chatRunDto.IsStream).GetAwaiter().GetResult();

                    // 如果工作流中包含DataQueryActivity，则跳过AddMessage，避免重复保存敏感词回复消息
                    if (!hasDataQueryActivity)
                    {
                        AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, f.Reply)).GetAwaiter().GetResult();
                        _logger.LogInformation("[StartActivity] 已添加敏感词回复消息到ChatMessages");
                    }
                    else
                    {
                        _logger.LogInformation("[StartActivity] 检测到DataQueryActivity，跳过敏感词回复的AddMessage，避免重复保存消息");
                    }

                    if (_chatRunDto.IsStream)
                    {
                        throw new CancelException();
                    }
                    else
                    {
                        throw new ValidateException(f.Reply);
                    }
                }
            });
        }

        /// <summary>
        /// 检查工作流中是否包含DataQueryActivity
        /// </summary>
        /// <returns>如果包含DataQueryActivity返回true，否则返回false</returns>
        private bool HasDataQueryActivity()
        {
            try
            {
                // 检查工作流中的所有节点，看是否有DataQuery类型的节点
                bool hasDataQuery = _chatRunDto.Nodes.Any(node => node.Type == SkillNodeTypeConstant.DataQuery);
                _logger.LogInformation("[StartActivity] 工作流中是否包含DataQueryActivity: {hasDataQuery}", hasDataQuery);
                return hasDataQuery;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[StartActivity] 检查DataQueryActivity时发生异常: {message}", ex.Message);
                // 异常情况下返回false，保持原有行为
                return false;
            }
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            var input = await _chatRunDto.GetArgumentValue<string>(KernelArgumentsConstant.Input);

            // 检查工作流中是否包含DataQueryActivity，如果包含则跳过AddMessage
            bool hasDataQueryActivity = HasDataQueryActivity();

            if (flowNode.Config.OutputType == "content")
            {
                _logger.LogInformation($"{nameof(StartActivity)}: {0}", input);

                // 如果工作流中包含DataQueryActivity，则跳过AddMessage，避免重复保存用户消息
                if (!hasDataQueryActivity)
                {
                    await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, input));
                    _logger.LogInformation("[StartActivity] 已添加用户消息到ChatMessages");
                }
                else
                {
                    _logger.LogInformation("[StartActivity] 检测到DataQueryActivity，跳过AddMessage，避免重复保存用户消息");
                }

                await AddFirstOutput(flowNode, input);
                await SensitiveWordsCheck(input);
                AddSucceedNodeLog(flowNode);
                return;
            }

            foreach (var output in flowNode.Config.Outputs)
            {
                output.LiteralValue = (_chatRunDto.ChatArguments.ContainsName(output.Code) ? _chatRunDto.ChatArguments[output.Code] as string : null) ?? string.Empty;
                await AddNodeOutputArgument(flowNode, output.Code, output.LiteralValue);
            }
            string outputsStr = JsonConvert.SerializeObject(flowNode.Config.Outputs);

            // 如果工作流中包含DataQueryActivity，则跳过AddMessage，避免重复保存用户消息
            if (!hasDataQueryActivity)
            {
                await AddMessage(ChatRoleConstant.User, string.IsNullOrEmpty(input) ? string.Format(EventDataConstant.TextEvent, outputsStr) : input , string.IsNullOrEmpty(input));
                _logger.LogInformation("[StartActivity] 已添加用户消息到ChatMessages");
            }
            else
            {
                _logger.LogInformation("[StartActivity] 检测到DataQueryActivity，跳过AddMessage，避免重复保存用户消息");
            }

            await SensitiveWordsCheck(input, outputsStr);
            AddSucceedNodeLog(flowNode);
            return;
        }
    }
}
