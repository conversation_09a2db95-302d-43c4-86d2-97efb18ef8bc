using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.ContentReview;

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Plugin.Activities
{
    /// <summary>
    /// 开始节点
    /// </summary>
    public class StartActivity : SemanticKernelActivity
    {
        private readonly SensitiveWordsRepostory _senitiveWordsRepostory;
        private readonly SkillRepostory _skillRepostory;
        private readonly ContentReviewFactory _contentReviewFactory;
        private readonly MysoftMemoryCache _mysoftMemoryCache;

        public StartActivity(IServiceProvider serviceProvider, MysoftMemoryCache mysoftMemoryCache, ContentReviewFactory contentReviewFactory, SkillRepostory skillRepostory, SensitiveWordsRepostory sensitiveWordsRepostory, ILogger<StartActivity> logger) : base(serviceProvider, logger)
        {
            _senitiveWordsRepostory = sensitiveWordsRepostory;
            _skillRepostory = skillRepostory;
            _contentReviewFactory = contentReviewFactory;
            _mysoftMemoryCache = mysoftMemoryCache;
        }
        [KernelFunction(nameof(StartActivity))]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }

        /// <summary>
        /// 敏感词校验
        /// </summary>
        private async Task SensitiveWordsCheck(string input,string outputsStr = "")
        {
            SkillEntity skill = await _skillRepostory.GetAsync(f => f.SkillGUID == _chatRunDto.SkillGUID);
            if (skill == null)
            {
                return;
            }
            List<SensitiveWordsEntity> sensitiveWordsEntities = _mysoftMemoryCache.GetSensitiveWordsCache(skill.SpaceGUID.ToString());
            if(sensitiveWordsEntities == null)
            {
                sensitiveWordsEntities = await _senitiveWordsRepostory.GetListAsync(f => f.SpaceGUID == skill.SpaceGUID && f.IsEnabled == 1);
                _mysoftMemoryCache.SetSensitiveWordsCache(sensitiveWordsEntities, skill.SpaceGUID.ToString());
            }
            sensitiveWordsEntities.ForEach(f =>
            {
                var splitLines = f.SensitiveWords.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries).Select(w=> w.Trim()).ToList();
                if((!string.IsNullOrEmpty(input) && splitLines.Any(a => input.Contains(a))) || (!string.IsNullOrEmpty(outputsStr) && splitLines.Any(a => outputsStr.Contains(a))))
                {
                    TextEvent(f.Reply, _chatRunDto.IsStream).GetAwaiter().GetResult();
                    AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, f.Reply)).GetAwaiter().GetResult();
                    if (_chatRunDto.IsStream)
                    {
                        throw new CancelException();
                    }
                    else
                    {
                        throw new ValidateException(f.Reply);
                    }
                }
            });
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            var input = await _chatRunDto.GetArgumentValue<string>(KernelArgumentsConstant.Input);
            if (flowNode.Config.OutputType == "content")
            {
                _logger.LogInformation($"{nameof(StartActivity)}: {0}", input);
                await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, input));
                await AddFirstOutput(flowNode, input);
                await SensitiveWordsCheck(input);
                AddSucceedNodeLog(flowNode);
                return;
            }

            foreach (var output in flowNode.Config.Outputs)
            {
                output.LiteralValue = (_chatRunDto.ChatArguments.ContainsName(output.Code) ? _chatRunDto.ChatArguments[output.Code] as string : null) ?? string.Empty;
                await AddNodeOutputArgument(flowNode, output.Code, output.LiteralValue);
            }
            string outputsStr = JsonConvert.SerializeObject(flowNode.Config.Outputs);
            await AddMessage(ChatRoleConstant.User, string.IsNullOrEmpty(input) ? string.Format(EventDataConstant.TextEvent, outputsStr) : input , string.IsNullOrEmpty(input));
            await SensitiveWordsCheck(input, outputsStr);
            AddSucceedNodeLog(flowNode);
            return;
        }
    }
}
