using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 表单卡片
    /// </summary>
    public class FormActivity : SemanticKernelActivity, IEventHandler<FieldEvent>
    {
        public FormActivity(IServiceProvider serviceProvider, ILogger<FormActivity> logger) : base(serviceProvider, logger)
        {
        }
        private string _nextNodeGuid;
        private List<string> _inputCodes;
        [KernelFunction(nameof(FormActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync(string? nextNodeGuid = null, CancellationToken cancellationToken = default)
        {
            _nextNodeGuid = nextNodeGuid ?? string.Empty;
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;

            _chatRunDto.EventBus.Subscribe<FieldEvent>(this, flowNode.Code);
            _inputCodes = flowNode.Config?.Inputs?.Select(x => x.Code)?.ToList() ?? new List<string>();


            return flowNode;
        }
        protected override async Task ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.ExecuteActivityAsync(flowNode, cancellationToken);

            var cardParamDtos = flowNode.Config.Props;
            await _chatRunDto.OutputsArgumentParser();
            // 处理卡片标题
            var PageParamValueDto = new FormCardActivityBodyData
            {
                TemplateId = flowNode.Config.TemplateId,
                Props = flowNode.Config.Props,
                Outputs = flowNode.Config.Outputs,
                Title = flowNode.Config.Title,
                NodeCode = flowNode.Code,
                Inputs = flowNode.Config.Inputs
            };
            if (_chatRunDto.IsArgumentContainsKey(KernelArgumentsConstant.SystemKeywordWordsResult))
            {
                var wordsResults = JsonConvert.DeserializeObject<List<WordsResult>>(await _chatRunDto.GetArgumentValue<string>(KernelArgumentsConstant.SystemKeywordWordsResult));
                //wordsResults装换为map对象
                Dictionary<string, int> wordsResultsMap = wordsResults
                    .GroupBy(x => x.Words)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Max(x => GetLevelByProb(x.Prob, PageParamValueDto))
                    );
                PageParamValueDto.ProbResult = wordsResultsMap;

            }
            var pageCardDto = new FormCardActivityBody
            {
                Code = flowNode.Code,
                Next = _chatRunDto.GetNextNodeIdNotEnd(_nextNodeGuid),
                Data = PageParamValueDto
            };

            await DataEvent(pageCardDto, _chatRunDto.IsStream);
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);

            var title = flowNode.Config.Title;
            foreach (var item in flowNode.Config.Inputs)
            {
                title = title.Replace($"{{{{${item.Code}}}}}", item.LiteralValue);
            }
            var PageParamValueDto = new FormCardActivityBodyData
            {
                TemplateId = flowNode.Config.TemplateId,
                Props = flowNode.Config.Props,
                Outputs = flowNode.Config.Outputs,
                Title = title,
                NodeCode = flowNode.Code,
                Inputs = flowNode.Config.Inputs
            };
            var pageCardDto = new FormCardActivityBody
            {
                Code = flowNode.Code,
                Data = PageParamValueDto
            };
            var setting = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };
            await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.DataEvent, JsonConvert.SerializeObject(pageCardDto, setting)));
            await Task.CompletedTask;
        }

        private int GetLevelByProb(double prob, FormCardActivityBodyData interactiveCardData)
        {
            foreach (var rule in interactiveCardData.ProbRules)
            {
                if (prob >= rule.MinProb && prob < rule.MaxProb)
                {
                    return rule.Priority;
                }
            }
            // 如果没有找到匹配的范围，返回默认等级（例如 -1 表示未找到）
            return -1;
        }

        protected override async Task InitRefParams(FlowNode flowNode)
        {
            await base.InitRefParams(flowNode);
            if (flowNode.Config.Outputs.Count > 0)
            {
                flowNode._refParams.AddRange(flowNode.Config.Outputs);
            }
        }
        public async Task HandleAsync(FieldEvent @event, string code)
        {
            var flowNode = await _chatRunDto.GetFlowNode(code);
            if (flowNode._refParams.Exists(c => c.Value != null && c.Value.Content == @event.Key))
            {
                await StreamEvent(@event.ToString());
            }
        }
    }

    public class FormCardActivityBody
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "form";

        [JsonPropertyName("next")]
        public string Next { get; set; } = string.Empty;

        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public FormCardActivityBodyData Data { get; set; } = new FormCardActivityBodyData();
    }

    public class FormCardActivityBodyData
    {
        public string Id { get; set; }

        public string TemplateId { get; set; }
        public List<CardParamDto> Props { get; set; }
        public string NodeCode { get; set; }
        public Dictionary<string, int> ProbResult { get; set; } = new Dictionary<string, int>();
        public List<ProbRule> ProbRules { get; set; } = new List<ProbRule>() {
            new ProbRule(85, 0, 0),
            new ProbRule(95, 85, 1),
            new ProbRule(100, 95, 2)
        };

        // inputs
        public List<ParamDto> Inputs { get; set; }
        public string Title { get; set; }
        public List<ParamDto> Outputs { get; set; }

    }
}
