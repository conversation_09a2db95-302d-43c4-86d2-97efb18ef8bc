using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 文本生成卡片
    /// </summary>
    public class TextCardActivity : SemanticKernelActivity<string>, IEventHandler<FieldEvent>
    {
        private string inputExpression = "{{${0}}}";
        private string _nextNodeGuid;
        public TextCardActivity(IServiceProvider serviceProvider, ILogger<TextCardActivity> logger) : base(serviceProvider, logger)
        {
        }

        [KernelFunction(nameof(TextCardActivity))]
        [Description("文本生成卡片")]
        public new Task ExecuteAsync(string? nextNodeGuid = null, CancellationToken cancellationToken = default)
        {
            _nextNodeGuid = nextNodeGuid ?? string.Empty;
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;

            _chatRunDto.EventBus.Subscribe<FieldEvent>(this, flowNode.Code);

            return flowNode;
        }
        protected override async Task<string?> ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            _logger.LogInformation("文本生成卡片: {0}", flowNode.Config.Content);

            await base.ExecuteActivityAsync(flowNode, cancellationToken);

            await _chatRunDto.OutputsArgumentParser();
            var promptTemplate = await TemplateReplaceAsync(promptTemplateText: flowNode.Config.Content, arguments: GetRefArguments(flowNode));
            await DataEvent(InitBody(flowNode, promptTemplate), _chatRunDto.IsStream);
            return promptTemplate;
        }

        private TextCardActivityData InitBody(FlowNode flowNode, string promptTemplate)
        {
            var body = new TextCardActivityData()
            {
                Code = flowNode.Code,
                Next = _chatRunDto.GetNextNodeIdNotEnd(_nextNodeGuid),
                Data = new TextCardActivityBodyData
                {
                    Content = promptTemplate,
                    Inputs = flowNode.Config.Inputs,
                    Outputs = flowNode.Config.Outputs
                }
            };
            return body;
        }

        protected override async Task<string?> PostExecuteActivityAsync(FlowNode flowNode, string? result, CancellationToken cancellationToken)
        {
            result = await base.PostExecuteActivityAsync(flowNode, result, cancellationToken);
            result = await TemplateReplaceAsync(promptTemplateText: result ?? flowNode.Config.Content, arguments: GetRefArguments(flowNode));

            var promptTemplate = await TemplateReplaceAsync(promptTemplateText: flowNode.Config.Content, arguments: GetRefArguments(flowNode));
            var setting = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };
            await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.DataEvent, JsonConvert.SerializeObject(InitBody(flowNode, promptTemplate), setting)));
            return result;
        }

        public async Task HandleAsync(FieldEvent @event, string code)
        {
            var flowNode = await _chatRunDto.GetFlowNode(code);
            if (flowNode._refParams.Exists(c => c.Value != null && c.Value.Content == @event.Key))
            {
                await StreamEvent(@event.ToString());
            }
        }

        protected override async Task InitRefParams(FlowNode flowNode)
        {
            await base.InitRefParams(flowNode);
            if (flowNode.Config.Outputs.Count > 0)
            {
                flowNode._refParams.AddRange(flowNode.Config.Outputs);
            }
        }
    }
    public class TextCardActivityData
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "text";

        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        [JsonPropertyName("next")]
        public string Next { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public TextCardActivityBodyData Data { get; set; } = new TextCardActivityBodyData();
    }

    public class TextCardActivityBodyData
    {
        [JsonPropertyName("inputs")]
        public List<ParamDto> Inputs { get; set; } = new List<ParamDto>();

        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("outputs")]
        public List<ParamDto> Outputs { get; set; } = new List<ParamDto>();
    }
}
