using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.Activities.Api
{
    public class ThirdPartyApiActivity : ApiActivity
    {
        private readonly IThirdPartyApiService _thirdPartyApiService;
        private readonly PluginRepostory _pluginRepostory;
        private readonly PluginMetadataRepostory _pluginMetadataRepostory;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
  
        public ThirdPartyApiActivity(IServiceProvider serviceProvider, ILogger<ThirdPartyApiActivity> logger
        , IThirdPartyApiService thirdPartyApiService, PluginRepostory pluginRepostory, MysoftMemoryCache mysoftMemoryCache
        , PluginMetadataRepostory pluginMetadataRepostory, IMysoftContextFactory mysoftContextFactory,IConfigurationService configurationService) 
            : base(serviceProvider, logger, mysoftMemoryCache, configurationService, mysoftContextFactory, pluginRepostory, pluginMetadataRepostory)
        {
            _thirdPartyApiService = thirdPartyApiService;
            _pluginRepostory = pluginRepostory;
            _pluginMetadataRepostory = pluginMetadataRepostory;
            _mysoftMemoryCache = mysoftMemoryCache;
        }

        [KernelFunction(nameof(ThirdPartyApiActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            try
            {
                await _chatRunDto.InputsArgumentParser(flowNode);
                OpenApiRequestOptions openApiRequestOptions = await GetOpenApiRequestOptions(flowNode);
                
                if (flowNode.Config.Async)
                {
                    RequestApi(openApiRequestOptions);
                    return;
                }
                
                var result = await RequestApi(openApiRequestOptions);
                await ReadValue(flowNode, result, _chatRunDto.IsStream && flowNode.Config.AsMessage);
                await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            }
            catch(PluginException pluginEx)
            {
                throw;
            }
            catch (Exception e)
            {
                string errMsg = string.Format(ErrMsgConst.Plugin_ErrMsg, flowNode.Config.ToolId, e.Message);
                throw new PluginException(errMsg, e);
            }
        }
        
        private async Task ReadValue(FlowNode flowNode, FunctionResult result, bool isStream)
        {
            object obj = result.GetValue<RestApiOperationResponse>()!.Content!;
            if (obj is Stream)
            {
                await AddFirstOutput(flowNode, await ReadStream((Stream)obj, isStream));
                await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, flowNode.Config.Outputs.First().LiteralValue));
            }
            else
            {
                var data = obj == null ? "" : JsonConvert.DeserializeObject(obj.ToString());
                await GenerateOutputValue(flowNode, data);
                
                // 推送Api事件 TODO待确认是否移除
                // flowNode.Config.Outputs.ForEach(async f =>
                // {
                //     FieldEvent fieldEvent = new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, f.Code), f.LiteralValue);
                //     await _chatRunDto.EventBus.PublishAsync(fieldEvent);
                // });
            }
        }
        
        private async Task<string> ReadStream(Stream result, bool isStream)
        {
            using var reader = new StreamReader(result);
            StringBuilder sb = new StringBuilder();
            
            while (!reader.EndOfStream)
            {
                var line = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(line) || line  == "data: [DONE]")
                {
                    continue;
                }
                if (line.StartsWith("data: "))
                {
                    line = line.Substring(6);
                }
                _logger.LogInformation("拿到返回结果了：{0}", line);
                var data = JsonConvert.DeserializeObject<ThirdPartyApiResultData>(line);
                if (data == null)
                {
                    continue;
                }

                foreach (var choice in data.Choices)
                {
                    if(isStream && choice.FinishReason == "content_revoke")
                    {
                        _logger.LogInformation("第三方接口撤回消息");
                        await ContentReviewEvent("");
                        sb = new StringBuilder();
                    }
                    await TextEvent(choice.Delta.Content, isStream);
                    sb.Append(choice.Delta.Content);
                }
            }

            return await Task.FromResult(sb.ToString());
        }
        

        
    }
    
    
    public class ThirdPartyApiResultData
    {
        [JsonProperty("id")]
        public string Id { get; set; } = string.Empty;

        [JsonProperty("created")]
        public int Created { get; set; }
        
        [JsonProperty("object")]
        public string Object { get; set; } = string.Empty;

        [JsonProperty("choices")]
        public List<ThirdPartyApiResultChoice> Choices { get; set; } = new List<ThirdPartyApiResultChoice>();
    }
    
    public class ThirdPartyApiResultChoice
    {
        [JsonProperty("index")]
        public int Index { get; set; }

        [JsonProperty("delta")]
        public ThirdPartyApiResultDelta Delta { get; set; } = new ThirdPartyApiResultDelta();
        
        [JsonProperty("finish_reason")]
        public string FinishReason { get; set; } = string.Empty;

    }
    
    public class ThirdPartyApiResultDelta
    {
        [JsonProperty("content")]
        public string Content { get; set; } = string.Empty;

    }
    
}
