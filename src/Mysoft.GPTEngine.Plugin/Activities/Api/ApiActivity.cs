using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Mysoft.GPTEngine.Plugin.Activities.Api
{
    /// <summary>
    /// API
    /// </summary>
    public class ApiActivity : SemanticKernelActivity
    {
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly PluginRepostory _pluginRepostory;
        private readonly PluginMetadataRepostory _pluginMetadataRepostory;
        private readonly IConfigurationService _configurationService;

        public ApiActivity(IServiceProvider serviceProvider, ILogger<ApiActivity> logger, MysoftMemoryCache mysoftMemoryCache, IConfigurationService configurationService,
            IMysoftContextFactory mysoftContextFactory, PluginRepostory pluginRepostory, PluginMetadataRepostory pluginMetadataRepostory) : base(serviceProvider, logger)
        {
            _mysoftMemoryCache = mysoftMemoryCache;
            _mysoftContextFactory = mysoftContextFactory;
            _pluginRepostory = pluginRepostory;
            _pluginMetadataRepostory = pluginMetadataRepostory;
            _configurationService = configurationService;
        }
        public async Task<string> GenerateContentText(FlowNode flowNode)
        {
            await _chatRunDto.InputsArgumentParser(flowNode);
            var inputs = flowNode.Config.Inputs;
            var result = new ExpandoObject();
            foreach (var input in inputs)
            {
                result.TryAdd(input.Code, input.LiteralValue);
            }
            return await Task.FromResult(JsonConvert.SerializeObject(result));
        }

        public async Task<FunctionResult> RequestApi(OpenApiRequestOptions openApiRequestOptions)
        {
            return await OpenApiServiceHelper.RequestApi(openApiRequestOptions, _mysoftMemoryCache, _configurationService, _httpContextAccessor);
        }

        public async Task GenerateOutputValue(FlowNode flowNode, object data)
        {
            var outputs = flowNode._outParams;
            if (outputs == null || outputs.Count == 0) return;
            try
            {
                var jsonValue = JToken.Parse(JsonConvert.SerializeObject(data));
                if (jsonValue is JArray && outputs.Count == 1)
                {
                    outputs[0].LiteralValue = data?.ToString() ?? string.Empty;
                    return;
                }

                if (jsonValue is JObject && jsonValue.Children().Count() > 0)
                {
                    var values = jsonValue.Children().Where(x => x is JProperty).ToDictionary(x => ((JProperty)x).Name, x => ((JProperty)x).Value);
                    foreach (var output in outputs)
                    {
                        output.LiteralValue = values.ContainsKey(output.Code) ? values[output.Code].ToString() : string.Empty;
                    }
                }

                if (outputs.Count == 1 && jsonValue is JObject == false && jsonValue is JArray == false)
                {
                    outputs[0].LiteralValue = data?.ToString() ?? string.Empty;
                }


            }
            catch(Exception ex)
            {
                if (outputs.Count == 1)
                {
                    outputs.First().LiteralValue = data?.ToString() ?? string.Empty;
                    return;
                }
            }
        }
        
        
        
        
        
        public async Task<OpenApiRequestOptions> GetOpenApiRequestOptions(FlowNode flowNode)
        {
            Verify.NotNull(flowNode.Config.ToolId);
            PluginEntity pluginEntity = await _pluginRepostory.GetAsync(f => f.PluginGUID == flowNode.Config.PluginId);
            PluginMetadataEntity pluginMetadataEntity = await _pluginMetadataRepostory.GetAsync(f => f.PluginGUID == flowNode.Config.PluginId);
            if (pluginEntity == null || pluginMetadataEntity == null) return await Task.FromResult(new OpenApiRequestOptions());
            
            
            var arguments = new KernelArguments();
            flowNode.Config.Inputs.ForEach(item => { arguments[item.Code] = item.LiteralValue; });

            OpenApiRequestOptions openApiRequestOptions = new OpenApiRequestOptions()
            {
                Metadata = pluginMetadataEntity.Metadata,
                CreateTokenUrl = pluginEntity.CreateTokenUrl,
                AuthMode = pluginEntity.AuthMode,
                Arguments = arguments,
                ParamListStr = pluginEntity.ParamList,
                Path = flowNode.Config.ToolId,
                OperationId = flowNode.Config.OperationId,
                MysoftContextDto = _mysoftContextFactory.GetMysoftContext()
            };
            return await Task.FromResult(openApiRequestOptions);
        }
        
        
    }
}
