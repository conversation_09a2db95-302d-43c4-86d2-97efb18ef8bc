using System;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.Activities.Api
{
    /// <summary>
    /// 明源ERP API
    /// </summary>
    public class MysoftIPassApiActivity : ApiActivity
    {
        private readonly IMysoftIPassApiService _mysoftIPassApiService;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;
        public MysoftIPassApiActivity(IServiceProvider serviceProvider, ILogger<MysoftApiActivity> logger
        , MysoftConfigurationDomain mysoftConfigurationDomain, IMysoftIPassApiService mysoftIPassApiService
        , PluginRepostory pluginRepostory, MysoftMemoryCache mysoftMemoryCache
        , PluginMetadataRepostory pluginMetadataRepostory, IMysoftContextFactory mysoftContextFactory,IConfigurationService configurationService) 
            : base(serviceProvider, logger, mysoftMemoryCache, configurationService, mysoftContextFactory, pluginRepostory, pluginMetadataRepostory)
        {
            _mysoftIPassApiService = mysoftIPassApiService;
            _mysoftConfigurationDomain = mysoftConfigurationDomain;
        }

        [KernelFunction(nameof(MysoftIPassApiActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            {
                var mipInfo = _mysoftConfigurationDomain.GetMipInfo();
                OpenApiRequestOptions openApiRequestOptions = await GetOpenApiRequestOptions(flowNode);
                openApiRequestOptions.ServerUrlOverride = new Uri(mipInfo.ServiceUrl);
                openApiRequestOptions.MipInfoDto = mipInfo;
                openApiRequestOptions.AuthMode = 3;
                object? data;
                try
                {
                    if (flowNode.Config.Async)
                    {
                        RequestApi(openApiRequestOptions);
                        return;
                    }
                    
                    var result = await RequestApi(openApiRequestOptions);

                    data = JsonConvert.DeserializeObject(result.ToString());
                    if (data == null) return;
                }
                catch (Exception e)
                {
                    string errMsg = string.Format(ErrMsgConst.Plugin_ErrMsg, openApiRequestOptions.Path, e.Message);
                    throw new PluginException(errMsg, e);
                }
                await GenerateOutputValue(flowNode, data);
                
                // 推送Api事件 TODO待确认是否移除
                // flowNode.Config.Outputs.ForEach(async f =>
                // {
                //     FieldEvent fieldEvent = new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, f.Code), f.LiteralValue);
                //     await _chatRunDto.EventBus.PublishAsync(fieldEvent);
                // });
            
                await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            }

        }
    }
}
