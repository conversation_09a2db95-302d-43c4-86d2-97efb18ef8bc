using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.System;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using HandlebarsDotNet;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Newtonsoft.Json;
using Mysoft.GPTEngine.Common.Helper;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;
using Mysoft.GPTEngine.Domain.Shared.Constants;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 赋值录入
    /// </summary>
    public class ContentAnalysisActivity : SemanticKernelActivity
    {
        public ContentAnalysisActivity(IServiceProvider serviceProvider, ILogger<ContentAnalysisActivity> logger) : base(serviceProvider, logger)
        {
        }

        [KernelFunction(nameof(ContentAnalysisActivity))]
        [Description("文本模板")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await ContentAnalysis(flowNode, cancellationToken);
        }

        private async Task<string> ContentAnalysis(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await _chatRunDto.InputsArgumentParser(flowNode);
            var arguments = await _chatRunDto.GetNodeInputArgument(flowNode);
            Dictionary<string, Object> data = new Dictionary<string, Object>();
            foreach (var arg in arguments)
            {
                parseJsonString(data, arg.Key, arg.Value?.ToString() ?? string.Empty);
            }
            var content = flowNode.Config.Content;
            var template = Handlebars.Compile(content);
            string result = template(data);
            result = HttpUtility.HtmlDecode(result);
            await AddFirstOutput(flowNode, result);
            // 推送Api事件
            flowNode.Config.Outputs.ForEach(async f =>
            {
                FieldEvent fieldEvent = new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, f.Code), f.LiteralValue);
                await _chatRunDto.EventBus.PublishAsync(fieldEvent);
            });
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            return "";
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
        }

        private void parseJsonString(Dictionary<string, Object> data, string key, string value)
        {
            if (IsValidJsonList(value))
            {
                data.Add(key, JsonConvert.DeserializeObject<List<Dictionary<string, Object>>>(value.ToString()));
            }
            else if (JsonValidateHelper.IsValidJson(value))
            {
                data.Add(key, JsonConvert.DeserializeObject(value));
            }
            else
            {
                data.Add(key, value);
            }
        }

        private bool IsValidJsonList(string json)
        {
            try
            {
                List<Dictionary<string, Object>> result = JsonConvert.DeserializeObject<List<Dictionary<string, Object>>>(json.ToString());
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

    }
}
