using DocumentFormat.OpenXml.Math;
using Microsoft.Extensions.FileSystemGlobbing.Internal;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Plugin.Activities.Card;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.Plugin.System;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;

namespace Mysoft.GPTEngine.Plugin.Activities.Knowledge
{
    public class KnowledgeActivity : SemanticKernelActivity
    {
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly IMilvusMemoryDomainService _milvusMemoryDomainService;
        private string? _input;
        private string? _knowledeCodes;
        private bool _enableRecommend;
        private int _limit;
        private double _minRelevanceScore;
        private int _pattern;
        public KnowledgeActivity(IKnowledgeDomainService knowledgeDomainService, IMilvusMemoryDomainService milvusMemoryDomainService,
            IServiceProvider serviceProvider, ILogger<KnowledgeActivity> logger) : base(serviceProvider, logger)
        {
            _milvusMemoryDomainService = milvusMemoryDomainService;
            _knowledgeDomainService = knowledgeDomainService;
        }

        [KernelFunction(nameof(KnowledgeActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync([Description("问题")] string? input = null
            , [Description("知识库编码集合")] string? knowledeCodes = null
            , [Description("启用相识问")] bool enableRecommend = true
            , [Description("返回条数")] int limit = 3
            , [Description("最低权值")] double minRelevanceScore = 0.6
            , [Description("分析模式")] int pattern = 0
            , [Description("取消令牌")] CancellationToken cancellationToken = default)
        {
            _input = input;
            _knowledeCodes = knowledeCodes;
            _enableRecommend = enableRecommend;
            _limit = limit;
            _minRelevanceScore = minRelevanceScore;
            _pattern = pattern;
            return base.ExecuteAsync(cancellationToken);
        }

        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;

            return flowNode;
        }

        protected override async Task ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await KnowledgeDelegate(flowNode, cancellationToken);
        }

        protected override async Task<ChatHistory> ChatMessages(FlowNode flowNode, string promptGuid)
        {
            var questionInput = flowNode._questionInput;
            var arguments = new KernelArguments { { nameof(questionInput), questionInput } };
            var promptTemplateText = EmbeddedResource.Read(EmbeddedResource.KnowledgePlugin_QuestionOptimization);
            var systemPrompy = await TemplateRenderAsync(promptTemplateText: promptTemplateText, arguments: arguments);
            _logger.LogInformation("大模型调用：{0}", systemPrompy.Content);
            var chatHistory = await CreateChatHistory(flowNode, systemMessage: systemPrompy.Content, history: 3);
            await AddMemoriesChatHistory(flowNode, chatHistory);
            if (string.IsNullOrWhiteSpace(flowNode._input) == false)
            {
                chatHistory.AddUserMessage(flowNode._input);
            }
            return chatHistory;
        }

        private async Task<string> KnowledgeDelegate(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await _chatRunDto.InputsArgumentParser(flowNode);
            var inputs = flowNode.Config.Inputs;
            _input = inputs.FirstOrDefault(x => x.Code == "input")?.LiteralValue ?? _input;
            //指代消除
            if (flowNode.Config.IsCOR)
            {
                var optimizationQuestionDto = await getOptimizationQuestionDto(_input);
                if (optimizationQuestionDto.Context != null && optimizationQuestionDto.Context.Count > 0)
                {
                    var questionInput = JsonConvert.SerializeObject(optimizationQuestionDto);
                    var arguments = new KernelArguments { { nameof(questionInput), questionInput } };
                    flowNode._input = _input;
                    flowNode._questionInput = questionInput;
                    flowNode._serviceId = _chatRunDto.ModelInstance.InstanceCode;
                    _input = await ChatCompletionExec(flowNode, systemPromptName: EmbeddedResource.KnowledgePlugin_QuestionOptimization, input: _input, arguments: arguments, serviceId: _chatRunDto.ModelInstance.InstanceCode, cancellationToken: cancellationToken);
                }
            }
            if (_input == null) return "";
            var knowledgeQueryEmbeddingMap = await _knowledgeDomainService.GetKnowledgeQueryEmbeddingMap(_knowledeCodes, _input, _kernel).ConfigureAwait(false);
            await CreateQuestion(_chatRunDto, knowledgeQueryEmbeddingMap, _limit, _minRelevanceScore, cancellationToken).ConfigureAwait(false);

            var topSectionList = await _knowledgeDomainService.GetQueryTopResult(knowledgeQueryEmbeddingMap, _limit, _minRelevanceScore).ConfigureAwait(false);

            var totalIdList = topSectionList.OrderByDescending(x => x.score).Take(_limit).Select(x => x.id).ToList();
            //拿到替换了图片信息的切片以及占位符文件
            ImageUrlDto imageUrl = await _knowledgeDomainService.GetKnowledgeFileSections(totalIdList).ConfigureAwait(false);

            if (imageUrl.replaceDtos.Count > 0)
            {
                _chatRunDto.ReplaceDtos = imageUrl.replaceDtos;
                await ReplaceEvent(imageUrl.replaceDtos, _chatRunDto.IsStream).ConfigureAwait(false);
            }
            var knowledgeFileSections = imageUrl.knowledgeFileSectionDtos;

            //格式化返回结果
            var result = FormatResult(knowledgeFileSections, _pattern);

            //创建会话节点知识库日志
            await _chatRunDto.AddKnowledgeNodeLog(knowledgeFileSections, topSectionList, _input).ConfigureAwait(false);

            if (flowNode.Config.AsSource && knowledgeFileSections != null && knowledgeFileSections.Count != 0 && _chatRunDto.IsStream)
            {
                await SourceEvent(knowledgeFileSections);
            }
            _logger.LogInformation("知识库-简单检索:问题:{0};结果：{1}", _input, result);

            await AddFirstOutput(flowNode, result);
            // 推送Api事件
            flowNode.Config.Outputs.ForEach(async f =>
            {
                FieldEvent fieldEvent = new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, f.Code), f.LiteralValue);
                await _chatRunDto.EventBus.PublishAsync(fieldEvent);
            });
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            return "";
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
        }

        private async Task SourceEvent(List<KnowledgeFileSectionDto> knowledgeFileSections)
        {
            if (knowledgeFileSections == null || knowledgeFileSections.Count == 0) return;
            var source = new KnowledgeSourceDto();

            var files = knowledgeFileSections
                .GroupBy(x => new { x.FileName, x.FileSize, x.FileType })
                .Select(group => group.First())
                .ToList();

            source.Data = files.Select(x => new KnowledgeSourceDataDto { Name = x.FileName, Type = x.FileType, Url = x.FileUrl, Size = $"{(x.FileSize / 1024)}k" }).ToList();
            //判断是否需要输出文档  不需要的话不用输出

            await DataEvent(source, _chatRunDto.IsStream);
            var setting = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };
            var data = JsonConvert.SerializeObject(source, setting);
            await AddMessage(AuthorRole.Assistant.Label, string.Format(EventDataConstant.DataEvent, data));
        }

        private string FormatResult(List<KnowledgeFileSectionDto> knowledgeFileSections, int pattern)
        {
            string result = "";
            //TODO 如果拉取到了同步过来的数据，特殊处理返回内容
            if (knowledgeFileSections.Exists(e => e.FileSourceEnum == (int)FileResourceEnum.OpenMingyy))
            {
                result = GenerateRes(knowledgeFileSections);
            }
            else
            {
                if (pattern == 1)
                {
                    var mapList = knowledgeFileSections.Select(x =>
                    {
                        var map = new Dictionary<string, object>();
                        map.Add("content", x.Content);
                        map.Add("title", x.ParagraphTitle);
                        map.Add("url", x.FileUrl);
                        var metadataMap = new Dictionary<string, string>();
                        if (!String.IsNullOrWhiteSpace(x.Metadata))
                        {
                            metadataMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.Metadata);
                        }
                        map.Add("metadata", metadataMap);
                        return map;
                    });
                    result = JsonConvert.SerializeObject(mapList);
                }
                else
                {
                    result = knowledgeFileSections?.Count > 0 ? string.Join("\r\n", knowledgeFileSections.Select(x => x.Content)) : string.Empty;
                }

            }

            return result;
        }

        /// <summary>
        /// 组装数据
        /// </summary>
        /// <param name="knowledgeFileSectionDtos"></param>
        /// <returns></returns>
        private string GenerateRes(List<KnowledgeFileSectionDto> knowledgeFileSectionDtos)
        {
            string result = "";
            foreach (var item in knowledgeFileSectionDtos)
            {
                if (item.FileSourceEnum == (int)FileResourceEnum.OpenMingyy)
                {
                    result +=
@$"```yaml
title: ""{item.FileName.Replace(".txt", "")}""
source: ""{item.FileUrl}""
content: ""{item.Content}""
```
";
                }
                else if (item.FileSourceEnum == (int)FileResourceEnum.GPT)
                {
                    result +=
@$"```yaml
content: ""{item.Content}""
```
";
                }
            }

            return result;
        }

        public async Task<ProblemOptimizationDto> getOptimizationQuestionDto(string input)
        {
            var problemOptimizationDto = new ProblemOptimizationDto();
            if (_chatRunDto.Nodes.Count == 0) return await Task.FromResult(problemOptimizationDto);

            var nodeGUID = _chatRunDto.Chat.CurrentNodeGUID;
            var nodeLogs = _chatRunDto.NodeLogs.Where(x => x.NodeGUID == nodeGUID && !x.BatchGUID.Equals("00000000-0000-0000-0000-000000000000")).OrderByDescending(x => x.Index).Take(5).OrderBy(x => x.Index).ToList();
            if (nodeLogs.Count == 0) return await Task.FromResult(problemOptimizationDto);
            List<ProblemOptimizationDto.HistoryContextDto> contextDtos = new List<ProblemOptimizationDto.HistoryContextDto>();
            foreach (var nodeLog in nodeLogs)
            {
                if (nodeLog.BatchGUID.ToString().Equals("00000000-0000-0000-0000-000000000000"))
                {
                   continue;
                }
                var historyContextDto = new ProblemOptimizationDto.HistoryContextDto();
                historyContextDto.Sender = nodeLog.Inputs;
                historyContextDto.Content = nodeLog.Outputs;
                contextDtos.Add(historyContextDto);
            }
            problemOptimizationDto.Query = input;
            problemOptimizationDto.Context = contextDtos;
            return await Task.FromResult(problemOptimizationDto);
        }

        private async Task CreateQuestion(ChatRunDto chatRunDto, Dictionary<string, ReadOnlyMemory<float>> knowledgeQueryEmbeddingMap, int limit, double minRelevanceScore, CancellationToken cancellationToken = default)
        {
            List<MemoryQueryResult> questionQueryResults = new List<MemoryQueryResult>();
            foreach (var knowledgeQueryEmbedding in knowledgeQueryEmbeddingMap)
            {
                //查询问题
                await foreach (var answer in this._milvusMemoryDomainService.SearchAsync(knowledgeQueryEmbedding.Key, knowledgeQueryEmbedding.Value, limit, minRelevanceScore, false, (int)SourceTypeEnum.Question, cancellationToken))
                {
                    questionQueryResults.Add(answer);
                }
            }

            List<Guid> questionGUIDs = questionQueryResults.Select(s => Guid.Parse(s.Metadata.Id)).ToList();
            // 根据问题id查询问题对象
            var questionDtos = await _knowledgeDomainService.GetQuestionByQuestionGUID(questionGUIDs).ConfigureAwait(false);
            //查到了问题就直接生成推荐问
            if (questionDtos.Count > 0)
            {
                var recommend = new KnowledgeRecommendDto
                {
                    Data = questionDtos.Select(s => s.Question).Distinct().ToList()
                };
                await DataEvent(recommend, chatRunDto.IsStream);
            }
        }
    }
}
