using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Plugin.Activities.Card;
using Mysoft.GPTEngine.Plugin.Activities.Llm;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;

namespace Mysoft.GPTEngine.Plugin.Activities.Analysis
{
    public class ImageAnalysisActivity : LlmActivity
    {
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        private readonly ContentReviewFactory _contentReviewFactory;
        private readonly ModelRepostory _modelRepostory;
        public readonly string content = "content";
        public readonly string multimodal = "multimodal";
        public readonly string ocr = "ocr";
        private PromptDto _promptDto;

        #region OCR重试相关常量
        private const int MaxRetryCount = 10; // 最大重试次数
        private const int InitialDelaySeconds = 1; // 初始延迟秒数
        private const int MinJitterMs = 500; // 最小抖动毫秒数
        private const int MaxJitterMs = 2000; // 最大抖动毫秒数
        private const string ResourceWaitingMessage = "当前请求较多，小助手正在为您安排资源，预计 1 分钟内完成计算，感谢您的耐心等待~ \n"; // 资源等待消息
        #endregion
        public ImageAnalysisActivity(IServiceProvider serviceProvider, IKnowledgeDomainService knowledgeDomainService, ModelRepostory modelRepostory, ContentReviewFactory contentReviewFactory,
            ModelInstanceRepostory modelInstanceRepostory, ILogger<LlmActivity> logger) : base(serviceProvider, logger, contentReviewFactory, knowledgeDomainService)
        {
            _knowledgeDomainService = knowledgeDomainService;
            _modelInstanceRepostory = modelInstanceRepostory;
            _modelRepostory = modelRepostory;
            _contentReviewFactory = contentReviewFactory;
        }

        [KernelFunction(nameof(ImageAnalysisActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }

        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;
            await FileArgumentCheck(flowNode);
            await ImageChatCheck(flowNode);
            return flowNode;
        }

        protected override async Task<LlmGetStreamingChatMessage?> ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            // 视觉识别
            if (flowNode.Config.RecognizeType == "ocr")
            {
                await ExecImageAnalysisUseOcr(flowNode, cancellationToken);
            }
            else
            {
                // 多模态识别，files→<BinaryContent>→LLM
                await base.ExecuteActivityAsync(flowNode, cancellationToken);
            }
            
            await base.PostExecuteActivityAsync(flowNode, null, cancellationToken);
            return await Task.FromResult<LlmGetStreamingChatMessage?>(default);
        }

        private async Task<string> ExecImageAnalysisUseOcr(FlowNode flowNode, CancellationToken cancellationToken)
        {
            if (flowNode.Config.Pattern == (int)DocumentAnalysisTypeEnum.InformationExtraction)
            {
                // OCR-信息提取，files→loop[OCR]→imagecontents→LLM
                await OcrInformationExtraction(flowNode, cancellationToken);
            }
            else
            {
                // OCR-文本读取，files→loop[OCR]→imagecontents
                int minProb = await getMinProb(flowNode.Config.OcrService);
                var fileIds = GetFileGuidList(flowNode);

                var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds, 0);
                var ocrRecognizeService = _kernel.GetRequiredService<IOcrRecognizeService>(flowNode.Config.OcrService);
                var result = "";
                foreach (var document in documents)
                {
                    // 先转换为 WebP， 替换原始内容为 WebP 数据
                    byte[] webpData = ImageExtractionHelper.ConvertToWebPAsync(document.FileName, document.FileContent);
                    if (webpData != null)
                    {
                        document.FileContent = webpData;
                    }

                    // 使用重试机制调用OCR服务
                    var imgContent = await ExecuteOcrWithRetry(ocrRecognizeService, new OcrRequest{ Body = document.FileContent, ocrCode = flowNode.Config.OcrService }, flowNode, cancellationToken);
                    if (!imgContent.Success || string.IsNullOrEmpty(imgContent.Content))
                    {
                        _logger.LogError("Ocr识别失败：{0}", flowNode.Config.OcrService);
                        continue;
                    }
                    string imgContentContent = handleMinProd(imgContent, minProb);
                    if (imgContent.WordsResult != null && imgContent.WordsResult.Count > 0)
                    {
                        _chatRunDto.AddWordsResult(imgContent.WordsResult);
                    }
                    result = result + imgContentContent + "\r\n";
                }

                List<ParamDto> outputs = flowNode.Config.Outputs;
                foreach (var item in outputs)
                {
                    if (item.Code == content)
                    {
                        item.LiteralValue = result;
                    }
                    
                    await _chatRunDto.EventBus.PublishAsync(new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, item.Code), item.LiteralValue));
                }
            }
            
            return "";
        }

        protected override async Task<ChatHistory> ChatMessages(FlowNode flowNode, string promptGuid)
        {
            var chatMessages = new ChatHistory();
            if (flowNode.Config.RecognizeType == "ocr" && flowNode.Config.Pattern == (int)DocumentAnalysisTypeEnum.InformationExtraction)
            {
                var promptTemplateText = EmbeddedResource.Read(EmbeddedResource.DocumentAnalysisPlugin_FixedGenerate);
                var systemPrompy = await TemplateRenderAsync(promptTemplateText: promptTemplateText, arguments: flowNode._arguments);
                _logger.LogInformation("大模型调用：{0}", systemPrompy.Content);
                chatMessages = await CreateChatHistory(flowNode, systemMessage: systemPrompy.Content, history: 3);
                await AddMemoriesChatHistory(flowNode, chatMessages);
                if (string.IsNullOrWhiteSpace(flowNode._input) == false)
                {
                    chatMessages.AddUserMessage(flowNode._input);
                }
            }
            else
            {
                var skillOrchestration = _chatRunDto.SkillOrchestration;
                var prompt = skillOrchestration.Prompts.FirstOrDefault(x => x.Id == Guid.Parse(promptGuid));
                _promptDto = prompt;
                Verify.NotNull(prompt);

                var arguments = await _chatRunDto.GetNodeInputArgument(flowNode);

                var promptTemplate = await TemplateRenderAsync(promptTemplateText: prompt.PromptTemplate, arguments: arguments);
                // 提示词是参数列表类型时，特殊处理输出格式
                if (prompt.OutputType == PromptOutputTypeEnum.Variables)
                {
                    var outputs = flowNode.Config.Outputs;
                    string formatStr = "";
                    TraverseParamDtos(outputs, ref formatStr);
                    promptTemplate.Content += GetOutputPrompt(formatStr);
                }

                var fileIds = GetFileGuidList(flowNode);
                if (fileIds.Any())
                {
                    string serviceId = prompt.ModelInstanceCode;
                    flowNode._serviceId = string.IsNullOrEmpty(serviceId) ? ChatCompletionTypeDto.ImageComprehend : serviceId;
                }

                var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds, 0);
                var messageCollection = new ChatMessageContentItemCollection();
                foreach (var document in documents)
                {
                    var image = new ImageContent()
                    {
                        Metadata = new Dictionary<string, object?>()
                    {
                        { nameof(document.FileName), document.FileName },
                        { nameof(document.FileContent), document.FileContent }
                    }
                    };
                    messageCollection.Add(image);
                }
                messageCollection.Add(new TextContent { Text = promptTemplate.Content } );

                chatMessages.AddUserMessage(messageCollection);
            }

            return chatMessages;
        }

        protected override async Task<LlmGetStreamingChatMessage?> PostExecuteActivityAsync(FlowNode flowNode, LlmGetStreamingChatMessage? result, CancellationToken cancellationToken)
        {
            if (flowNode.Config.RecognizeType == "ocr" && flowNode.Config.Pattern != (int)DocumentAnalysisTypeEnum.InformationExtraction)
            {
                AddSucceedNodeLog(flowNode);
            }
            return await base.PostExecuteActivityAsync(flowNode, result, cancellationToken);
        }


        public async Task<string> OcrInformationExtraction(FlowNode flowNode, CancellationToken cancellationToken)
        {

            //todo：获取上下文中识别的阈值 默认 70
            int minProb = await getMinProb(flowNode.Config.OcrService);
            string str = "";
            // 组装json scheme
            TraverseParamDtos(flowNode.Config.Outputs, ref str);
            var fileIds = GetFileGuidList(flowNode);
            var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds, 0);

            if (documents == null || documents.Count == 0)
            {
                return "";
            }
            var ocrRecognizeService = _kernel.GetRequiredService<IOcrRecognizeService>(flowNode.Config.OcrService);
            var imageContent = "";
            foreach (var document in documents)
            {
                // 先转换为 WebP，替换原始内容为 WebP 数据
                byte[] webpData = ImageExtractionHelper.ConvertToWebPAsync(document.FileName, document.FileContent);
                if (webpData != null)
                {
                    document.FileContent = webpData;
                }

                // 使用重试机制调用OCR服务
                var imgContent = await ExecuteOcrWithRetry(ocrRecognizeService, new OcrRequest { Body = document.FileContent,ocrCode = flowNode.Config.OcrService }, flowNode, cancellationToken);
                //TODO：获取imgContent中WordsResult数组中所有的值 对超过阈值的数据进行组装，空格分割
                //TODO：获取所有的WordsResult的值 放入上下文runDto中WordsResult的对象中
                if (!imgContent.Success)
                {
                    _logger.LogError("OCR图片识别失败：{0}", document.FileName);
                    continue;
                }

                if (imgContent.WordsResult != null && imgContent.WordsResult.Count > 0)
                {
                    _chatRunDto.AddWordsResult(imgContent.WordsResult);
                }
                imageContent = imageContent + "\r\n" + handleMinProd(imgContent, minProb);
            }
            if (string.IsNullOrEmpty(imageContent.Replace("\r\n", "")))
            {
                return "";
            }
            var publishEventConfig = new PublishEventConfig()
            {
                PublishTextEvent = true,
                PublishLlmResultEvent = true,
                ResponseFormatType = flowNode.Config.Pattern == 0 ? ResponseFormatType.Text : ResponseFormatType.JsonObject,
                ParamCodes = flowNode.Config.Pattern == 0 ? null : flowNode.Config.Outputs.Select(x => x.Code).ToList()
            };
            var arguments = new KernelArguments { { nameof(flowNode.Config.Outputs), str } };
            flowNode._input = imageContent;
            flowNode._serviceId = _chatRunDto.ModelInstance.InstanceCode;
            flowNode._publishEventConfig = publishEventConfig;
            flowNode._arguments = arguments;
            // 使用立即执行对话完成
            var res = await ChatCompletionExec(flowNode, systemPromptName: EmbeddedResource.DocumentAnalysisPlugin_FixedGenerate, 
                input: imageContent, arguments, serviceId: _chatRunDto.ModelInstance.InstanceCode, publishEventConfig: publishEventConfig, 
                cancellationToken: cancellationToken);
            return res;
        }

        protected override async Task LlmResultHandleAsync(LlmResultEvent @event)
        {
            var result = @event.Value;
            //_contentReviewFactory.CheckOutput(_chatRunDto, result, null);
            var flowNode = await _chatRunDto.GetFlowNode(@event.FlowCode);

            if (flowNode.Config.RecognizeType == "ocr" && flowNode.Config.Pattern == (int)DocumentAnalysisTypeEnum.InformationExtraction)
            {
                var task = JsonCompile(result);
                List<ParamDto> outputs = flowNode.Config.Outputs;
                foreach (var item in outputs)
                {
                    if (task.ContainsKey(item.Code))
                    {
                        item.LiteralValue = task[item.Code].ToString();
                    }
                    else
                    {
                        item.LiteralValue = "";
                    }
                    await _chatRunDto.AddNodeOutputArgument2(item.Code, item.LiteralValue, flowNode.Code);
                }
                AddSucceedNodeLog(flowNode);
            }
            if (flowNode.Config.RecognizeType != "ocr" && _promptDto.OutputType != PromptOutputTypeEnum.Variables)
            {
                await AddFirstOutput(flowNode, result);
                if (flowNode.Config.AsMessage)
                {
                    await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, result));
                }
                AddSucceedNodeLog(flowNode, null, result);
            }
            if (flowNode.Config.RecognizeType != "ocr" && _promptDto.OutputType == PromptOutputTypeEnum.Variables)
            {
                Dictionary<string, object> keyValueDtos = JsonCompile(result);
                foreach (var item in flowNode.Config.Outputs)
                {
                    item.LiteralValue = keyValueDtos.ContainsKey(item.Code) ? keyValueDtos[item.Code]?.ToString() ?? string.Empty : string.Empty;
                    item.Value = null;
                    await _chatRunDto.AddNodeOutputArgument2(item.Code, item.LiteralValue, flowNode.Code);
                }
                AddSucceedNodeLog(flowNode);
            }
            _chatRunDto.UpdateOutputCache(flowNode.Code, flowNode.Config.Outputs);
        }

        private string handleMinProd(OcrResponse imgContent, int minProb)
        {
            var cleanedContent = JsonValidateHelper.CleanUpJsonIdentifiers(imgContent.Content);
            if (JsonValidateHelper.IsValidJsonObject(cleanedContent) || imgContent.WordsResult == null)
            {
                return cleanedContent;
            }
            
            String result = "";
            var imgContentWordsResult = imgContent.WordsResult;
            //循环遍历imgContentWordsResult
            foreach (var item in imgContentWordsResult)
            {
                //判断item.Prob是否大于等于minProb
                if (item.Prob >= minProb)
                {
                    //返回item.Words
                    result = result + " " + item.Words;
                }
            }

            return result;
        }

        private async Task<int> getMinProb(string configOcrService)
        {
            var modelInstances = await _modelInstanceRepostory.GetFirstAsync(x => x.InstanceCode == configOcrService);
            var modelEntity = await _modelRepostory.GetFirstAsync((x => x.ModelGUID == modelInstances.ModelGUID));
            var modelEntityExecutionSetting = modelEntity.ExecutionSetting;
            JObject executionSettingValue = JObject.Parse(modelEntityExecutionSetting);
            // 获取 minProd 的值
            return executionSettingValue.Value<int>("minProb");
        }

        private async Task ImageChatCheck(FlowNode flowNode)
        {
            if (flowNode.Config.RecognizeType == multimodal)
            {
                if (string.IsNullOrEmpty(flowNode.Config.TemplateId))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】提示词没有设置");
                }
            }
            else if (flowNode.Config.RecognizeType == ocr)
            {
                if (string.IsNullOrEmpty(flowNode.Config.OcrService))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】视觉识别服务没有设置");
                }
            }
            await Task.CompletedTask;
        }

        #region OCR重试机制

        /// <summary>
        /// 使用重试机制执行OCR服务
        /// </summary>
        /// <param name="ocrRecognizeService">OCR识别服务</param>
        /// <param name="ocrRequest">OCR请求</param>
        /// <param name="flowNode">流程节点</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>OCR响应</returns>
        private async Task<OcrResponse> ExecuteOcrWithRetry(IOcrRecognizeService ocrRecognizeService, OcrRequest ocrRequest, FlowNode flowNode, CancellationToken cancellationToken)
        {
            int retryCount = 0;
            var delay = TimeSpan.FromSeconds(InitialDelaySeconds);
            var random = new Random();

            while (true)
            {
                try
                {
                    return await ocrRecognizeService.Execute(ocrRequest);
                }
                catch (LLmCustomException ocrEx)
                {
                    // 处理OCR异常
                    var shouldRetry = await HandleOcrException(
                        ocrEx,
                        flowNode,
                        retryCount,
                        cancellationToken);

                    if (!shouldRetry)
                        throw;

                    retryCount++;
                    // 检查是否超过最大重试次数
                    if (retryCount > MaxRetryCount)
                    {
                        throw;
                    }

                    _logger.LogError("第 {0} 次重试 - OCR异常: {1}", retryCount, ocrEx.Message);

                    // 计算当前延迟时间（包含抖动）
                    var currentDelayWithJitter = CalculateRetryDelay(retryCount, delay, random);
                    await Task.Delay(currentDelayWithJitter, cancellationToken);

                    // 更新下次迭代的延迟时间
                    delay = GetNextRetryDelay(retryCount);
                }
            }
        }

        /// <summary>
        /// 处理OCR异常
        /// </summary>
        /// <param name="ocrEx">OCR异常</param>
        /// <param name="flowNode">流程节点</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否应该重试</returns>
        private async Task<bool> HandleOcrException(
            LLmCustomException ocrEx,
            FlowNode flowNode,
            int retryCount,
            CancellationToken cancellationToken)
        {
            // 判断是否应该重试的状态码和错误类型
            bool shouldRetry = ShouldRetryOcrException(ocrEx);

            if (!shouldRetry)
            {
                return false;
            }

            // 首次重试时显示等待消息
            if (retryCount == 0 && IsBrowserRequest())
            {
                await _chatRunDto.EventBus.PublishAsync(new TextEvent(flowNode.Code, ResourceWaitingMessage));
            }

            return true; // 应该重试
        }

        /// <summary>
        /// 判断OCR异常是否应该重试
        /// </summary>
        /// <param name="ocrEx">OCR异常</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryOcrException(LLmCustomException ocrEx)
        {
            // 只有当statusCode为"Ocr"且错误消息包含"异常编码：400"时才重试
            if (ocrEx.StatusCode == "Ocr" && !string.IsNullOrEmpty(ocrEx.Message))
            {
                return ocrEx.Message.Contains("异常编码：400");
            }

            return false; // 其他情况不重试
        }

        /// <summary>
        /// 计算重试延迟时间（包含抖动）
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <param name="baseDelay">基础延迟时间</param>
        /// <param name="random">随机数生成器</param>
        /// <returns>包含抖动的延迟时间</returns>
        private TimeSpan CalculateRetryDelay(int retryCount, TimeSpan baseDelay, Random random)
        {
            // 添加随机抖动以避免雷群效应
            var jitter = TimeSpan.FromMilliseconds(random.Next(MinJitterMs, MaxJitterMs));
            return baseDelay + jitter;
        }

        /// <summary>
        /// 获取下次重试的延迟时间
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <returns>下次重试的延迟时间</returns>
        private TimeSpan GetNextRetryDelay(int retryCount)
        {
            // 重试延迟策略：1s, 5s, 15s, 30s, 60s...
            return retryCount switch
            {
                1 => TimeSpan.FromSeconds(5),
                2 => TimeSpan.FromSeconds(15),
                3 => TimeSpan.FromSeconds(30),
                _ => TimeSpan.FromSeconds(59)
            };
        }

        /// <summary>
        /// 判断是否为浏览器请求
        /// </summary>
        /// <returns>是否为浏览器请求</returns>
        private bool IsBrowserRequest()
        {
            var userAgent = _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].FirstOrDefault();
            if (string.IsNullOrEmpty(userAgent))
                return false;

            var browserKeywords = new[] { "Chrome", "Firefox", "Safari", "Edge", "Opera", "MSIE", "Trident" };
            return browserKeywords.Any(keyword => userAgent.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        #endregion
    }
}
