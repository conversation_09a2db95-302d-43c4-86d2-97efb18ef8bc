using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Plugin.Activities.Llm;
using Mysoft.GPTEngine.Plugin.ContentReview;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Domain.Shared.Constants;


namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 文档分析
    /// </summary>
    public class DocumentActivity : LlmActivity
    {
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly ContentReviewFactory _contentReviewFactory;

        private readonly string fileInput = "fileInput";
        private readonly string content = "content";
        private readonly string fields = "fields";
        private readonly string tables = "tables";
        private readonly string ocr = "ocr";
        public DocumentActivity(IServiceProvider serviceProvider, MysoftApiService mysoftApiDomainService
            , IKnowledgeDomainService knowledgeDomainService, ContentReviewFactory contentReviewFactory
            , ILogger<DocumentActivity> logger) : base(serviceProvider, logger, contentReviewFactory, knowledgeDomainService)
        {
            _mysoftApiDomainService = mysoftApiDomainService;
            _knowledgeDomainService = knowledgeDomainService;
            _contentReviewFactory = contentReviewFactory;
        }

        [KernelFunction(nameof(DocumentActivity))]
        [Description("文档分析")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;
            #region 参数校验
            await FileArgumentCheck(flowNode);
            await DocumentAnalysisCheck(flowNode);
            #endregion
            return flowNode;
        }
        
        protected override async Task<LlmGetStreamingChatMessage?> ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            var files = flowNode.Config.Files;
            var currentDocumentKey = files.FirstOrDefault(x => x.Code == fileInput && x.Value?.Type == "ref")?.Value?.Content ?? "";
            if (!_chatRunDto.ChatArguments.ContainsName(currentDocumentKey))
            {
                throw new BusinessException("没有找到fileInput引用参数：" + currentDocumentKey);
            }
            var currentDocument = _chatRunDto.ChatArguments[currentDocumentKey]?.ToString();
            if (string.IsNullOrWhiteSpace(currentDocument))
            {
                throw new BusinessException("字段" + currentDocumentKey + "值为空");
            }
            
            // 文档读取
            if (flowNode.Config.Pattern == (int)DocumentAnalysisTypeEnum.DocumentRead)
            {
                await ExecDocumentReading(flowNode, cancellationToken);
            }
            // 信息提取
            else if (flowNode.Config.Pattern == (int)DocumentAnalysisTypeEnum.InformationExtraction)
            {
                await ExecInformationExtraction(flowNode, cancellationToken);
            }
            return await Task.FromResult<LlmGetStreamingChatMessage?>(default);
        }

        private async Task<string> ExecDocumentReading(FlowNode flowNode, CancellationToken cancellationToken)
        {
            var documentReadingDto = await DocumentReading(flowNode);
            foreach (var item in flowNode.Config.Outputs)
            {
                if (item.Code == content)
                {
                    item.LiteralValue = documentReadingDto.Content;
                }
                if (item.Code == fields)
                {
                    item.LiteralValue = documentReadingDto.FieldList;
                }
                if (item.Code == tables)
                {
                    item.LiteralValue = JsonConvert.SerializeObject(documentReadingDto.Tables);
                }
                
                await _chatRunDto.EventBus.PublishAsync(new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, item.Code), item.LiteralValue));
            }
            
            await base.PostExecuteActivityAsync(flowNode, null, cancellationToken);
            return "";
        }

        private async Task<string> ExecInformationExtraction(FlowNode flowNode, CancellationToken cancellationToken)
        {
            string str = "";
            // 组装json scheme
            TraverseParamDtos(flowNode.Config.Outputs, ref str);
            var chatMessage = await InformationExtraction(flowNode, str, _chatRunDto.ModelInstance.InstanceCode, flowNode.Config, cancellationToken);
            
            await base.PostExecuteActivityAsync(flowNode, null, cancellationToken);
            return "";
        }

        protected override async Task<LlmGetStreamingChatMessage?> PostExecuteActivityAsync(FlowNode flowNode, LlmGetStreamingChatMessage? result, CancellationToken cancellationToken)
        {
            if (flowNode.Config.Pattern == (int)DocumentAnalysisTypeEnum.DocumentRead)
            {
                await _chatRunDto.InputsArgumentParser();
                await _chatRunDto.OutputsArgumentParser();
                AddSucceedNodeLog(flowNode);
                return result;
            }
            return await base.PostExecuteActivityAsync(flowNode, result, cancellationToken);
        }

        protected override async Task LlmResultHandleAsync(LlmResultEvent @event)
        {
            var result = @event.Value;
            _contentReviewFactory.CheckOutput(_chatRunDto, result, null);
            var flowNode = await _chatRunDto.GetFlowNode(@event.FlowCode);
            List<ParamDto> outputs = flowNode.Config.Outputs;
            var keyValueDtos = JsonCompile(result.ToString());
            foreach (var item in outputs)
            {
                if (keyValueDtos.ContainsKey(item.Code))
                {
                    item.LiteralValue = keyValueDtos[item.Code]?.ToString() ?? string.Empty;
                }
                else
                {
                    item.LiteralValue = "";
                }
                await _chatRunDto.AddNodeOutputArgument2(item.Code, item.LiteralValue, flowNode.Code);
            }
            AddSucceedNodeLog(flowNode);
            _chatRunDto.UpdateOutputCache(flowNode.Code, flowNode.Config.Outputs);
        }

        private async Task DocumentAnalysisCheck(FlowNode flowNode)
        {
            if (flowNode.Config.ImageRecognizeType == ocr && string.IsNullOrEmpty(flowNode.Config.OcrService))
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】视觉识别服务没有设置");
            }
            await Task.CompletedTask;
        }

        /// <summary>
        /// 文本读取
        /// </summary>
        /// <param name="flowNode"></param>
        /// <param name="documentGuids"></param>
        /// <returns></returns>
        private async Task<DocumentReadingDto> DocumentReading(FlowNode flowNode)
        {
            // 支持多文件上传
            var documentGuids = GetFileGuidList(flowNode);
            DocumentReadingDto documentReadingDto = new DocumentReadingDto();
            List<DocumentInfoBaseDto> documentInfoBaseDtos = await _knowledgeDomainService.GetDocumentInfo(documentGuids);
            if (documentInfoBaseDtos == null || documentInfoBaseDtos.Count == 0)
            {
                return documentReadingDto;
            }
            // 多文件合并，file→loop[Aspose/OCR]→documentcontents
            foreach (var documentInfoBaseDto in documentInfoBaseDtos)
            {
                string downloadUrl = documentInfoBaseDto.DownloadUrl;
                string fileName = documentInfoBaseDto.FileName;

                // 使用重试机制下载文件
                byte[] fileBytes = await DownloadFileWithRetryAsync(downloadUrl, fileName);
                MemoryStream stream = new MemoryStream(fileBytes);
                string type = FileTypeConvertHelper.GetFileType(fileName);
                IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(type, this._kernel, this._mysoftApiDomainService, this._mysoftContextFactory, this._httpContextAccessor, this._mapper);
                var curDocumentReadingDto = await documentDecoder.DomentReadAsync(stream, flowNode.Config);
                documentReadingDto.Content = documentReadingDto.Content + "\r\n" + curDocumentReadingDto.Content;
                documentReadingDto.FieldList = documentReadingDto.FieldList + "\r\n" + curDocumentReadingDto.FieldList;
                documentReadingDto.Tables = documentReadingDto.Tables.Concat(curDocumentReadingDto.Tables).ToList<String>();
            }
            return documentReadingDto;
        }

        /// <summary>
        /// 带重试机制的文件下载方法
        /// </summary>
        /// <param name="downloadUrl">下载URL</param>
        /// <param name="fileName">文件名</param>
        /// <returns>文件字节数组</returns>
        private async Task<byte[]> DownloadFileWithRetryAsync(string downloadUrl, string fileName)
        {
            const int MaxRetryCount = 5; // 增加重试次数，因为服务端断开是常见问题
            const int InitialDelaySeconds = 1; // 减少初始延迟，快速重试

            int retryCount = 0;
            var delay = TimeSpan.FromSeconds(InitialDelaySeconds);
            var random = new Random();

            _logger.LogInformation("开始下载文件: {0}, URL: {1}", fileName, downloadUrl);

            while (true)
            {
                try
                {
                    using (var httpClient = new HttpClient())
                    {
                        // 设置超时时间为10分钟（适合大文件下载）
                        httpClient.Timeout = TimeSpan.FromMinutes(10);

                        // 设置请求头，模拟浏览器行为
                        httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
                        httpClient.DefaultRequestHeaders.Add("Accept", "*/*");
                        httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
                        httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");

                        _logger.LogDebug("第 {0} 次尝试下载文件: {1}", retryCount + 1, fileName);

                        // 使用HttpClient下载，更好地处理大文件和网络中断
                        using (var response = await httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead))
                        {
                            response.EnsureSuccessStatusCode();

                            // 获取文件大小信息（如果可用）
                            var contentLength = response.Content.Headers.ContentLength;
                            if (contentLength.HasValue)
                            {
                                _logger.LogDebug("文件大小: {0} bytes ({1} MB)", contentLength.Value, contentLength.Value / 1024.0 / 1024.0);
                            }

                            // 读取内容
                            var content = await response.Content.ReadAsByteArrayAsync();
                            _logger.LogInformation("文件下载成功: {0}, 实际大小: {1} bytes", fileName, content.Length);
                            return content;
                        }
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    // 判断是否应该重试
                    bool shouldRetry = ShouldRetryHttpException(httpEx);

                    if (!shouldRetry || retryCount >= MaxRetryCount)
                    {
                        var errorDesc = GetDownloadErrorDescription(httpEx);
                        _logger.LogError(httpEx, "文件下载失败，已达到最大重试次数。文件: {0}, URL: {1}, 错误描述: {2}", fileName, downloadUrl, errorDesc);
                        throw new Exception($"文件下载失败: {fileName}. 错误: {errorDesc}", httpEx);
                    }

                    retryCount++;

                    // 首次重试时显示等待消息（如果是浏览器请求）
                    if (retryCount == 1 && IsBrowserRequest())
                    {
                        var errorDesc = GetDownloadErrorDescription(httpEx);
                        await _chatRunDto.EventBus.PublishAsync(new TextEvent("", $"文件下载遇到问题（{errorDesc}），正在重试..."));
                    }

                    var friendlyError = GetDownloadErrorDescription(httpEx);
                    _logger.LogWarning("文件下载失败，第 {0} 次重试。文件: {1}, 错误: {2}", retryCount, fileName, friendlyError);

                    // 计算延迟时间（包含随机抖动）
                    var currentDelayWithJitter = CalculateRetryDelay(retryCount, delay, random);
                    await Task.Delay(currentDelayWithJitter);

                    // 更新下次重试的延迟时间（指数退避）
                    delay = TimeSpan.FromSeconds(Math.Min(InitialDelaySeconds * Math.Pow(2, retryCount), 30));
                }
                catch (TaskCanceledException tcEx) when (tcEx.InnerException is TimeoutException)
                {
                    // 处理超时异常
                    if (retryCount >= MaxRetryCount)
                    {
                        _logger.LogError(tcEx, "文件下载超时，已达到最大重试次数。文件: {0}, URL: {1}", fileName, downloadUrl);
                        throw new Exception($"文件下载超时: {fileName}. 错误: {tcEx.Message}", tcEx);
                    }

                    retryCount++;

                    if (retryCount == 1 && IsBrowserRequest())
                    {
                        await _chatRunDto.EventBus.PublishAsync(new TextEvent("", "文件下载超时，正在重试..."));
                    }

                    _logger.LogWarning("文件下载超时，第 {0} 次重试。文件: {1}", retryCount, fileName);

                    var currentDelayWithJitter = CalculateRetryDelay(retryCount, delay, random);
                    await Task.Delay(currentDelayWithJitter);
                    delay = TimeSpan.FromSeconds(Math.Min(InitialDelaySeconds * Math.Pow(2, retryCount), 30));
                }
                catch (IOException ioEx)
                {
                    // 处理IO异常（如连接提前结束）
                    if (retryCount >= MaxRetryCount)
                    {
                        _logger.LogError(ioEx, "文件下载IO异常，已达到最大重试次数。文件: {0}, URL: {1}", fileName, downloadUrl);
                        throw new Exception($"文件下载失败: {fileName}. 错误: {ioEx.Message}", ioEx);
                    }

                    retryCount++;

                    if (retryCount == 1 && IsBrowserRequest())
                    {
                        await _chatRunDto.EventBus.PublishAsync(new TextEvent("", "文件下载连接中断，正在重试..."));
                    }

                    _logger.LogWarning("文件下载IO异常，第 {0} 次重试。文件: {1}, 错误: {2}", retryCount, fileName, ioEx.Message);

                    var currentDelayWithJitter = CalculateRetryDelay(retryCount, delay, random);
                    await Task.Delay(currentDelayWithJitter);
                    delay = TimeSpan.FromSeconds(Math.Min(InitialDelaySeconds * Math.Pow(2, retryCount), 30));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "文件下载发生未预期的异常。文件: {0}, URL: {1}", fileName, downloadUrl);
                    throw new Exception($"文件下载失败: {fileName}. 错误: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 判断HttpRequestException是否应该重试
        /// </summary>
        /// <param name="httpEx">HTTP请求异常</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryHttpException(HttpRequestException httpEx)
        {
            // 检查异常消息中的关键词（包括内部异常）
            var message = httpEx.Message?.ToLower() ?? "";
            var innerMessage = httpEx.InnerException?.Message?.ToLower() ?? "";
            var fullMessage = $"{message} {innerMessage}";

            // 服务端主动断开连接的常见错误模式
            if (fullMessage.Contains("response ended prematurely") ||
                fullMessage.Contains("error while copying content to a stream") ||
                fullMessage.Contains("unexpected eof") ||
                fullMessage.Contains("connection closed") ||
                fullMessage.Contains("connection reset") ||
                fullMessage.Contains("connection aborted") ||
                fullMessage.Contains("connection interrupted") ||
                fullMessage.Contains("remote host closed") ||
                fullMessage.Contains("server closed the connection") ||
                fullMessage.Contains("0 bytes from the transport stream") ||
                fullMessage.Contains("unable to read data from the transport connection"))
            {
                _logger.LogWarning("检测到文档服务断开连接，将进行重试: {0}", fullMessage);
                return true;
            }

            // 其他网络相关错误也应该重试
            if (message.Contains("timeout") ||
                message.Contains("network") ||
                message.Contains("socket") ||
                message.Contains("dns") ||
                message.Contains("ssl") ||
                message.Contains("tls") ||
                message.Contains("handshake") ||
                message.Contains("certificate"))
            {
                return true;
            }

            // 检查HTTP状态码（如果可用）
            if (httpEx.Data.Contains("StatusCode"))
            {
                if (int.TryParse(httpEx.Data["StatusCode"]?.ToString(), out int statusCode))
                {
                    // 5xx 服务器错误、429 请求过多、502 网关错误、503 服务不可用、504 网关超时
                    if (statusCode >= 500 || statusCode == 429 || statusCode == 502 || statusCode == 503 || statusCode == 504)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 获取下载异常的友好描述
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>友好的错误描述</returns>
        private string GetDownloadErrorDescription(Exception ex)
        {
            var message = ex.Message?.ToLower() ?? "";
            var innerMessage = ex.InnerException?.Message?.ToLower() ?? "";
            var fullMessage = $"{message} {innerMessage}";

            if (fullMessage.Contains("response ended prematurely") ||
                fullMessage.Contains("error while copying content to a stream"))
            {
                return "文档服务主动断开连接，可能是服务器负载过高或网络不稳定";
            }

            if (message.Contains("timeout"))
            {
                return "下载超时，可能是文件过大或网络速度较慢";
            }

            if (message.Contains("connection reset") || message.Contains("connection aborted"))
            {
                return "网络连接被重置，可能是网络不稳定或防火墙阻断";
            }

            if (message.Contains("dns") || message.Contains("name resolution"))
            {
                return "域名解析失败，请检查网络连接";
            }

            if (message.Contains("ssl") || message.Contains("tls") || message.Contains("certificate"))
            {
                return "SSL/TLS证书验证失败，可能是证书过期或配置问题";
            }

            return ex.Message;
        }

        /// <summary>
        /// 计算重试延迟时间（包含随机抖动）
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <param name="baseDelay">基础延迟时间</param>
        /// <param name="random">随机数生成器</param>
        /// <returns>实际延迟时间</returns>
        private TimeSpan CalculateRetryDelay(int retryCount, TimeSpan baseDelay, Random random)
        {
            // 添加±25%的随机抖动
            var jitterFactor = 0.75 + (random.NextDouble() * 0.5); // 0.75 到 1.25 之间
            var delayWithJitter = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * jitterFactor);
            return delayWithJitter;
        }

        /// <summary>
        /// 判断是否为浏览器请求
        /// </summary>
        /// <returns>是否为浏览器请求</returns>
        private bool IsBrowserRequest()
        {
            var userAgent = _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].FirstOrDefault();
            return !string.IsNullOrEmpty(userAgent) &&
                   (userAgent.Contains("Mozilla") || userAgent.Contains("Chrome") || userAgent.Contains("Safari"));
        }

        /// <summary>
        /// 带重试机制的文件下载方法
        /// </summary>
        /// <param name="downloadUrl">下载URL</param>
        /// <param name="flowNode">流程节点</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>下载的文件字节数组</returns>
        private async Task<byte[]> DownloadFileWithRetryAsync(string downloadUrl, FlowNode flowNode, CancellationToken cancellationToken)
        {
            const int maxRetryCount = 3; // 最大重试次数
            const int initialDelaySeconds = 1; // 初始延迟秒数
            const string resourceWaitingMessage = "文档服务连接中断，正在重新尝试下载，请稍候..."; // 资源等待消息

            var random = new Random();
            int retryCount = 0;
            TimeSpan delay = TimeSpan.FromSeconds(initialDelaySeconds);

            while (true)
            {
                try
                {
                    using (WebClient client = new WebClient())
                    {
                        // 设置超时时间
                        client.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                        return client.DownloadData(downloadUrl);
                    }
                }
                catch (WebException webEx) when (ShouldRetryWebException(webEx))
                {
                    retryCount++;

                    // 检查是否超过最大重试次数
                    if (retryCount > maxRetryCount)
                    {
                        _logger.LogError("文档下载重试次数超过最大限制 {0}，URL: {1}，错误: {2}", maxRetryCount, downloadUrl, webEx.Message);
                        throw new LLmCustomException("DocumentDownload", $"文档下载失败，已重试 {maxRetryCount} 次: {webEx.Message}");
                    }

                    _logger.LogError("第 {0} 次重试文档下载 - URL: {1}，错误: {2}", retryCount, downloadUrl, webEx.Message);

                    // 首次重试时显示等待消息
                    if (retryCount == 1 && IsBrowserRequest())
                    {
                        await _chatRunDto.EventBus.PublishAsync(new TextEvent(flowNode.Code, resourceWaitingMessage));
                    }

                    // 计算延迟时间（包含抖动）
                    var jitter = TimeSpan.FromMilliseconds(random.Next(500, 2000));
                    var totalDelay = delay + jitter;
                    await Task.Delay(totalDelay, cancellationToken);

                    // 更新下次重试的延迟时间
                    delay = retryCount switch
                    {
                        1 => TimeSpan.FromSeconds(3),
                        2 => TimeSpan.FromSeconds(5),
                        _ => TimeSpan.FromSeconds(10)
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError("文档下载发生非网络异常，URL: {0}，错误: {1}", downloadUrl, ex.Message);
                    throw new LLmCustomException("DocumentDownload", $"文档下载失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 判断WebException是否应该重试
        /// </summary>
        /// <param name="webEx">Web异常</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryWebException(WebException webEx)
        {
            // 检查异常类型和状态
            if (webEx.Status == WebExceptionStatus.ConnectionClosed ||
                webEx.Status == WebExceptionStatus.ConnectFailure ||
                webEx.Status == WebExceptionStatus.Timeout ||
                webEx.Status == WebExceptionStatus.ReceiveFailure ||
                webEx.Status == WebExceptionStatus.SendFailure ||
                webEx.Status == WebExceptionStatus.PipelineFailure ||
                webEx.Status == WebExceptionStatus.KeepAliveFailure)
            {
                return true;
            }

            // 检查内部异常
            if (webEx.InnerException != null)
            {
                var innerMessage = webEx.InnerException.Message?.ToLower();
                if (!string.IsNullOrEmpty(innerMessage))
                {
                    // 检查是否是连接断开相关的错误
                    if (innerMessage.Contains("unexpected eof") ||
                        innerMessage.Contains("0 bytes from the transport stream") ||
                        innerMessage.Contains("connection reset") ||
                        innerMessage.Contains("connection aborted") ||
                        innerMessage.Contains("transport stream"))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private async Task<string> InformationExtraction(FlowNode flowNode, string outputs, string modelCode, NodeConfig nodeConfig, CancellationToken cancellationToken)
        {
            // 支持多文件上传
            var documentGuids = GetFileGuidList(flowNode);
            _logger.LogInformation("文档解析documentGuiDs：{0}", documentGuids);
            //先返回卡片格式，到时候看看要不要改
            var interactiveCard = new FormCardDto();
            //发送一次提醒
            //await TextEvent("我即将进行【文本填充】，以下是根据你的文档提取的表单信息，确认后我将进一步执行。", true);

            List<DocumentInfoBaseDto> documentInfoBaseDtos = await _knowledgeDomainService.GetDocumentInfo(documentGuids);
            if (documentInfoBaseDtos == null || documentInfoBaseDtos.Count == 0)
            {
                return "";
            }
            // 多文件合并，file→loop[Aspose/OCR]→documentcontents
            string documentContents = "";
            foreach (var documentInfoBaseDto in documentInfoBaseDtos)
            {
                string downloadUrl = documentInfoBaseDto.DownloadUrl;
                string fileName = documentInfoBaseDto.FileName;
                string type = FileTypeConvertHelper.GetFileType(fileName);
                IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(type, this._kernel, this._mysoftApiDomainService, this._mysoftContextFactory, this._httpContextAccessor, this._mapper);

                // 使用重试机制下载文件
                byte[] fileBytes = await DownloadFileWithRetryAsync(downloadUrl, flowNode, cancellationToken);
                MemoryStream stream = new MemoryStream(fileBytes);
                string curDocumentContent = await documentDecoder.GetDocumentTxt(stream, nodeConfig);
                documentContents = documentContents + curDocumentContent + "/r/n";
            }

            var arguments = new KernelArguments { { nameof(outputs), outputs } };
            var publishEventConfig = new PublishEventConfig()
            {
                PublishTextEvent = true,
                PublishLlmResultEvent = true,
                ResponseFormatType = flowNode.Config.Pattern == 0 ? ResponseFormatType.Text : ResponseFormatType.JsonObject,
                ParamCodes = flowNode.Config.Pattern == 0 ? null : flowNode.Config.Outputs.Select(x => x.Code).ToList()
            };
            flowNode._documentTxt = documentContents;
            flowNode._serviceId = modelCode;
            flowNode._publishEventConfig = publishEventConfig;
            // 使用立即执行对话完成
            var result = await ChatCompletionExec(flowNode, systemPromptName: EmbeddedResource.DocumentAnalysisPlugin_FixedGenerate,
                input: documentContents, arguments, serviceId: modelCode, publishEventConfig: publishEventConfig,
                cancellationToken: cancellationToken);

            return result;
        }

        protected override async Task<ChatHistory> ChatMessages(FlowNode flowNode, string promptGuid)
        {
            string outputs = "";
            // 组装json scheme
            TraverseParamDtos(flowNode.Config.Outputs, ref outputs);
            var arguments = new KernelArguments { { nameof(outputs), outputs } };
            arguments = arguments ?? await _chatRunDto.GetNodeInputArgument(flowNode);
            var promptTemplateText = EmbeddedResource.Read(EmbeddedResource.DocumentAnalysisPlugin_FixedGenerate);
            var systemPrompy = await TemplateRenderAsync(promptTemplateText: promptTemplateText, arguments: arguments);
            
            var chatHistory = await CreateChatHistory(flowNode,systemMessage: systemPrompy.Content, history: 3);
            await AddMemoriesChatHistory(flowNode,chatHistory);
            if (string.IsNullOrWhiteSpace(flowNode._documentTxt) == false)
            {
                chatHistory.AddUserMessage(flowNode._documentTxt);
            }
            return chatHistory;
        }
    }
}
