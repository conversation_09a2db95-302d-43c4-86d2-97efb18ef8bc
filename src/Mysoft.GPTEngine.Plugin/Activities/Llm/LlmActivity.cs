using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;

namespace Mysoft.GPTEngine.Plugin.Activities.Llm
{
    public class LlmActivity : SemanticKernelActivity<LlmGetStreamingChatMessage>, IEventHandler<TextEvent>
    {
        private readonly ContentReviewFactory _contentReviewFactory;
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        public LlmActivity(IServiceProvider serviceProvider, ILogger<LlmActivity> logger, ContentReviewFactory contentReviewFactory,
            IKnowledgeDomainService knowledgeDomainService) : base(serviceProvider, logger)
        {
            _knowledgeDomainService = knowledgeDomainService;
            _contentReviewFactory = contentReviewFactory;
        }
        [KernelFunction(nameof(LlmActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;

            // 只有提示词节点才会判断是否订阅流式输出（text级别）
            if (flowNode.Config.AsMessage && flowNode.Type == SkillNodeTypeConstant.PromptTemplateNode)
            {
                _chatRunDto.EventBus.Subscribe<TextEvent>(this, flowNode.Code);
            }


            return flowNode;
        }
        protected override async Task<LlmGetStreamingChatMessage?> ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.ExecuteActivityAsync(flowNode, cancellationToken);
            var promptGuid = flowNode.Config.TemplateId;
            var skillOrchestration = _chatRunDto.SkillOrchestration;
            var prompt = skillOrchestration.Prompts.FirstOrDefault(x => x.Id == Guid.Parse(promptGuid));
            Verify.NotNull(prompt);
            
            string serviceId = prompt.ModelInstanceCode;
            
            ChatHistory chatHistory = new ChatHistory();
            var publishEventConfig = new PublishEventConfig()
            {
                PublishTextEvent = true,
                PublishLlmResultEvent = true,
                ResponseFormatType = flowNode.Config.Pattern == 0 ? ResponseFormatType.Text : ResponseFormatType.JsonObject,
                ParamCodes = flowNode.Config.Pattern == 0 ? null : flowNode.Config.Outputs.Select(x => x.Code).ToList()
            };
            
            flowNode._executionSetting = prompt.ExecutionSetting;
            flowNode._publishEventConfig = publishEventConfig;
            flowNode._promptGuid = promptGuid;
            var llmDelegate = await ChatCompletion(flowNode, chatHistory: chatHistory, serviceId: serviceId, executionSetting: prompt.ExecutionSetting, publishEventConfig: publishEventConfig, cancellationToken: cancellationToken);
            // 需要流式输出的时候，手动把这个委托注入一下
            if (flowNode.Config.AsMessage && flowNode.Type == SkillNodeTypeConstant.PromptTemplateNode)
            {
                flowNode.ExcuteCurrentFlowNode = true;
                flowNode._inputDependencies.Add(new NodeDependency()
                {
                    NodeCode = flowNode.Code,
                    nodeDependencyHandler = llmDelegate
                });
            }

            foreach (var item in flowNode.Config.Outputs)
            {
                item.Value = new SemanticKernel.Core.Dtos.ParamValueDto() { SyncMessage = llmDelegate, Content = flowNode.Code };
            }

            return llmDelegate;
        }
        
        protected override async Task<LlmGetStreamingChatMessage?> PostExecuteActivityAsync(FlowNode flowNode, LlmGetStreamingChatMessage? result, CancellationToken cancellationToken)
        {
            return await base.PostExecuteActivityAsync(flowNode, result, cancellationToken); ;
        }

        protected override async Task<ChatHistory> ChatMessages(FlowNode flowNode, string promptGuid)
        {
            var skillOrchestration = _chatRunDto.SkillOrchestration;
            var prompt = skillOrchestration.Prompts.FirstOrDefault(x => x.Id == Guid.Parse(promptGuid));
            Verify.NotNull(prompt);
            // 提示词是参数列表类型时，特殊处理输出格式
            if (prompt.OutputType == PromptOutputTypeEnum.Variables)
            {
                var outputs = flowNode.Config.Outputs;
                string formatStr = "";
                TraverseParamDtos(outputs, ref formatStr);
                prompt.PromptTemplate += GetOutputPrompt(formatStr);
            }
            var arguments = await _chatRunDto.GetNodeInputArgument(flowNode);
            var chatHistory = await CreateChatHistory(flowNode,prompt, arguments);
            string serviceId = prompt.ModelInstanceCode;
            var fileIds = GetFileGuidList(flowNode);
            if (fileIds.Any())
            {
                if (flowNode.Type == SkillNodeTypeConstant.ImageAnalysisNode)
                {
                    serviceId = string.IsNullOrEmpty(serviceId) ? ChatCompletionTypeDto.ImageComprehend : serviceId;
                }
                else if (flowNode.Type == SkillNodeTypeConstant.PromptTemplateNode)
                {
                    serviceId = string.IsNullOrEmpty(serviceId) ? ChatCompletionTypeDto.FileComprehend : serviceId;
                }
            }
            else
            {
                serviceId = string.IsNullOrEmpty(serviceId) ? ChatCompletionTypeDto.TextGeneration : serviceId;
            }
            
            flowNode._serviceId = serviceId;
            if (fileIds.Any())
            {
                if (flowNode.Type == SkillNodeTypeConstant.PromptTemplateNode)
                {
                    var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds);
                    if (documents.Any())
                    {
                        int lastSystemIndex = -1; // 记录最后一个 System 消息的位置
                        bool hasSystemMessage = false; // 标记是否存在 System 消息
                                                       // 遍历 chatHistory 以查找 System 消息
                        int insertPosition = 0;
                        foreach (var chatMessageContent in chatHistory)
                        {
                            if (chatMessageContent.Role == AuthorRole.System)
                            {
                                hasSystemMessage = true;
                                lastSystemIndex = insertPosition;
                            }
                            insertPosition++;
                        }
                        int startIndex = 0;
                        // 插入新的 System 消息
                        if (hasSystemMessage)
                        {
                            // 如果没有 System 消息，在最顶端插入一条 System 消息
                            startIndex = lastSystemIndex + 1;
                        }
                        for (int i = 0; i < documents.Count; i++)
                        {
                            using (WebClient client = new WebClient())
                            {
                                byte[] fileBytes = client.DownloadData(documents[i].DownloadUrl);
                                chatHistory.Insert(startIndex, new ChatMessageContent(role: AuthorRole.System, items: new ChatMessageContentItemCollection(){new SemanticKernel.Core.Contents.BinaryContent()
                                {
                                    Data = new ReadOnlyMemory<byte>(fileBytes)
                                }}));
                            }
                            startIndex++;
                        }
                    }
                }
            }
            var chatHistoryLog = chatHistory.Select(x => x.Items.Where(item => item is TextContent).Select(item => item)).ToList();
            flowNode.Config.Inputs.Add(new ParamDto()
            {
                Code = "ChatHistory",
                LiteralValue = JsonConvert.SerializeObject(chatHistoryLog)
            });
            return chatHistory;
        }
        public virtual async Task HandleAsync(TextEvent @event, string code)
        {
            if (@event.FlowCode == code)
            {
                var flowNode = await _chatRunDto.GetFlowNode(code);

                await TextEvent(@event.Value, flowNode.Config.AsMessage);
            }
        }

        /// <summary>
        /// 当前节点的提示词完成事件
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        protected override async Task LlmResultHandleAsync(LlmResultEvent @event)
        {
            var result = @event.Value;
            var flowNode = await _chatRunDto.GetFlowNode(@event.FlowCode);
            var promptDto = GetPrompt(flowNode);
            // 内容检查
            if (_chatRunDto.IsStream)
            {
                _contentReviewFactory.CheckOutput(_chatRunDto, result, promptDto);
            }
            else
            {
                await _contentReviewFactory.CheckOutput(_chatRunDto, result, promptDto);
            }

            // 替换占位符
            if (_chatRunDto.ReplaceDtos != null && _chatRunDto.ReplaceDtos.Count != 0)
            {
                foreach (var item in _chatRunDto.ReplaceDtos)
                {
                    result = result.Replace(item.key, item.value);
                }
            }

            // 解析JSON赋值变量
            var outputJsonObject = promptDto?.OutputType == PromptOutputTypeEnum.Variables || flowNode.PublishEventConfig?.ResponseFormatType == ResponseFormatType.JsonObject;
            if (outputJsonObject)
            {
                Dictionary<string, object> keyValueDtos = JsonCompile(result);
                foreach (var item in flowNode.Config.Outputs)
                {
                    item.LiteralValue = keyValueDtos.ContainsKey(item.Code) ? keyValueDtos[item.Code]?.ToString() ?? string.Empty : string.Empty;
                    item.Value = null;
                    await _chatRunDto.AddNodeOutputArgument2(item.Code, item.LiteralValue, flowNode.Code);
                }
                AddSucceedNodeLog(flowNode);
            }
            else
            {
                await AddFirstOutput(flowNode, result);
                AddSucceedNodeLog(flowNode, null, result);
            }
            if (flowNode.Config.AsMessage)
            {
                await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, result));
            }
            _chatRunDto.UpdateOutputCache(flowNode.Code, flowNode.Config.Outputs);
        }
        
        public List<Guid> GetFileGuidList(FlowNode flowNode)
        {
            var fileIds = new List<Guid>();
            foreach (var file in flowNode.Config.Files)
            {
                if (string.IsNullOrEmpty(file.LiteralValue))
                {
                    continue;
                }
                
                var guidStrings = new List<string>();
                if (JsonValidateHelper.IsValidArrayString(file.LiteralValue))
                {
                    guidStrings = JsonConvert.DeserializeObject<List<string>>(file.LiteralValue) ?? new List<string>();
                }else
                {
                    guidStrings = file.LiteralValue.Split(',').ToList();
                }
                
                foreach (string guidStr in guidStrings)
                {
                    if (Guid.TryParse(guidStr.Trim(), out Guid guid))
                    {
                        fileIds.Add(guid);
                    }
                }
            }

            return fileIds;
        }
    }
}
