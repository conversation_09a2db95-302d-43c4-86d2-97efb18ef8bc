using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Plugin.Activities.Llm;
using Mysoft.GPTEngine.Plugin.ContentReview;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;
using Microsoft.SemanticKernel.ChatCompletion;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 意图识别
    /// </summary>
    public class ClassificationActivity : SemanticKernelActivity<string>
    {
        public ClassificationActivity(IServiceProvider serviceProvider, IKnowledgeDomainService knowledgeDomainService, ContentReviewFactory contentReviewFactory, ILogger<ClassificationActivity> logger) : base(serviceProvider, logger)
        {

        }

        [KernelFunction(nameof(ClassificationActivity))]
        [Description("意图识别")]
        public new async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            await base.ExecuteAsync(cancellationToken);
        }

        protected override async Task<ChatHistory> ChatMessages(FlowNode flowNode, string promptGuid)
        {
            string outputs = "";
            // 组装json scheme
            var classification = JsonConvert.SerializeObject(flowNode.Config.Classifications);
            var arguments = new KernelArguments { { nameof(classification), classification } };
            var promptTemplateText = EmbeddedResource.Read(EmbeddedResource.SelectorPlugin_Classification);
            var systemPrompt = await TemplateRenderAsync(promptTemplateText: promptTemplateText, arguments: arguments);
            // Console.WriteLine("大模型调用：" + systemPrompt.Content);
            var chatHistory = await CreateChatHistory(flowNode, systemMessage: systemPrompt.Content, history: 3);
            await AddMemoriesChatHistory(flowNode, chatHistory);
            if (string.IsNullOrWhiteSpace(flowNode._input) == false)
            {
                chatHistory.AddUserMessage(flowNode._input);
            }
            return chatHistory;
        }

        protected override async Task<string?> PostExecuteActivityAsync(FlowNode flowNode, string? result, CancellationToken cancellationToken)
        {
            if (flowNode == null || flowNode.Config == null || flowNode.Config.Classifications.Any() == false)
            {
                return "";
            }
            await _chatRunDto.InputsArgumentParser(flowNode);
            var inputs = flowNode.Config.Inputs;
            var input = "";
            input = inputs.FirstOrDefault(x => x.Code == nameof(input))?.LiteralValue ?? input;
            var classification = JsonConvert.SerializeObject(flowNode.Config.Classifications);
            var arguments = new KernelArguments { { nameof(classification), classification } };
            //执行提示词
            var publishEventConfig = new PublishEventConfig()
            {
                ResponseFormatType = ResponseFormatType.Text,
                ParamCodes = flowNode.Config.Pattern == 0 ? null : flowNode.Config.Outputs.Select(x => x.Code).ToList()
            };
            flowNode._input = input;
            flowNode._serviceId = _chatRunDto.ModelInstance.InstanceCode;
            flowNode._publishEventConfig = publishEventConfig;
            result = await ChatCompletionExec(flowNode, systemPromptName: EmbeddedResource.SelectorPlugin_Classification,
                input: input, arguments: arguments, serviceId: _chatRunDto.ModelInstance.InstanceCode, publishEventConfig: publishEventConfig,
                cancellationToken: cancellationToken, history: 3);
            Dictionary<string, object> keyValueDtos = JsonCompile(result.ToString());
            var outputs = await _chatRunDto.GetFlowNodeOutputs();
            foreach (var item in outputs)
            {
                if (keyValueDtos.ContainsKey(item.Code))
                {
                    item.LiteralValue = keyValueDtos[item.Code] == null ? "" : keyValueDtos[item.Code].ToString();
                }
                else
                {
                    item.LiteralValue = "";
                }
                await AddNodeOutputArgument(flowNode, item.Code, item.LiteralValue);
            }
            return await base.PostExecuteActivityAsync(flowNode, result, cancellationToken); ;
        }
    }
}
