using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.Activities.Card;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Plugin.Activities
{
    /// <summary>
    /// 结束节点
    /// </summary>
    public class EndActivity : SemanticKernelActivity
    {
        public EndActivity(IServiceProvider serviceProvider, ILogger<EndActivity> logger) : base(serviceProvider, logger)
        {

        }

        [KernelFunction(nameof(EndActivity))]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }
        protected override Task InitRefParams(FlowNode flowNode)
        {
            flowNode._refParams = flowNode.Config.Outputs;
            return Task.CompletedTask;
        }
        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            var result = new ExpandoObject();
            foreach (var output in flowNode._outParams)
            {
                _ = result.TryAdd(output.Code, output.Type == FieldTypeConstant.ArrayField ? JsonConvert.DeserializeObject<Object>(output.LiteralValue) : output.LiteralValue);
            }
            string resultStr = JsonConvert.SerializeObject(result);
            _logger.LogInformation($"{nameof(EndActivity)}: {0}", resultStr);
            // 添加技能的返回值
            await _chatRunDto.AddArgument(KernelArgumentsConstant.Output, resultStr);
            if (flowNode._outParams.Count > 0)
            {
                bool hiddenMsg = flowNode._outParams.All(s => string.IsNullOrEmpty(s.LiteralValue));
                await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, resultStr), hiddenMsg); 
            }
            AddSucceedNodeLog(flowNode);
        }
    }
}
