using Mysoft.GPTEngine.Plugin.System;
using System.IO;
using System.Reflection;

namespace Mysoft.GPTEngine.Plugin.Resources
{
    public static class EmbeddedResource
    {
        private static readonly string? s_namespace = typeof(EmbeddedResource).Namespace;
        public static readonly string DocumentAnalysisPlugin_FixedGenerate = "DocumentAnalysisPlugin.FixedGenerate.txt";
        public static readonly string ParameterPlugin_FixedGenerate = "ParameterPlugin.FixedGenerate.txt";
        public static readonly string ParameterPlugin_ParamMapping = "ParameterPlugin.ParamMapping.txt";
        public static readonly string PromptPlugin_Estimate = "PromptPlugin.Estimate.txt";
        public static readonly string QuestionGeneratePlugin_FixedGenerate = "QuestionGeneratePlugin.FixedGenerate.txt";
        public static readonly string KnowledgePlugin_QuestionOptimization = "KnowledgePlugin.QuestionOptimization.txt";
        public static readonly string SelectorPlugin_Classification = "SelectorPlugin.Classification.txt";
        public static readonly string DataQuery = "DataQuery.txt";

        public static string Read(string name)
        {
            var assembly = typeof(EmbeddedResource).GetTypeInfo().Assembly ??
                throw new FileNotFoundException($"[{s_namespace}] {name} assembly not found");

            using Stream? resource = assembly.GetManifestResourceStream($"{s_namespace}." + name) ??
                throw new FileNotFoundException($"[{s_namespace}] {name} resource not found");

            using var reader = new StreamReader(resource);
            return reader.ReadToEnd();
        }
    }
}
