using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using System;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using System.Collections.Generic;
using Newtonsoft.Json;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Microsoft.SemanticKernel.Memory;
using System.Diagnostics;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.DTO;

namespace Mysoft.GPTEngine.Plugin
{
    public class ChatPlugin : PluginBase
    {
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly ILogger<ChatPlugin> _logger;
        private readonly ContentReviewFactory _contentReviewFactory;
        public ChatPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper, IMysoftContextFactory mysoftContextFactory, ILogger<ChatPlugin> logger, IKnowledgeDomainService knowledgeDomainService,
            ContentReviewFactory contentReviewFactory) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _logger = logger;
            _contentReviewFactory = contentReviewFactory;
            _knowledgeDomainService = knowledgeDomainService;
        }

        [KernelFunction]
        [Description("Get Streaming ChatMessage Contents.")]
        public virtual async Task<string> StreamingChatCompletionAsync([Description("提示词模板ID")] string promptGuid, CancellationToken cancellationToken)
        {
            _logger.LogInformation("提示词模版GUID: {0}", promptGuid);
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return string.Empty;
            var skillOrchestration = chatRunDto.SkillOrchestration;
            var prompt = skillOrchestration.Prompts.FirstOrDefault(x => x.Id == Guid.Parse(promptGuid));
            Verify.NotNull(prompt);
            // 参数校验
            await NodeArgumentCheck(chatRunDto);

            ChatHistory chatHistory = await ChatMessages(chatRunDto, promptGuid);
            string serviceId = prompt.ModelInstanceCode;
            var flowNode = await chatRunDto.GetFlowNode();
            var fileIds = flowNode.Config.Files.Where(x => string.IsNullOrEmpty(x.LiteralValue) == false).Select(x =>
            {
                if (Guid.TryParse(x.LiteralValue, out Guid guid))
                {
                    return guid;
                }
                else
                {
                    return Guid.Empty;
                }
            })
            .Where(x => x != Guid.Empty)
            .ToList();
            if (fileIds.Any())
            {
                if (flowNode.Type == SkillNodeTypeConstant.ImageAnalysisNode)
                {
                    serviceId = string.IsNullOrEmpty(serviceId) ? ChatCompletionTypeDto.ImageComprehend : serviceId;
                }
                else if (flowNode.Type == SkillNodeTypeConstant.PromptTemplateNode)
                {
                    serviceId = string.IsNullOrEmpty(serviceId) ? ChatCompletionTypeDto.FileComprehend : serviceId;
                    var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds, 0);
                    if (documents.Any())
                    {
                        int lastSystemIndex = -1; // 记录最后一个 System 消息的位置
                        bool hasSystemMessage = false; // 标记是否存在 System 消息
                                                       // 遍历 chatHistory 以查找 System 消息
                        int insertPosition = 0;
                        foreach (var chatMessageContent in chatHistory)
                        {
                            if (chatMessageContent.Role == AuthorRole.System)
                            {
                                hasSystemMessage = true;
                                lastSystemIndex = insertPosition;
                            }
                            insertPosition++;
                        }

                        var chatMessageContentItemCollection = new ChatMessageContentItemCollection(){new SemanticKernel.Core.Contents.BinaryContent()
                        {
                            Data = new ReadOnlyMemory<byte>(documents[0].FileContent),
                            FileName = documents[0].FileName??"文档"
                        }};


                        // 插入新的 System 消息
                        if (!hasSystemMessage)
                        {
                            // 如果没有 System 消息，在最顶端插入一条 System 消息
                            chatHistory.Insert(0, new ChatMessageContent(role: AuthorRole.System, items: chatMessageContentItemCollection));
                        }
                        else
                        {
                            // 在最后一个 System 消息之后插入新的 System 消息
                            chatHistory.Insert(lastSystemIndex + 1, new ChatMessageContent(role: AuthorRole.System, items: chatMessageContentItemCollection));
                        }
                    }
                }
            }
            else
            {
                serviceId = string.IsNullOrEmpty(serviceId) ? ChatCompletionTypeDto.TextGeneration : serviceId;
            }
            flowNode.Config.Inputs.Add(new ParamDto()
            {
                Code = "ChatHistory",
                LiteralValue = JsonConvert.SerializeObject(chatHistory)
            });
            var result = await ChatCompletion(chatHistory: chatHistory, textEvent: flowNode.Config.AsMessage, serviceId: serviceId, executionSetting: prompt.ExecutionSetting, cancellationToken: cancellationToken);

            await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.TextEvent, result));

            _logger.LogInformation("结果：{0}", result);

            if (chatRunDto.IsStream)
            {
                _contentReviewFactory.CheckOutput(chatRunDto, result, prompt, cancellationToken);
            }
            else
            {
                await _contentReviewFactory.CheckOutput(chatRunDto, result, prompt, cancellationToken);
            }

            if (chatRunDto.ReplaceDtos != null && chatRunDto.ReplaceDtos.Count != 0)
            {
                foreach (var item in chatRunDto.ReplaceDtos)
                {
                    result = result.Replace(item.key, item.value);
                }
            }
            if (prompt.OutputType == PromptOutputTypeEnum.Variables)
            {
                Dictionary<string, object> keyValueDtos = JsonCompile(result);
                List<ParamDto> outputs = await chatRunDto.GetFlowNodeOutputs();
                foreach (var item in outputs)
                {
                    if (keyValueDtos.ContainsKey(item.Code))
                    {
                        item.LiteralValue = keyValueDtos[item.Code] == null ? "" : keyValueDtos[item.Code].ToString();
                    }
                    else
                    {
                        item.LiteralValue = "";
                    }
                    //await chatRunDto.AddNodeOutputArgument(item.Code, item.LiteralValue);
                }
            }
            else
            {
                await AddFirstOutput(result);
            }
            return await Task.FromResult(result);
        }

        public virtual async Task<ChatHistory> ChatMessages(ChatRunDto chatRun, string promptGuid)
        {
            var skillOrchestration = chatRun.SkillOrchestration;
            var prompt = skillOrchestration.Prompts.FirstOrDefault(x => x.Id == Guid.Parse(promptGuid));
            Verify.NotNull(prompt);
            // 提示词是参数列表类型时，特殊处理输出格式
            if (prompt.OutputType == PromptOutputTypeEnum.Variables)
            {
                var flowNode = await chatRun.GetFlowNode();
                var outputs = flowNode.Config.Outputs;
                string formatStr = "";
                TraverseParamDtos(outputs, ref formatStr);
                prompt.PromptTemplate += formatPrompt + formatStr;
            }
            var arguments = await chatRun.GetNodeInputArgument();

            return await CreateChatHistory(prompt, arguments); ;
        }
    }
}
