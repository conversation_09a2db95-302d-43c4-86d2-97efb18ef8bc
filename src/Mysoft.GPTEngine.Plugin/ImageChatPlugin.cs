using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper.Internal;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using Newtonsoft.Json.Linq;
using SqlSugar;
using Mysoft.GPTEngine.Common.CustomerException;

namespace Mysoft.GPTEngine.Plugin
{
    public class ImageChatPlugin : ChatPlugin
    {
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        private readonly ModelRepostory _modelRepostory;
        public readonly string content = "content";
        public readonly string multimodal = "multimodal";
        public readonly string ocr = "ocr";
        public ImageChatPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper, IMysoftContextFactory mysoftContextFactory, ILogger<ChatPlugin> logger,
            ContentReviewFactory contentReviewFactory, IKnowledgeDomainService knowledgeDomainService, ModelInstanceRepostory modelInstanceRepostory, ModelRepostory modelRepostory) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory, logger, knowledgeDomainService, contentReviewFactory)
        {
            _knowledgeDomainService = knowledgeDomainService;
            _modelInstanceRepostory = modelInstanceRepostory;
            _modelRepostory = modelRepostory;
        }
        [KernelFunction]
        [Description("Get Streaming ChatMessage Contents.")]
        public override async Task<string> StreamingChatCompletionAsync([Description("提示词模板ID")] string promptGuid, CancellationToken cancellationToken)
        {
            var chatRun = await GetChatRunDto();
            if (chatRun.Cancel) return string.Empty;

            var flowNode = await chatRun.GetFlowNode();
            #region 参数校验
            await FileArgumentCheck(flowNode);
            await ImageChatCheck(flowNode);
            await NodeArgumentCheck(chatRun);
            var fileIdCheck = flowNode.Config.Files.Where(x => string.IsNullOrEmpty(x.LiteralValue) == false).Select(x =>
            {
                if (Guid.TryParse(x.LiteralValue, out Guid guid))
                {
                    return guid;
                }
                else
                {
                    return Guid.Empty;
                }
            })
            .Where(x => x != Guid.Empty)
            .ToList();
            if (fileIdCheck.Count == 0)
            {
                return string.Empty;
            }
            #endregion
            if (flowNode == null || flowNode.Config?.Files.Count == 0 || flowNode.Config.Files.All(x => string.IsNullOrEmpty(x.LiteralValue)))
            {
                await AddFirstOutput(string.Empty);
                return await Task.FromResult(string.Empty);
            }

            if (flowNode.Config.RecognizeType == "ocr")
            {
                if (flowNode.Config.Pattern == (int)DocumentAnalysisTypeEnum.InformationExtraction)
                {
                    var task = await Ocr(chatRun, cancellationToken);
                    List<ParamDto> outputs = await chatRun.GetFlowNodeOutputs();
                    foreach (var item in outputs)
                    {
                        if (task.ContainsKey(item.Code))
                        {
                            item.LiteralValue = task[item.Code].ToString();
                        }
                        else
                        {
                            item.LiteralValue = "";
                        }
                        //await chatRun.AddNodeOutputArgument(item.Code,item.LiteralValue);
                    }
                }
                else
                {
                    int minProb = await getMinProb(flowNode.Config.OcrService);
                    var fileIds = flowNode.Config.Files.Where(x => string.IsNullOrEmpty(x.LiteralValue) == false).Select(x => Guid.Parse(x.LiteralValue)).ToList();

                    var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds, 0);
                    var ocrRecognizeService = _kernel.GetRequiredService<IOcrRecognizeService>(flowNode.Config.OcrService);
                    var result = "";
                    foreach (var document in documents)
                    {
                        var imgContent = await ocrRecognizeService.Execute(new OcrRequest { Body = document.FileContent,ocrCode = flowNode.Config.OcrService });
                        if(!imgContent.Success || imgContent.Content == null)
                        {
                            Console.WriteLine("Ocr识别失败");
                            continue;
                        }
                        var imgContentContent = handleMinProd(imgContent, minProb);
                        chatRun.AddWordsResult(imgContent.WordsResult);
                        result = result + imgContentContent + "\r\n";
                    }
                    List<ParamDto> outputs = await chatRun.GetFlowNodeOutputs();
                    foreach (var item in outputs)
                    {
                        if(item.Code == content)
                        {
                            item.LiteralValue = result;
                        }
                        
                    }
                    return await Task.FromResult(string.Empty);
                }


                return await Task.FromResult(string.Empty);
            }
            else
            {
                return await base.StreamingChatCompletionAsync(promptGuid, cancellationToken);
            }
        }

        private async Task ImageChatCheck(FlowNode flowNode)
        {
            if(flowNode.Config.RecognizeType == multimodal)
            {
                if (string.IsNullOrEmpty(flowNode.Config.TemplateId))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】提示词没有设置");
                }
            }
            else if (flowNode.Config.RecognizeType == ocr)
            {
                if (string.IsNullOrEmpty(flowNode.Config.OcrService))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】视觉识别服务没有设置");
                }
            }
            await Task.CompletedTask;
        }

        public override async Task<ChatHistory> ChatMessages(ChatRunDto chatRun, string promptGuid)
        {
            var skillOrchestration = chatRun.SkillOrchestration;
            var prompt = skillOrchestration.Prompts.FirstOrDefault(x => x.Id == Guid.Parse(promptGuid));
            Verify.NotNull(prompt);

            var arguments = await chatRun.GetNodeInputArgument();

            var promptTemplate = await TemplateRenderAsync(promptTemplateText: prompt.PromptTemplate, arguments: arguments);
            var flowNode = await chatRun.GetFlowNode();

            var fileIds = flowNode.Config.Files.Where(x => string.IsNullOrEmpty(x.LiteralValue) == false).Select(x => Guid.Parse(x.LiteralValue)).ToList();

            var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds, 0);
            var messageCollection = new ChatMessageContentItemCollection() { new TextContent { Text = promptTemplate.Content } };
            foreach (var document in documents)
            {
                var image = new ImageContent()
                {
                    Metadata = new Dictionary<string, object?>()
                    {
                        { nameof(document.FileName), document.FileName },
                        { nameof(document.FileContent), document.FileContent }
                    }
                };
                messageCollection.Add(image);
            }

            var chatMessages = new ChatHistory();
            chatMessages.AddUserMessage(messageCollection);

            return chatMessages;
        }
        
        public async Task<Dictionary<string, object>> Ocr(ChatRunDto chatRun, CancellationToken cancellationToken)
        {
            List<ParamDto> outputs = await chatRun.GetFlowNodeOutputs();
            var flowNode = await chatRun.GetFlowNode();
            //todo：获取上下文中识别的阈值 默认 70
            int minProb = await getMinProb(flowNode.Config.OcrService);
            string str = "";
            // 组装json scheme
            TraverseParamDtos(outputs, ref str);
            var fileIds = flowNode.Config.Files.Where(x => string.IsNullOrEmpty(x.LiteralValue) == false).Select(x => Guid.Parse(x.LiteralValue)).ToList();
            var documents = await _knowledgeDomainService.GetDocumentInfo(fileIds, 0);
            
            if (documents == null || documents.Count == 0)
            {
                return new Dictionary<string, object>();
            }
            var ocrRecognizeService = _kernel.GetRequiredService<IOcrRecognizeService>(flowNode.Config.OcrService);
            var imageContent = "";
            foreach (var document in documents)
            {
                var imgContent = await ocrRecognizeService.Execute(new OcrRequest { Body = document.FileContent,ocrCode = flowNode.Config.OcrService });
                if (imgContent != null && imgContent.WordsResult != null && imgContent.WordsResult.Count > 0)
                {
                    chatRun.AddWordsResult(imgContent.WordsResult);
                }
                imageContent = imageContent + "\r\n" + handleMinProd(imgContent, minProb);
            }
            if (string.IsNullOrEmpty(imageContent.Replace("\r\n", "")))
            {
                return new Dictionary<string, object>();
            }
            var arguments = new KernelArguments { { nameof(outputs), str }};
            var result = await ChatCompletion(systemPromptName: EmbeddedResource.DocumentAnalysisPlugin_FixedGenerate, input: imageContent, arguments,serviceId: chatRun.ModelInstance.InstanceCode, cancellationToken: cancellationToken);
            Console.WriteLine("信息提取：" + result);
            //组装一下json
            return JsonCompile(result);
        }

        private string handleMinProd(OcrResponse imgContent, int minProb)
        {
            String result = "";
            var imgContentWordsResult = imgContent.WordsResult;
            //循环遍历imgContentWordsResult
            foreach (var item in imgContentWordsResult)
            {
                //判断item.Prob是否大于等于minProb
                if (item.Prob >= minProb)
                {
                    //返回item.Words
                    result = result + " " + item.Words;
                }
            }

            return result;
        }

        private async Task<int> getMinProb(string configOcrService)
        {
            var modelInstances = await _modelInstanceRepostory.GetFirstAsync(x => x.InstanceCode == configOcrService);
            var modelEntity = await _modelRepostory.GetFirstAsync((x => x.ModelGUID == modelInstances.ModelGUID));
            var modelEntityExecutionSetting = modelEntity.ExecutionSetting;
            JObject executionSettingValue = JObject.Parse(modelEntityExecutionSetting);
            // 获取 minProd 的值
            return executionSettingValue.Value<int>("minProb");
        }
    }
}
