using AutoMapper;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Parsers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Paragraph = DocumentFormat.OpenXml.Wordprocessing.Paragraph;
using Mysoft.GPTEngine.Common.Helper;
using static Mysoft.GPTEngine.Plugin.System.DocumentAnalysisPlugin;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using DocumentFormat.OpenXml.Office2010.Word;
using SqlSugar;
using RestSharp.Extensions;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json.Linq;
using Aspose.Words.Drawing.Charts;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.CustomerException;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;

namespace Mysoft.GPTEngine.Plugin.System
{
    public class DocumentAnalysisPlugin : PluginBase
    {
        private readonly IJsonOutputParsers _jsonOutputParsers;

        private readonly IKnowledgeDomainService _knowledgeDomainService;

        private readonly ILogger<DocumentAnalysisPlugin> _logger;

        public readonly string fileInput = "fileInput";
        public readonly string content = "content";
        public readonly string fields = "fields";
        public readonly string tables = "tables";
        public readonly string ocr = "ocr";
        private readonly MysoftApiService _mysoftApiDomainService;
        public DocumentAnalysisPlugin(MysoftApiService mysoftApiDomainService,Kernel kernel, MemoryBuilder memoryBuilder, IJsonOutputParsers jsonOutputParsers, IHttpContextAccessor httpContextAccessor, IMapper mapper,
            IKnowledgeDomainService knowledgeDomainService, IMysoftContextFactory mysoftContextFactory, ILogger<DocumentAnalysisPlugin> logger) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _mysoftApiDomainService = mysoftApiDomainService;
            _jsonOutputParsers = jsonOutputParsers;
            _knowledgeDomainService = knowledgeDomainService;
            _logger = logger;
        }

        [KernelFunction]
        [Description("文档解析")]
        public async Task DocumentAnalysis(CancellationToken cancellationToken)
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return;
            var node = await chatRunDto.GetFlowNode();
            var files = await chatRunDto.GetFlowNodeFiles();
            #region 参数校验
            await FileArgumentCheck(node);
            await DocumentAnalysisCheck(node);
            await NodeArgumentCheck(chatRunDto);
            #endregion
            var currentDocumentKey = files.FirstOrDefault(x => x.Code == fileInput && x.Value.Type == "ref")?.Value.Content ?? "";
            if (!chatRunDto.ChatArguments.ContainsName(currentDocumentKey))
            {
                return ;
            }
            var currentDocument = chatRunDto.ChatArguments[currentDocumentKey]?.ToString();
            if (!currentDocument.HasValue())
            {
                return ;
            }
            if (!Guid.TryParse(currentDocument, out Guid documentGUID))
            {
                return;
            }
            if (node.Config.Pattern == (int)DocumentAnalysisTypeEnum.DocumentRead)
            {
                List<ParamDto> outputs = await chatRunDto.GetFlowNodeOutputs();
                DocumentReadingDto documentReadingDto = await DocumentReading(documentGUID, node.Config);
                InitDocumentInfo(outputs, documentReadingDto);
            }
            else if (node.Config.Pattern == (int)DocumentAnalysisTypeEnum.InformationExtraction)
            {
                List<ParamDto> outputs = await chatRunDto.GetFlowNodeOutputs();
                string str = "";
                // 组装json scheme
                TraverseParamDtos(outputs, ref str);
                Dictionary<string, object> keyValueDtos = await InformationExtraction(documentGUID, str, chatRunDto.ModelInstance.InstanceCode, node.Config, cancellationToken);
               foreach (var item in outputs)
                {
                    if (keyValueDtos.ContainsKey(item.Code))
                    {
                        item.LiteralValue = keyValueDtos[item.Code] == null ? "" : keyValueDtos[item.Code].ToString();
                    }
                    else
                    {
                        item.LiteralValue = "";
                    }
                    //await chatRunDto.AddNodeOutputArgument(item.Code,item.LiteralValue);
                }
            }
            return ;
        }

        private async Task DocumentAnalysisCheck(FlowNode flowNode)
        {
            if(flowNode.Config.ImageRecognizeType == ocr && string.IsNullOrEmpty(flowNode.Config.OcrService))
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】视觉识别服务没有设置");
            }
            await Task.CompletedTask;
        }

        private void InitDocumentInfo(List<ParamDto> outputs, DocumentReadingDto documentReadingDto)
        {
            foreach (var item in outputs)
            {
                if(item.Code == content)
                {
                    item.LiteralValue = documentReadingDto.Content;
                }
                if (item.Code == fields)
                {
                    item.LiteralValue = documentReadingDto.FieldList;
                }
                if (item.Code == tables)
                {
                    item.LiteralValue = JsonConvert.SerializeObject(documentReadingDto.Tables);
                }
            }
        }

        public async Task<Dictionary<string, object>> InformationExtraction(Guid documentGUID,string outputs,string modelCode,NodeConfig nodeConfig, CancellationToken cancellationToken)
        {
            _logger.LogInformation("文档解析documentGUID：{0}", documentGUID);
            //先返回卡片格式，到时候看看要不要改
            var interactiveCard = new FormCardDto();
            //发送一次提醒
            //await TextEvent("我即将进行【文本填充】，以下是根据你的文档提取的表单信息，确认后我将进一步执行。", true);

            List<string> res = new List<string>();
            List<DocumentInfoBaseDto> documentInfoBaseDtos = await _knowledgeDomainService.GetDocumentInfo(new List<Guid> { documentGUID });
            if (documentInfoBaseDtos == null || documentInfoBaseDtos.Count == 0)
            {
                return new Dictionary<string, object>();
            }
            //拿到下载地址
            string downloadUrl = documentInfoBaseDtos[0].DownloadUrl;
            string fileName = documentInfoBaseDtos[0].FileName;
            string type = FileTypeConvertHelper.GetFileType(fileName);
            IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(type,this._kernel, this._mysoftApiDomainService, this._mysoftContextFactory, this._httpContextAccessor, this._mapper);
            string md = "";
            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(downloadUrl); // 下载文件

                MemoryStream stream = new MemoryStream(fileBytes);
                md = await documentDecoder.GetDocumentTxt(stream, nodeConfig);
            }
            var arguments = new KernelArguments { { nameof(outputs), outputs }};
            var result = await ChatCompletion(systemPromptName: EmbeddedResource.DocumentAnalysisPlugin_FixedGenerate, input: md, arguments,serviceId: modelCode, cancellationToken: cancellationToken);
             Console.WriteLine("信息提取：" + result);
            //组装一下json
            return JsonCompile(result);
        }

        //文本读取
        public async Task<DocumentReadingDto> DocumentReading(Guid documentGUID,NodeConfig nodeConfig)
        {
            DocumentReadingDto documentReadingDto = new DocumentReadingDto();
            List<DocumentInfoBaseDto> documentInfoBaseDtos = await _knowledgeDomainService.GetDocumentInfo(new List<Guid> { documentGUID });
            if (documentInfoBaseDtos == null || documentInfoBaseDtos.Count == 0)
            {
                return documentReadingDto;
            }
            //拿到下载地址
            string downloadUrl = documentInfoBaseDtos[0].DownloadUrl;
            string fileName = documentInfoBaseDtos[0].FileName;
            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(downloadUrl); // 下载文件

                MemoryStream stream = new MemoryStream(fileBytes);
                string type = FileTypeConvertHelper.GetFileType(fileName);
                IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(type, this._kernel, this._mysoftApiDomainService, this._mysoftContextFactory, this._httpContextAccessor, this._mapper);
                documentReadingDto = await documentDecoder.DomentReadAsync(stream, nodeConfig);
                
            }
            return documentReadingDto;
        }

        [KernelFunction]
        [Description("生成固定值参数的字段信息")]
        public async Task<string> FixedGenerateFields()
        {
            return await Task.FromResult("");
        }

        public static bool IsJsonValid(string jsonString)
        {
            try
            {
                JsonConvert.DeserializeObject(jsonString);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public class DocumentFillDto
        {
            public string Type { get; set; } = "action-fill-form";
            public List<string> Data { get; set; } = new List<string>();
            public string content { get; set; }


        }

        /// <summary>
        /// 计算一下token并且拼接
        /// </summary>
        /// <param name="strings"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        private List<string> CalcTokenAndAppend(List<string> strings, int token)
        {
            StringBuilder result = new StringBuilder(); // 用于拼接结果的 StringBuilder 对象
            string[] groupedStrings = new string[strings.Count]; // 用于存储超过限制值的字符串数组

            int groupCount = 0; // 存储超过限制值的字符串数组的索引

            for (int i = 0; i < strings.Count; i++)
            {
                // 判断拼接后是否超过限制值
                if (s_tokenizer.CountTokens(result + strings[i]) <= token)
                {
                    result.Append(strings[i]);
                }
                else
                {
                    groupedStrings[groupCount] = result.ToString(); // 当前拼接结果超过限制值，存储起来
                    result.Clear();
                    result.Append(strings[i]);
                    groupCount++;
                }
            }

            // 处理最后一组
            if (result.Length > 0 && s_tokenizer.CountTokens(result + groupedStrings[groupCount - 1]) <= token)
            {
                groupedStrings[groupCount] = result.ToString();
                groupCount++;
            }
            else if (result.Length > 0)
            {
                groupedStrings[groupCount - 1] = groupedStrings[groupCount - 1] + result.ToString();
            }

            var list = groupedStrings.ToList();
            list.RemoveAll(string.IsNullOrEmpty);
            return list;
        }

        /// <summary>
        /// 解析信息
        /// </summary>
        /// <param name="downloadUrl"></param>
        /// <param name="analysisTypeEnum"></param>
        /// <returns></returns>
        private List<string> Analysis(string downloadUrl, int analysisTypeEnum)
        {
            List<string> res = new List<string>();
            //解析一下文本
            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(downloadUrl); // 下载文件

                using (MemoryStream stream = new MemoryStream(fileBytes)) // 将文件转换为文件流
                {
                    //打开一个open xml
                    var wordprocessingDocument = WordprocessingDocument.Open(stream, false);

                    // 获取文档的主体部分
                    Body body = wordprocessingDocument.MainDocumentPart.Document.Body;

                    //按解析方式处理
                    switch (analysisTypeEnum)
                    {
                        case (int)AnalysisTypeEnum.UnderLine:
                            // 查找带有下划线的段落
                            var underlinedParagraphs = body.Descendants<Paragraph>()
                                .Where(p => p.Descendants<DocumentFormat.OpenXml.Wordprocessing.Underline>().Any());

                            // 遍历并打印带有下划线的段落文本
                            foreach (var paragraph in underlinedParagraphs)
                            {
                                if (!string.IsNullOrWhiteSpace(paragraph.InnerText) && paragraph.InnerText != "null")
                                {
                                    res.Add(paragraph.InnerText);
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            return res;
        }
    }
}
