using AutoMapper;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Office2019.Excel.RichData2;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.Embeddings;
using Mysoft.GPTEngine.Plugin.Resources;
using Newtonsoft.Json;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.DTO;

namespace Mysoft.GPTEngine.Plugin.System
{
#pragma warning disable SKEXP0001 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
    public class KnowledgePlugin : PluginBase
    {
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly IMilvusMemoryDomainService _milvusMemoryDomainService;
        private readonly ILogger<KnowledgePlugin> _logger;

        [KernelFunction]
        [Description("简单检索")]
        public async Task SimpleSearch([Description("问题")] string? input = null
            , [Description("知识库编码集合")] string? knowledeCodes = null
            , [Description("启用相识问")] bool enableRecommend = true
            , [Description("返回条数")] int limit = 3
            , [Description("最低权值")] double minRelevanceScore = 0.6
            , [Description("分析模式")] int pattern = 0
            , [Description("取消令牌")] CancellationToken cancellationToken = default)
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return;
            var flowNode = await chatRunDto.GetFlowNode();

            if (string.IsNullOrWhiteSpace(knowledeCodes))
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】知识库没有设置");
            }
            await NodeArgumentCheck(chatRunDto);

            var inputs = await chatRunDto.GetFlowNodeInputs();
            input = inputs.FirstOrDefault(x => x.Code == nameof(input))?.LiteralValue ?? input;
            //指代消除
            if (flowNode.Config.IsCOR)
            {
                var optimizationQuestionDto = await getOptimizationQuestionDto(input);
                if (optimizationQuestionDto.Context != null && optimizationQuestionDto.Context.Count > 0)
                {
                    var questionInput = JsonConvert.SerializeObject(optimizationQuestionDto);
                    var arguments = new KernelArguments { { nameof(questionInput), questionInput }};
                    var serviceId = string.IsNullOrEmpty(chatRunDto.ModelInstance.InstanceCode) ? ChatCompletionTypeDto.TextGeneration : chatRunDto.ModelInstance.InstanceCode;
                    input = await ChatCompletion(systemPromptName: EmbeddedResource.KnowledgePlugin_QuestionOptimization, input: input, arguments:arguments, serviceId: serviceId, cancellationToken: cancellationToken);
                }
            }
            if (input == null) return;
            var knowledgeQueryEmbeddingMap = await _knowledgeDomainService.GetKnowledgeQueryEmbeddingMap(knowledeCodes, input, _kernel).ConfigureAwait(false);

            await CreateQuestion(chatRunDto, knowledgeQueryEmbeddingMap, limit, minRelevanceScore, cancellationToken).ConfigureAwait(false);
            
            var topSectionList = await _knowledgeDomainService.GetQueryTopResult(knowledgeQueryEmbeddingMap, limit, minRelevanceScore).ConfigureAwait(false);
            
            var totalIdList = topSectionList.OrderByDescending(x => x.score).Take(limit).Select(x => x.id).ToList();
            //拿到替换了图片信息的切片以及占位符文件
            ImageUrlDto imageUrl = await _knowledgeDomainService.GetKnowledgeFileSections(totalIdList).ConfigureAwait(false);
            
            if (imageUrl.replaceDtos.Count > 0)
            {
                chatRunDto.ReplaceDtos = imageUrl.replaceDtos;
                await ReplaceEvent(imageUrl.replaceDtos, chatRunDto.IsStream).ConfigureAwait(false);
            }
            var knowledgeFileSections = imageUrl.knowledgeFileSectionDtos;

            //格式化返回结果
            var result = FormatResult(knowledgeFileSections, pattern);

            //创建会话节点知识库日志
            await chatRunDto.AddKnowledgeNodeLog(knowledgeFileSections, topSectionList, input).ConfigureAwait(false);
            
            if (flowNode.Config.AsSource && knowledgeFileSections != null && knowledgeFileSections.Count != 0 && chatRunDto.IsStream)
            {
                await SourceEvent(knowledgeFileSections);
            }
            _logger.LogInformation("知识库-简单检索:问题:{0};结果：{1}", input, result);

            await AddFirstOutput(result);
        }

        private string FormatResult(List<KnowledgeFileSectionDto> knowledgeFileSections, int pattern)
        {
            string result = "";
            //TODO 如果拉取到了同步过来的数据，特殊处理返回内容
            if (knowledgeFileSections.Exists(e => e.FileSourceEnum == (int)FileResourceEnum.OpenMingyy))
            {
                result = GenerateRes(knowledgeFileSections);
            }
            else
            {
                if (pattern == 1)
                {
                    var mapList = knowledgeFileSections.Select(x =>
                    {
                        var map = new Dictionary<string, object>();
                        map.Add("content", x.Content);
                        map.Add("title", x.ParagraphTitle);
                        map.Add("url", x.FileUrl);
                        var metadataMap = new Dictionary<string, string>();
                        if (!String.IsNullOrWhiteSpace(x.Metadata))
                        {
                            metadataMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.Metadata);
                        }
                        map.Add("metadata", metadataMap);
                        return map;
                    });
                    result = JsonConvert.SerializeObject(mapList);
                }
                else
                {
                    result = knowledgeFileSections?.Count > 0 ? string.Join("\r\n", knowledgeFileSections.Select(x => x.Content)) : string.Empty;
                }

            }

            return result;
        }


        private async Task CreateQuestion(ChatRunDto chatRunDto, Dictionary<string, ReadOnlyMemory<float>> knowledgeQueryEmbeddingMap, int limit, double minRelevanceScore, CancellationToken cancellationToken = default)
        {
            List<MemoryQueryResult> questionQueryResults = new List<MemoryQueryResult>();
            foreach (var knowledgeQueryEmbedding in knowledgeQueryEmbeddingMap)
            {
                //查询问题
                await foreach (var answer in this._milvusMemoryDomainService.SearchAsync(knowledgeQueryEmbedding.Key, knowledgeQueryEmbedding.Value, limit, minRelevanceScore, false, (int)SourceTypeEnum.Question, cancellationToken))
                {
                    questionQueryResults.Add(answer);
                }
            }

            List<Guid> questionGUIDs = questionQueryResults.Select(s => Guid.Parse(s.Metadata.Id)).ToList();
            // 根据问题id查询问题对象
            var questionDtos = await _knowledgeDomainService.GetQuestionByQuestionGUID(questionGUIDs).ConfigureAwait(false);
            //查到了问题就直接生成推荐问
            if (questionDtos.Count > 0)
            {
                var recommend = new KnowledgeRecommendDto
                {
                    Data = questionDtos.Select(s => s.Question).Distinct().ToList()
                };
                await DataEvent(recommend, chatRunDto.IsStream);
            }
        }

        private async Task<ReadOnlyMemory<float>> GetQueryEmbedding(String input, String memoryCollectionName, Kernel _kernel,
            Dictionary<string, ReadOnlyMemory<float>> embeddingMap,
            Dictionary<string, ITextEmbeddingGenerationService> embeddingGenerationServicesMap, CancellationToken cancellationToken = default)
        {
            if (embeddingMap.ContainsKey(memoryCollectionName))
            {
                return embeddingMap[memoryCollectionName];
            }
            var embeddingGenerationService = embeddingGenerationServicesMap[memoryCollectionName];
            if (embeddingGenerationServicesMap.ContainsKey(memoryCollectionName))
            {
                var serviceKey = await _knowledgeDomainService.GetEmbeddingModelCodeByKnowledgeCode(memoryCollectionName);
                embeddingGenerationService = _kernel.GetRequiredService<ITextEmbeddingGenerationService>(serviceKey);
            }
            return await embeddingGenerationService.GenerateEmbeddingAsync(input, _kernel, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// 组装数据
        /// </summary>
        /// <param name="knowledgeFileSectionDtos"></param>
        /// <returns></returns>
        private string GenerateRes(List<KnowledgeFileSectionDto> knowledgeFileSectionDtos)
        {
            string result = "";
            foreach (var item in knowledgeFileSectionDtos)
            {
                if (item.FileSourceEnum == (int)FileResourceEnum.OpenMingyy)
                {
                    result +=
@$"```yaml
title: ""{item.FileName.Replace(".txt", "")}""
source: ""{item.FileUrl}""
content: ""{item.Content}""
```
";
                }
                else if (item.FileSourceEnum == (int)FileResourceEnum.GPT)
                {
                    result +=
@$"```yaml
content: ""{item.Content}""
```
";
                }
            }

            return result;
        }

        public KnowledgePlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper
            , IKnowledgeDomainService knowledgeDomainService, IMilvusMemoryDomainService milvusMemoryDomainService, IMysoftContextFactory mysoftContextFactory
            , ILogger<KnowledgePlugin> logger
            ) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _knowledgeDomainService = knowledgeDomainService;
            _milvusMemoryDomainService = milvusMemoryDomainService;
            _logger = logger;
        }

        private async Task SourceEvent(List<KnowledgeFileSectionDto> knowledgeFileSections)
        {
            if (knowledgeFileSections == null || knowledgeFileSections.Count == 0) return;
            var source = new KnowledgeSourceDto();

            var files = knowledgeFileSections
                .GroupBy(x => new { x.FileName, x.FileSize, x.FileType })
                .Select(group => group.First())
                .ToList();

            source.Data = files.Select(x => new KnowledgeSourceDataDto { Name = x.FileName, Type = x.FileType, Url = x.FileUrl, Size = $"{(x.FileSize / 1024)}k" }).ToList();
            //判断是否需要输出文档  不需要的话不用输出

            await DataEvent(source, true);
        }
        
        public async Task<ProblemOptimizationDto> getOptimizationQuestionDto(string input)
        {
            var problemOptimizationDto = new ProblemOptimizationDto();
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Nodes.Count == 0) return await Task.FromResult(problemOptimizationDto);
            var currentNode = await chatRunDto.GetFlowNode();
            
            var nodeGUID = chatRunDto.Chat.CurrentNodeGUID;
            var nodeLogs = chatRunDto.NodeLogs.Where(x => x.NodeGUID == nodeGUID && !x.BatchGUID.Equals("00000000-0000-0000-0000-000000000000")).OrderByDescending(x => x.Index).Take(5).OrderBy(x => x.Index).ToList();
            if (nodeLogs.Count == 0) return await Task.FromResult(problemOptimizationDto); 
            List<ProblemOptimizationDto.HistoryContextDto> contextDtos = new List<ProblemOptimizationDto.HistoryContextDto>();
            foreach (var nodeLog in nodeLogs)
            {
                var historyContextDto = new ProblemOptimizationDto.HistoryContextDto();
                historyContextDto.Sender = nodeLog.Inputs;
                historyContextDto.Content = nodeLog.Outputs;
                contextDtos.Add(historyContextDto);
            }
            problemOptimizationDto.Query = input;
            problemOptimizationDto.Context = contextDtos;
            return await Task.FromResult(problemOptimizationDto);
        }
    }

    public class KnowledgeRecommendDto
    {
        public string Type { get; set; } = "recommend";
        public List<string> Data { get; set; } = new List<string>();
    }
    public class KnowledgeSourceDto
    {
        public string Type { get; set; } = "source";

        public List<KnowledgeSourceDataDto> Data { get; set; } = new List<KnowledgeSourceDataDto>();
    }
    public class FormBindingDto
    {
        public string Type { get; set; } = "form-binding";

        public AutoInputDto Data { get; set; } = new AutoInputDto();
    }
    public class KnowledgeSourceDataDto
    {
        public string Name { get; set; } = string.Empty;
        public string Size { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }

}
