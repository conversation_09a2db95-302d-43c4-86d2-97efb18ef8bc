using System;
using System.Collections.Generic;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;

namespace Mysoft.GPTEngine.Plugin.System
{
    public class CardPlugin : PluginBase
    {
        private readonly ILogger<CardPlugin> _logger;

        public CardPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper,
            IMysoftContextFactory mysoftContextFactory, ILogger<CardPlugin> logger) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _logger = logger;
        }

        [KernelFunction]
        [Description("交互卡片")]
        public async Task Interactive()
        {
            var chaRunDto = await GetChatRunDto();
            if (chaRunDto.Cancel) return;

            var inputExpression = "{{${0}}}";

            var flowNode = await chaRunDto.GetFlowNode();
            await NodeArgumentCheck(chaRunDto);
            _logger.LogInformation("交互卡片: {0}", flowNode.Config.Content);

            var inputs = await chaRunDto.GetFlowNodeInputs();
            var interactiveCard = new FormCardDto();
            interactiveCard.Data.Content = flowNode.Config.Content;
            interactiveCard.Data.TemplateId = flowNode.Config.TemplateId ?? MysoftConstant.Interactive_Form;
            //todo：数据结构处理
            foreach (var input in inputs)
            {
                interactiveCard.Data.Content = interactiveCard.Data.Content.Replace(
                    string.Format(inputExpression, input.Code), string.Format(inputExpression, input.LiteralCode));
                
                interactiveCard.Data.Config.Add(new FormConfigDto
                { Code = input.LiteralCode, Name = input.Name, Type = input.Type, Required = input.Required });
                interactiveCard.Data.Data.Add(new KeyValueDto { Key = input.LiteralCode, Value = input.LiteralValue, Code = input.Code, Type = input.Type});
            }
            await chaRunDto.OutputsArgumentParser();
            foreach (var item in flowNode.Config.Outputs)
            {
                //await chaRunDto.AddNodeOutputArgument(item.Code, item.LiteralValue);
            }
            interactiveCard.Data.Outputs = flowNode.Config.Outputs;

            await TextEvent(flowNode.Description ?? string.Empty, chaRunDto.IsStream);
            await DataEvent(interactiveCard, true);
        }

        [KernelFunction]
        [Description("消息卡片")]
        public async Task Info([Description("卡片内容")] string content)
        {
            var chaRunDto = await GetChatRunDto();
            if (chaRunDto.Cancel) return;


            var flowNode = await chaRunDto.GetFlowNode();
            Verify.NotNull(content);

            content = await ParameterDecryption(content);

            var arguments = await (await GetChatRunDto()).GetNodeInputArgument();

            var template = await TemplateRenderAsync(promptTemplateText: content, arguments: arguments);
            flowNode.Config.Content = template.Content;
            await TextEvent(template.Content, chaRunDto.IsStream);
            await AddMessage(AuthorRole.Assistant.Label, string.Format(EventDataConstant.TextEvent, template.Content));
        }


        [KernelFunction]
        [Description("表单卡片")]
        public async Task FormCard()
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return;

            var flowNode = await chatRunDto.GetFlowNode();
            await NodeArgumentCheck(chatRunDto);
            var cardParamDtos = flowNode.Config.Props;
            // 处理卡片标题
            var inputs = await chatRunDto.GetFlowNodeInputs();
            var title = flowNode.Config.Title;
            foreach (var item in inputs)
            {
                title = title.Replace($"{{{{${item.Code}}}}}", item.LiteralValue);
            }
            
            var PageParamValueDto = new FormCardDataDto
            {
                TemplateId = flowNode.Config.TemplateId,
                Props = flowNode.Config.Props,
                Title = title,
                Outputs = flowNode.Config.Outputs,
                NodeCode = flowNode.Code,
            };
            var wordsResults = chatRunDto.WordsResult;
            //wordsResults装换为map对象
            Dictionary<string, int> wordsResultsMap = wordsResults
                .GroupBy(x => x.Words)
                .ToDictionary(
                    g => g.Key,
                    g => g.Max(x => GetLevelByProb(x.Prob, PageParamValueDto))
                );
            PageParamValueDto.ProbResult = wordsResultsMap;
            var pageCardDto = new FormCardDto
            {
                Data = PageParamValueDto
            };
            await chatRunDto.OutputsArgumentParser();
            foreach (var item in flowNode.Config.Outputs)
            {
         
                //await chatRunDto.AddNodeOutputArgument(item.Code, item.LiteralValue);
            }
            await DataEvent(pageCardDto, chatRunDto.IsStream);
        }
        
        public int GetLevelByProb(double prob, FormCardDataDto interactiveCardData)
        {
            foreach (var rule in interactiveCardData.ProbRules)
            {
                if (prob >= rule.MinProb && prob < rule.MaxProb)
                {
                    return rule.Priority;
                }
            }
            // 如果没有找到匹配的范围，返回默认等级（例如 -1 表示未找到）
            return -1;
        }

        [KernelFunction]
        [Description("页面")]
        public async Task Page()
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return;

            var flowNode = await chatRunDto.GetFlowNode();

            _logger.LogInformation("卡片页面:{0}", flowNode.Config.Props);

            var cardParamDtos = flowNode.Config.Props;
            var urlValue = cardParamDtos.FirstOrDefault(x => x.Name == "url")?.Value;
            if (urlValue == null) throw new NoRequiredArgumentException($"节点【{flowNode.Name}】卡片地址没有设置");

            //装换 content类型为CardParamValueDto
            var urlContent = JsonConvert.DeserializeObject<CardParamValueDto>(urlValue.ToString());
            var newContent = urlContent.Type != "ref" ? urlContent.Content : await ResolveUrlContent(urlContent.Content, chatRunDto);

            var props = cardParamDtos.Select(p => new PagePropDto
            {
                Name = p.Name,
                Value = p.Name == "url" ? newContent : p.Value
            }).ToList();

            var PageParamValueDto = new PageParamValueDto
            {
                Id = flowNode.Config.TemplateId,
                props = props

            };
            var pageCardDto = new PageCardDto
            {
                Data = PageParamValueDto
            };
            await DataEvent(pageCardDto, chatRunDto.IsStream);
        }

        private async Task<string> ResolveUrlContent(string content, ChatRunDto chatRunDto)
        {
            if (content == KernelArgumentsConstant.Input || content.StartsWith(KernelArgumentsConstant._prefixKeyword))
            {
                return await chatRunDto.GetArgumentValue<string>(content);
            }

            var keys = content.Split('_');
            if (keys.Length < 3) throw new ArgumentException("Invalid content format.");
            var prefix = keys[0];
            var nodeCode = keys[1];
            var parameterCode = content.Replace($"{prefix}_{nodeCode}_", "");
            var paramFlowNode = await chatRunDto.GetFlowNode(nodeCode);
            var item = prefix == KernelArgumentsConstant._prefixNodeInput
                ? paramFlowNode.Config.Inputs.FirstOrDefault(x => x.Code == parameterCode)
                : paramFlowNode.Config.Outputs.FirstOrDefault(x => x.Code == parameterCode);

            if (item == null) throw new ArgumentNullException(nameof(item));

            return string.IsNullOrWhiteSpace(item.LiteralValue)
                ? await chatRunDto.GetArgumentValue<string>(content)
                : item.LiteralValue;
        }
    }
}