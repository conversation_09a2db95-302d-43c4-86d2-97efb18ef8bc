using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System.Diagnostics;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;

namespace Mysoft.GPTEngine.Plugin
{
    public class ApiPlugin : PluginBase
    {
        private readonly IMysoftApiService _mysoftApiService;
        private readonly IMysoftIPassApiService _mysoftIPassApiService;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly IMysoftCustomerServiceApiService _mysoftCustomerServiceApiService;
        private readonly ILogger<ApiPlugin> _logger;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;

        public ApiPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper, IMysoftApiService mysoftApiService
        , IMysoftIPassApiService mysoftIPassApiService, MysoftConfigurationDomain mysoftConfigurationDomain, IMysoftCustomerServiceApiService mysoftCustomerServiceApiService, IMysoftContextFactory mysoftContextFactory, ILogger<ApiPlugin> logger) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _mysoftApiService = mysoftApiService;
            _mysoftIPassApiService = mysoftIPassApiService;
            _mysoftCustomerServiceApiService = mysoftCustomerServiceApiService;
            _mysoftContextFactory = mysoftContextFactory;
            _logger = logger;
            _mysoftConfigurationDomain = mysoftConfigurationDomain;
        }

        [KernelFunction]
        [Description("调用明源云API")]
        public async Task MysoftApi([Description("API地址")] string uri, CancellationToken cancellationToken)
        {
            Verify.NotNull(uri);

            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return ;

            var node = await chatRunDto.GetFlowNode();
            // 参数校验
            await ApiCheck(chatRunDto, node);
            
            if (node.Config.Async)
            {
                MysoftApiExec(uri, cancellationToken);
                return;
            }
            await MysoftApiExec(uri, cancellationToken);
        }

        private async Task MysoftApiExec(string uri, CancellationToken cancellationToken)
        {
            var jsonBody = await GenerateContentText();

            var context = _mysoftContextFactory.GetMysoftContext();
            _logger.LogInformation("准备请求了：路径：{0},参数:{1}", context.GptBuilderUrl + uri, JsonConvert.SerializeObject(jsonBody));
            var result = await _mysoftApiService.PostAsync(context.GptBuilderUrl + uri, jsonBody, cancellationToken);
            _logger.LogInformation("拿到返回结果了：{0}", JsonConvert.SerializeObject(result));
            var data = JsonConvert.DeserializeObject<MysoftApiResultDto>(result);

            if (data?.Success == false)
            {
                throw new BusinessException(data?.Error?.Exception?.Message);
            }

            if (data?.Data == null) return;

            await GenerateOutputValue(data.Data);
        }

        [KernelFunction]
        [Description("调用明源云集成平台API")]
        public async Task MysoftIPassApi([Description("API地址")] string uri, CancellationToken cancellationToken)
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return;
            Verify.NotNull(uri);
            var node = await chatRunDto.GetFlowNode();
            // 参数校验
            await ApiCheck(chatRunDto,node);
            
            if (node.Config.Async)
            {
                MysoftIPassApiExec(uri, cancellationToken);
                return;
            }
            await MysoftIPassApiExec(uri, cancellationToken);
        }
        private async Task MysoftIPassApiExec(string uri, CancellationToken cancellationToken)
        {
            var jsonBody = await GenerateContentText();

            var context = _mysoftContextFactory.GetMysoftContext();
            var mipInfo = _mysoftConfigurationDomain.GetMipInfo();
            _logger.LogInformation("准备请求了：路径：{0},参数:{1}", mipInfo.ServiceUrl + uri, JsonConvert.SerializeObject(jsonBody));
            var result = await _mysoftIPassApiService.PostAsync(mipInfo.ServiceUrl + uri, jsonBody, cancellationToken);

            _logger.LogInformation("拿到返回结果了：{0}", JsonConvert.SerializeObject(result));
            var data = JsonConvert.DeserializeObject(result);

            if (data == null) return;

            await GenerateOutputValue(data);
        }

        private async Task ApiCheck(ChatRunDto chatRunDto, FlowNode node)
        {
            var config = node.Config;
            if (string.IsNullOrEmpty(config.PluginId))
            {
                throw new NoRequiredArgumentException($"节点【{node.Name}】插件没有设置");
            }
            if (string.IsNullOrEmpty(config.ToolId))
            {
                throw new NoRequiredArgumentException($"节点【{node.Name}】插件服务没有设置");
            }
            await NodeArgumentCheck(chatRunDto);
        }

        [KernelFunction]
        [Description("调用明源云客服API")]
        public async Task MysoftCustomerServiceApi([Description("问题")] string input, CancellationToken cancellationToken)
        {
            Verify.NotNull(input);

            var paramDto = new MysoftCustomerServiceApiParamDto
            {
                Input = input,
                Context = new ContextDto
                {
                    AppCode = await GetKeywordValue<string>(nameof(ContextDto.AppCode)),
                    AppName = await GetKeywordValue<string>(nameof(ContextDto.AppName)),
                    AppVersion = await GetKeywordValue<string>(nameof(ContextDto.AppVersion)),
                    Mobile = await GetKeywordValue<string>(nameof(ContextDto.Mobile)),
                    UserName = await GetKeywordValue<string>(nameof(ContextDto.UserName)),
                    CustomerId = await GetKeywordValue<string>(nameof(ContextDto.CustomerId)),
                    CustomerName = await GetKeywordValue<string>(nameof(ContextDto.CustomerName))
                }
            };
            paramDto.Context.AppVersion = string.IsNullOrWhiteSpace(paramDto.Context.AppVersion) ? "V2.0" : paramDto.Context.AppVersion;

            //TODO 测试环境数据
            paramDto.Context.CustomerId = "7d850cf2-58e7-409a-bdb6-c567811928c4";
            paramDto.Context.CustomerName = "上海海亮";

            var uri = "/v20/app/agent/messageinfo/tianjiGPT.svc";
            var jsonBody = JsonConvert.SerializeObject(paramDto);
            _logger.LogInformation("调用明源云客服API-准备请求了：路径：{0},参数:{1}", uri, jsonBody);
            var result = await _mysoftCustomerServiceApiService.PostAsync(uri, jsonBody, cancellationToken);
            _logger.LogInformation("调用明源云客服API-结果：路径：{0}", result);

        }

        private async Task<string> GenerateContentText()
        {
            var chatRunDto = await GetChatRunDto();
            var inputs = await chatRunDto.GetFlowNodeInputs();
            var result = new ExpandoObject();
            foreach (var input in inputs)
            {
                result.TryAdd(input.Code, input.LiteralValue);
            }
            return await Task.FromResult(JsonConvert.SerializeObject(result));
        }

        private async Task GenerateOutputValue(object data)
        {
            var chatRunDto = await GetChatRunDto();
            var outputs = await chatRunDto.GetFlowNodeOutputs();
            if (outputs == null || outputs.Count == 0) return;
            if (outputs.Count == 1)
            {
                outputs.First().LiteralValue = data?.ToString() ?? string.Empty;
                return;
            }

            var jsonValue = JToken.Parse(JsonConvert.SerializeObject(data));
            if (jsonValue is JObject == false || jsonValue.Children().Count() == 0) return;

            var values = jsonValue.Children().Where(x => x is JProperty).ToDictionary(x => ((JProperty)x).Name, x => ((JProperty)x).Value);
            foreach (var output in outputs)
            {
                output.LiteralValue = values.ContainsKey(output.Code) ? values[output.Code].ToString() : string.Empty;
            }
        }
    }
    public class MysoftApiResultDto
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public bool Success { get; set; }
        public MysoftApiErrorDto Error { get; set; }
        public object Data { get; set; }
    }
    public class MysoftApiErrorDto
    {
        public string Type { get; set; }
        public MysoftApiErrorExceptionDto Exception { get; set; }
    }
    public class MysoftApiErrorExceptionDto
    {
        public string Message { get; set; }
    }

    public class MysoftCustomerServiceApiParamDto
    {
        public string Input { get; set; } = string.Empty;
        public ContextDto Context { get; set; }
    }

    public class ContextDto
    {
        public string UserName { get; set; }
        public string Mobile { get; set; }
        public string AppCode { get; set; }
        public string AppName { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string AppVersion { get; set; }
    }
}