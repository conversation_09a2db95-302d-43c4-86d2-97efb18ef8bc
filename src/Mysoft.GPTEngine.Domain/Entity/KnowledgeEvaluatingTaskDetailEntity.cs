using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeEvaluatingTaskDetail")]
    public class KnowledgeEvaluatingTaskDetailEntity:BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeTaskDetailGUID { get; set; }

        public String Answer { get; set; }

        public int EvaluatingResultEnum { get; set; }

        public int Index { get; set; }

        public int IsConfirm { get; set; }

        public Guid KnowledgeEvaluatingTaskGUID { get; set; }

        public Double MinScore { get; set; }

        public String Question { get; set; }

        public String Remark { get; set; }

        public int StatusEnum { get; set; }

        public int TopK { get; set; }
    }
}
