using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_PluginMetadata")]
    public class PluginMetadataEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PluginMetadataGUID { get; set; }

        public string PluginGUID { get; set; }

        public string Metadata { get; set; }
    }
}
