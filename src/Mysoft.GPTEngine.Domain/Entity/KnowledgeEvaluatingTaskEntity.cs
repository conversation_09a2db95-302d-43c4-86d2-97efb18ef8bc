using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeEvaluatingTask")]
    public class KnowledgeEvaluatingTaskEntity:BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeEvaluatingTaskGUID { get; set; }

        public int EvaluatingStatusEnum { get; set; }

        public Guid KnowledgeEvaluatingDataGUID { get; set;}

        public Guid KnowledgeGUID { get; set; }

        public string KnowledgeName { get; set; }

        public double MinScore { get; set; }

        public string Name { get; set; }

        public int TopK { get; set; }
    }
}
