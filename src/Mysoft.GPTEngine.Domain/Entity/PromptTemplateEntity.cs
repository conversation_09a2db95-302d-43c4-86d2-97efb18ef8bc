using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_PromptTemplate")]
    public class PromptTemplateEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid PromptTemplateGUID { get; set; }
        public Guid PromptGUID { get; set; }
        public String PromptTemplate { get; set; }
        
        public String MessageContent { get; set; }

    }
}
