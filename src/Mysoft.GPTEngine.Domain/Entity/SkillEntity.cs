using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_skill")]
    public class SkillEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid SkillGUID { get; set; }
        public String SkillCode { get; set; }
        public String SkillName { get; set; }
        public String ModelInstanceCode { get; set; }
        public int SkillChatCount { get; set; }
        public Guid SpaceGUID { get; set; }
    }
}
