using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_Chat")]
    public class ChatEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatGUID { get; set; }
        public Guid ApplicationGUID { get; set; }
        public Guid AssistantGUID { get; set; }
        public Guid SkillGUID { get; set; }
        public Guid CurrentNodeGUID { get; set; } = Guid.Empty;
        public Guid CurrentOrchestrationGUID { get; set; } = Guid.Empty;
        [SugarColumn(ColumnName = "Title")]
        public string _title { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string Title {
            get
            {
                return _title;
            }
            set
            {
                this._title = value == null ? "" : value.Substring(0, Math.Min(value.Length, 800));
            }
        }
        public int IsDeleted { get; set; }
        public string CustomerId { get; set; } = string.Empty;
        public String CustomerName { get; set; } = string.Empty;
        public string UserGUID { get; set; } = string.Empty;
        public String UserName { get; set; } = string.Empty;
        
        public String TenantCode { get; set; } = string.Empty;
        
        public String TenantName { get; set; } = string.Empty;
        
        /**
         * SDK调用技能会话执行状态，枚举值：0=等待执行，1=运行中，2=完成，3=失败
         */
        public int State { get; set; }
        
        /**
         * SDK调用技能会话执行失败错误信息
         */
        public String ErrorMessage { get; set; }
    }
}
