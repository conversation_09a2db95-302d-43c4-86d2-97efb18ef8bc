using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion
{
    [SugarTable("gpt_KnowledgeQuestionRelation")]
    public class QuestionRelationEntity: BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeQuestionRelationGUID { get; set; }

        public Guid KnowledgeGUID { get; set; }

        public Guid KnowledgeQuestionGUID { get; set; }

        public Guid KnowledgeSectionGUID { get; set; }

        public int Disable { get; set; }
    }
}
