using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion
{
    [SugarTable("gpt_KnowledgeQuestion")]
    public class QuestionEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeQuestionGUID { get; set; }

        public Guid KnowledgeGUID { get; set; }

        public string Question { get; set; }

        public int Disable { get; set; }
    }
}
