using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ChatDetail")]
    public class ChatDetailEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatDetailGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public Int32 RoleType { get; set; }
        public String Completion { get; set; }
    }
}
