using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ChatMessageKnowledgeNodeLog")]
    public class ChatMessageKnowledgeNodeLogEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatMessageKnowledgeNodeLogGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public Guid BatchGUID { get; set; }
        public Guid ApplicationGUID { get; set; }
        public Guid AssistantGUID { get; set; }
        public Guid SkillGUID { get; set; }
        public Guid NodeGUID { get; set; }
        public string KnowledgeCode { get; set; }
        public Guid KnowledgeFileSectionGUID { get; set; }
        public double Score { get; set; } = 0;
        public string Inputs { get; set; }
        public string Outputs { get; set; }
        public string CustomerId { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string UserGUID { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string TenantCode { get; set; } = string.Empty;
        public string TenantName { get; set; } = string.Empty;
    }
}
