using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeEvaluatingTaskDetailKeyWord")]
    public class KnowledgeEvaluatingKeyWordEntity:BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeTaskKeyWordGUID { get; set; }

        public string KeyWord { get; set; }

        public Guid KnowledgeTaskDetailGUID { get; set;}

        public string CompareResult { get; set;}

        public int Index { get; set; }

    }
}
