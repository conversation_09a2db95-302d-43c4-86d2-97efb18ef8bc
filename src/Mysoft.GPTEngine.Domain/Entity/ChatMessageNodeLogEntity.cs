using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;
using System;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ChatMessageNodeLog")]
    public class ChatMessageNodeLogEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatMessageNodeLogGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public Guid NodeGUID { get; set; }
        public Guid BatchGUID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int Index { get; set; }
        public double Duration { get; set; }
        public double FirstRespDuration { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Status { get; set; } = "success";
        public string Message { get; set; }
        public int PromptTokens { get; set; } = 0;
        public int CompleteTokens { get; set; } = 0;
        public int Tokens { get; set; }
        [SugarColumn(ColumnName = "Config")]
        public string _config { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string Config
        {
            get
            {
                return _config.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._config = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }
        [SugarColumn(ColumnName = "Inputs")]
        public string _inputs { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string Inputs
        {
            get
            {
                return _inputs.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._inputs = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }
        [SugarColumn(ColumnName = "Outputs")]
        public string _outputs { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string Outputs
        {
            get
            {
                return _outputs.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._outputs = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }
        public String CustomerId { get; set; } = string.Empty;
        public String CustomerName { get; set; } = string.Empty;
        public String UserGUID { get; set; } = string.Empty;
        public String UserName { get; set; } = string.Empty;
        public String ThinkOutputs { get; set; } = string.Empty;
    }
}
