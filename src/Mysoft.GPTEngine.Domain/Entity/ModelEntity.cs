using Mysoft.GPTEngine.Domain.Shared.Entity;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using SqlSugar;
using System;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_model")]
    public class ModelEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ModelGUID { get; set; }
        public String ModelName { get; set; }
        public String ModelCode { get; set; }
        public ModelTypeEnum ModelType { get; set; }
        public Int32 IsSystem { get; set; }
        public String ExecutionSetting { get; set; }
        public ServiceTypeEnum ServiceTypeEnum { get; set; }
    }
}
