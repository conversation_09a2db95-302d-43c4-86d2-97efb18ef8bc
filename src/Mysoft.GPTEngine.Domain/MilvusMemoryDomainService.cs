using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.Memory;
using Milvus.Client;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Repositories;
using Microsoft.SemanticKernel.Embeddings;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using System.Linq;
using Serilog.Extensions.Logging;

namespace Mysoft.GPTEngine.Domain
{
    public class MilvusMemoryDomainService : DomainServiceBase, IMilvusMemoryDomainService
    {
        private readonly KnowledgeFileRepository _knowledgeFileRepository;
        private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly ILogger<MilvusMemoryDomainService> _logger;

        public MilvusMemoryDomainService(ILogger<MilvusMemoryDomainService> logger,IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper) : 
            base(mysoftContextFactory, httpContextAccessor, mapper)
        {
            _logger = logger;
            _mysoftContext = mysoftContextFactory.GetMysoftContext();
        }

        private const string DefaultIndexName = "default";
        private const string IsReferenceFieldName = "is_reference";
        private const string ExternalSourceNameFieldName = "external_source_name";
        private const string IdFieldName = "id";
        private const string DescriptionFieldName = "description";
        private const string TextFieldName = "text";
        private const string AdditionalMetadataFieldName = "additional_metadata";
        private const string EmbeddingFieldName = "embedding";
        private const string KeyFieldName = "key";
        private const string TimestampFieldName = "timestamp";

        private const int DefaultMilvusPort = 19530;
        private const int DefaultVarcharLength = 65_535;

        private readonly QueryParameters _queryParametersWithEmbedding;
        private readonly QueryParameters _queryParametersWithoutEmbedding;
        private ITextEmbeddingGenerationService _embeddingGenerationService;
        private Milvus.Client.MilvusClient milvusClient;
        private readonly MysoftContext _mysoftContext;


        private readonly SearchParameters _searchParameters = new SearchParameters()
        {
            OutputFields = { IdFieldName, AdditionalMetadataFieldName }
        };

        /// <inheritdoc/>
        public async IAsyncEnumerable<MemoryQueryResult> SearchAsync(
            string collection,
            ReadOnlyMemory<float> queryEmbedding,
            int limit = 1,
            double minRelevanceScore = 0.0,
            bool withEmbeddings = false,
            int sourceTypeEnum = 0,
            [EnumeratorCancellation] CancellationToken cancellationToken = default)
        {
            this.milvusClient = _mysoftContext.MemoryStore.CreateMilvusClient(_mysoftContext.TenantCode);
            IAsyncEnumerable<(MemoryRecord, double)> results = this.GetNearestMatchesAsync(
                collectionName: collection,
                embedding: queryEmbedding,
                limit: limit,
                minRelevanceScore: minRelevanceScore,
                withEmbeddings: withEmbeddings,
                sourceTypeEnum,
                cancellationToken: cancellationToken);
            
            await foreach ((MemoryRecord, double) result in results.ConfigureAwait(false))
            {
                yield return MemoryQueryResult.FromMemoryRecord(result.Item1, result.Item2);
            }
        }


        /// <inheritdoc />
        private async Task<(MemoryRecord, double)?> GetNearestMatchAsync(
            string collectionName,
            ReadOnlyMemory<float> embedding,
            double minRelevanceScore = 0,
            bool withEmbedding = false,
            int sourceTypeEnum = 0,
            CancellationToken cancellationToken = default)
        {
            await foreach ((MemoryRecord, double) result in this.GetNearestMatchesAsync(collectionName, embedding, limit: 1, minRelevanceScore, withEmbedding, sourceTypeEnum, cancellationToken).ConfigureAwait(false))
            {
                return result;
            }

            return null;
        }


        /// <inheritdoc />
        private async IAsyncEnumerable<(MemoryRecord, double)> GetNearestMatchesAsync(
            string collectionName,
            ReadOnlyMemory<float> embedding,
            int limit,
            double minRelevanceScore = 0,
            bool withEmbeddings = false,
            int sourceTypeEnum = 0,
            [EnumeratorCancellation] CancellationToken cancellationToken = default)
        {
            MilvusCollection collection = null;
            try
            {
                collection = this.milvusClient.GetCollection(collectionName);
            }
            catch (Milvus.Client.MilvusException ex) when (ex.Message.Contains("collection not found"))
            {
                _logger.LogWarning($"Milvus collection not found: {collectionName}");
                yield break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取Milvus collection异常: {collectionName}");
                yield break;
            }
            //处理数据来源字段
            await InitJsonParams(sourceTypeEnum);
            SearchResults results = null;
            try
            {
                results = await collection
                    .SearchAsync(EmbeddingFieldName, new List<ReadOnlyMemory<float>> { embedding }, SimilarityMetricType.Ip, limit, this._searchParameters, cancellationToken)
                    .ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询向量库异常");
            }
            if (results == null)
            {
                _logger.LogInformation("没有查询到数据");
                yield break;
            }
            IReadOnlyList<string> ids = results.Ids.StringIds!;
            if (ids == null)
            {
                _logger.LogInformation("没有查询到数据");
                yield break;
            }
            int rowCount = ids.Count;
            IReadOnlyList<FieldData> data = results.FieldsData;

            // Since Milvus does not support fetching vectors via the Search API, we do an extra call to fetch the ids and embeddings using the Query API,
            // using the IDs returned from the Search above, populating a map from the IDs to the embedding.
            // TODO: There's some support for fetching vectors from Search in Milvus 2.3, check that out.
            Dictionary<string, ReadOnlyMemory<float>>? embeddingMap = null;
            if (withEmbeddings)
            {
                StringBuilder filter = new StringBuilder();
                filter.Append(IdFieldName).Append(" in [");

                for (int rowNum = 0; rowNum < ids.Count; rowNum++)
                {
                    if (rowNum > 0)
                    {
                        filter.Append(',');
                    }

                    filter.Append('"').Append(ids[rowNum]).Append('"');
                }

                filter.Append(']');

                IReadOnlyList<FieldData> fieldData = await collection.QueryAsync(
                        filter.ToString(),
                        new QueryParameters() { OutputFields = { EmbeddingFieldName } },
                        cancellationToken: cancellationToken)
                    .ConfigureAwait(false);

                IReadOnlyList<string> idData = (fieldData[0] as FieldData<string> ?? fieldData[1] as FieldData<string>)!.Data;
                IReadOnlyList<ReadOnlyMemory<float>> embeddingData = (fieldData[0] as FloatVectorFieldData ?? fieldData[1] as FloatVectorFieldData)!.Data;

                embeddingMap = new Dictionary<string, ReadOnlyMemory<float>>(ids.Count);
                for (int rowNum = 0; rowNum < ids.Count; rowNum++)
                {
                    embeddingMap[idData[rowNum]] = embeddingData[rowNum];
                }
            }

            for (int rowNum = 0; rowNum < rowCount; rowNum++)
            {
                // TODO: Milvus 2.3 has range search, which will move this to the server.
                if (results.Scores[rowNum] >= minRelevanceScore)
                {
                    yield return (
                        this.ReadMemoryRecord(data, rowNum, withEmbeddings ? embeddingMap![ids[rowNum]] : null),
                        results.Scores[rowNum]);
                }
            }
        }

        /// <summary>
        /// 初始化JSON参数
        /// </summary>
        /// <param name="collection"></param>
        /// <param name="sourceTypeEnum"></param>
        /// <returns></returns>
        private async Task InitJsonParams(int sourceTypeEnum)
        {
            //如果存在额外的json字段并且来源枚举值不是0
            if (sourceTypeEnum != (int)SourceTypeEnum.Default)
            {
                this._searchParameters.Expression = $"{AdditionalMetadataFieldName}[\"disable\"] != '1' && {AdditionalMetadataFieldName}[\"sourceTypeEnum\"] == '{sourceTypeEnum}'";
            }
            await Task.CompletedTask;
        }

        private MemoryRecord ReadMemoryRecord(IReadOnlyList<FieldData> data, int rowNum, ReadOnlyMemory<float>? externalEmbedding = null)
        {
            bool isReference = false;
            string externalSourceName = string.Empty;
            string id = string.Empty;
            string description = string.Empty;
            string text = string.Empty;
            string additionalMetadata = string.Empty;
            ReadOnlyMemory<float>? embedding = null;
            string key = string.Empty;
            DateTimeOffset? timestamp = null;

            foreach (FieldData field in data)
            {
                switch (field.FieldName)
                {
                    case IsReferenceFieldName when field is FieldData<bool> isReferenceField:
                        isReference = isReferenceField.Data[rowNum];
                        break;

                    case ExternalSourceNameFieldName when field is FieldData<string> externalSourceNameField:
                        externalSourceName = externalSourceNameField.Data[rowNum];
                        break;

                    case IdFieldName when field is FieldData<string> idField:
                        id = idField.Data[rowNum];
                        break;

                    case DescriptionFieldName when field is FieldData<string> descriptionField:
                        description = descriptionField.Data[rowNum];
                        break;

                    case TextFieldName when field is FieldData<string> textField:
                        text = textField.Data[rowNum];
                        break;

                    case AdditionalMetadataFieldName when field is FieldData<string> additionalMetadataField:
                        additionalMetadata = additionalMetadataField.Data[rowNum];
                        break;

                    case EmbeddingFieldName when field is FloatVectorFieldData embeddingField:
                        Debug.Assert(externalEmbedding is null);
                        embedding = embeddingField.Data[rowNum];
                        break;

                    case KeyFieldName when field is FieldData<string> keyField:
                        key = keyField.Data[rowNum];
                        break;

                    case TimestampFieldName when field is FieldData<string> timestampField:
                        string timestampString = timestampField.Data[rowNum];
                        if (timestampString.Length > 0)
                            timestamp = DateTimeOffset.Parse(timestampString, CultureInfo.InvariantCulture);
                        break;

                    default:
                        continue; // Unknown field - ignore
                }
            }

            return new MemoryRecord(
                new MemoryRecordMetadata(isReference, id, text, description, externalSourceName, additionalMetadata),
                embedding ?? externalEmbedding ?? Array.Empty<float>(),
                key,
                timestamp);
        }
    }
}
