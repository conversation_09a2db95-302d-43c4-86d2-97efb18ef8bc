using System.Collections.Generic;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode
{
    /// <summary>
    /// txt文档处理器
    /// </summary>
    public class TextDecoder : IDocumentDecoder
    {
        public TextDecoder(Kernel _kernel, MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper)
        {
        }

        public async Task<FileContent> DecodeAsync(MemoryStream data, ExecutionSetting executionSetting)
        {
            var result = new FileContent("text/plain");
            // 根据 BOM 判断编码
            Encoding encoding = EncodingHelper.DetectEncoding(data);
            using var reader = new StreamReader(data, encoding);
            var content = await reader.ReadToEndAsync().ConfigureAwait(false);

            result.Sections.Add(new FileSection(1, content.Trim(), true));
            return result;
        }

        public bool CanDecode(string documentType)
        {
            return documentType == "Excel";
        }

        public async Task<DocumentReadingDto> DomentReadAsync(MemoryStream data, NodeConfig nodeConfig)
        {
            DocumentReadingDto documentReadingDto = new DocumentReadingDto();
            Encoding encoding = EncodingHelper.DetectEncoding(data);
            using var reader = new StreamReader(data, encoding);
            var content = await reader.ReadToEndAsync().ConfigureAwait(false);
            documentReadingDto.Content = content;
            return documentReadingDto;
        }

        public async Task<string> GetDocumentTxt(MemoryStream data, NodeConfig nodeConfig)
        {
            DocumentReadingDto documentReadingDto = new DocumentReadingDto();
            Encoding encoding = EncodingHelper.DetectEncoding(data);
            using var reader = new StreamReader(data, encoding);
            var content = await reader.ReadToEndAsync().ConfigureAwait(false);
            return content;
        }

        public Task<List<ImageUploadCallBackDto>> ConvertDocumentToImg(MemoryStream ms)
        {
            throw new NotImplementedException();
        }
    }
}
