using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.Domain.TextExtractDecode.Util;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode
{
    /// <summary>
    /// txt文档处理器
    /// </summary>
    public class MarkdownDecoder : BaseDecode, IDocumentDecoder
    {
        private readonly MysoftApiService _mysoftApiDomainService;
        
        private readonly IMysoftContextFactory _mysoftContextFactory;
        public MarkdownDecoder(<PERSON><PERSON> _kernel, MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper)
            : base(_kernel,mysoftApiDomainService, mysoftContextFactory, httpContextAccessor, mapper)
        {
            _mysoftApiDomainService = mysoftApiDomainService;
            _mysoftContextFactory = mysoftContextFactory;
        }

        public async Task<FileContent> DecodeAsync(MemoryStream data, ExecutionSetting executionSetting)
        {
            var result = new FileContent("text/markdown");
            using var reader = new StreamReader(data);
            var markdownContent = await reader.ReadToEndAsync().ConfigureAwait(false);

            if (executionSetting.SectionConfig.Mode == (int)SectionModeEnum.Custom)
            {
                result.Sections.Add(new FileSection(0, markdownContent, true));
                return result;
            }
            

            var list = await MarkdownReader.Builder(_mysoftApiDomainService, _mysoftContextFactory).ConvertToParagraph(markdownContent, executionSetting.SectionConfig.LevelConfig).ConfigureAwait(false);
            
            var i = 0;
            foreach (var markdownParagraph in list)
            {
                if (!String.IsNullOrWhiteSpace(markdownParagraph.Content))
                {
                    i += 1;
                    result.Sections.Add(new FileSection(i, markdownParagraph.Content, true, markdownParagraph.Title));
                }
            }
            
            return result;
            
        }

        public Task<DocumentReadingDto> DomentReadAsync(MemoryStream file, NodeConfig nodeConfig)
        {
            throw new NotImplementedException();
        }

        public Task<string> GetDocumentTxt(MemoryStream data, NodeConfig nodeConfig)
        {
            throw new NotImplementedException();
        }

        public bool CanDecode(string documentType)
        {
            return documentType == "markdown";
        }

        public Task<List<ImageUploadCallBackDto>> ConvertDocumentToImg(MemoryStream ms)
        {
            throw new NotImplementedException();
        }

    }
    
    
}
