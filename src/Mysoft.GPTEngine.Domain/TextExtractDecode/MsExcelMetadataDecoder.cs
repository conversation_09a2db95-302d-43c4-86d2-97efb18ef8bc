using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aspose.Cells;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode
{
    public class MsExcelMetadataDecoder : IDocumentDecoder
    {
        public MsExcelMetadataDecoder(Kernel _kernel, MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper)
        {
        }
        public Task<FileContent> DecodeAsync(MemoryStream data, ExecutionSetting executionSetting)
        {
            AsposeHelper.InitAspose();
            var result = new FileContent("text/excel");
            using (data)
            {
                Workbook workbook = new Workbook(data);
                
                Worksheet worksheet = workbook.Worksheets[0];
                var maxRow = worksheet.Cells.MaxDataRow;
                var maxCol = worksheet.Cells.MaxDataColumn;
                if (maxRow < 1 || maxCol < 2)
                {
                    return Task.FromResult(result);
                }

                var header = ReaderHeader(worksheet.Cells.GetRow(1), maxCol);
                result.Sections = ReaderContent(worksheet, header);
            }

            return Task.FromResult(result);
        }

        private List<FileSection> ReaderContent(Worksheet worksheet, Dictionary<int, string> header)
        {
            var maxRow = worksheet.Cells.MaxDataRow;
            var maxCol = worksheet.Cells.MaxDataColumn;
            var fileSections = new List<FileSection>();
            var number = 1;
            for (var i = 2; i <= maxRow; i++)
            {
                var row = worksheet.Cells.GetRow(i);
                if (row.IsBlank) continue;
                
                

                FileSection fileSection = new FileSection();
                fileSection.Content = ReaderContent(row);
                if(String.IsNullOrWhiteSpace(fileSection.Content)) continue;
                
                fileSection.ThirdId = ReaderThirdId(row);
                if(String.IsNullOrWhiteSpace(fileSection.ThirdId)) continue;
                
                fileSection.Title = ReaderTitle(row);
                fileSection.Number = number;
                fileSection.Metadata = ReaderMetadata(row, header, maxCol);
                fileSections.Add(fileSection);
                number += 1;
            }

            return fileSections;
        }
        
        private string ReaderThirdId(Row row)
        {
            var cell = row[1];
            if (cell.Value == null 
                || String.IsNullOrWhiteSpace(cell.Value.ToString()?.Trim())
                || cell.Value.ToString()?.Trim().Length > 128) return "";
            return cell.Value.ToString()?.Trim();
        }
        
        private string ReaderContent(Row row)
        {
            var cell = row[3];
            if (cell.Value == null 
                || String.IsNullOrWhiteSpace(cell.Value.ToString()?.Trim())) return "";
            return cell.Value.ToString()?.Trim();
        }

        private string ReaderTitle(Row row)
        {
            var cell = row[2];
            if (cell.Value == null 
                || String.IsNullOrWhiteSpace(cell.Value.ToString())
                || cell.Value.ToString()?.Trim().Length > 512) return "";

            return cell.Value.ToString()?.Trim();
        }

        private string ReaderMetadata(Row row, Dictionary<int, string> header, int maxCol)
        {
            var metadata = new Dictionary<string, string>();
            for (var j = 4; j <= maxCol; j++)
            {
                var cell = row[j];
                var value = cell.Value;
                if (!header.ContainsKey(j)) continue;
                    
                var key = header[j];
                if (cell.Value == null || String.IsNullOrEmpty(cell.Value.ToString()))
                {
                    metadata[key] = "";
                }
                else
                {
                    metadata[key] = cell.Value.ToString()?.Trim();
                }
            }
            return JsonConvert.SerializeObject(metadata);
        }

        private Dictionary<int, string> ReaderHeader(Row row, int maxCol)
        {
            var result = new Dictionary<int, string>();
            
            var cell = row[1];
            if (cell.Value == null 
                || String.IsNullOrWhiteSpace(cell.Value.ToString()) 
                || !string.Equals(cell.Value.ToString(), "thirdId", StringComparison.OrdinalIgnoreCase))
                throw new BusinessException("第一列表头不存在或者列明不正确");
            
            cell = row[2];
            if (cell.Value == null 
                || String.IsNullOrWhiteSpace(cell.Value.ToString()) 
                || !string.Equals(cell.Value.ToString(), "title", StringComparison.OrdinalIgnoreCase))
                throw new BusinessException("第二列表头不存在或者列明不正确");
            
            cell = row[3];
            if (cell.Value == null 
                || String.IsNullOrWhiteSpace(cell.Value.ToString()) 
                || !string.Equals(cell.Value.ToString(), "content", StringComparison.OrdinalIgnoreCase))
                throw new BusinessException("第三列表头不存在或者列明不正确");
            
            for (var i = 4; i <= maxCol; i++)
            {
                cell = row[i];
                if (cell.Value == null || String.IsNullOrWhiteSpace(cell.Value.ToString()))
                {
                    throw  new BusinessException("表头第" + (i+1) + "列为空");
                }
                result.Add(i, cell.Value.ToString());
            }

            return result;
        }

        public bool CanDecode(string documentType)
        {
            return documentType == "excel";
        }

        public async Task<DocumentReadingDto> DomentReadAsync(MemoryStream file, NodeConfig nodeConfig)
        {
            AsposeHelper.InitAspose();
            DocumentReadingDto documentReadingDto = new DocumentReadingDto();
            documentReadingDto.Content = await GetDocumentTxt(file, nodeConfig);
            return documentReadingDto;
        }

        public async Task<string> GetDocumentTxt(MemoryStream file, NodeConfig nodeConfig)
        {
            AsposeHelper.InitAspose();
            string res = string.Empty;
            using (file)
            {
                Workbook workbook = new Workbook(file);
                // 拿到过滤了隐藏sheet页的数据，隐藏系统自带的sheet页或不需要展示的数据
                List<int> workSheetIndex = new List<int>();
                foreach (Worksheet worksheet in workbook.Worksheets)
                {
                    // 隐藏不展示的sheet页 或者系统自带的
                    if (worksheet.VisibilityType == VisibilityType.Hidden || worksheet.VisibilityType == VisibilityType.VeryHidden)
                    {
                        continue;
                    }
                    workSheetIndex.Add(worksheet.Index);
                }
                // 需要过滤sheet页的场景
                if (nodeConfig.UsePageRange != "all" && nodeConfig.UseFirstFewPages + nodeConfig.UseLastFewPages < workSheetIndex.Count)
                {
                    // 计算要移除的部分的起始位置和长度
                    int removeStart = nodeConfig.UseFirstFewPages;
                    int removeCount = workSheetIndex.Count - (nodeConfig.UseFirstFewPages + nodeConfig.UseLastFewPages);

                    // 移除中间不需要的部分
                    workSheetIndex.RemoveRange(removeStart, removeCount);
                }
                foreach (Worksheet worksheet in workbook.Worksheets)
                {
                    // 不包含在sheet页里 跳过
                    if(!workSheetIndex.Contains(worksheet.Index))
                    {
                        continue;
                    }

                    // 设置当前活动工作表
                    workbook.Worksheets.ActiveSheetIndex = worksheet.Index;

                    string md = string.Empty;
                    using (MemoryStream ms = new MemoryStream())
                    {
                        workbook.Save(ms, SaveFormat.Markdown);
                        md = Encoding.UTF8.GetString(ms.ToArray());
                        md = ImageExtractionHelper.RemoveBase64Img(md);
                    }
                    // sheet 名称
                    res += $"# {worksheet.Name}\n";
                    // markdown 内容
                    res += md;
                }
            }
            return await Task.FromResult(res);
        }

        public Task<List<ImageUploadCallBackDto>> ConvertDocumentToImg(MemoryStream ms)
        {
            throw new NotImplementedException();
        }
    }
}