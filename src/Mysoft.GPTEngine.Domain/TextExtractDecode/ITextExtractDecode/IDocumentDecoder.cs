using Mysoft.GPTEngine.Domain.DTO;
using System.IO;
using System.Threading.Tasks;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode
{
    public interface IDocumentDecoder
    {
        Task<FileContent> DecodeAsync(MemoryStream file, ExecutionSetting executionSetting);
        bool CanDecode(string documentType);
        Task<DocumentReadingDto> DomentReadAsync(MemoryStream file, NodeConfig nodeConfig);
        Task<string> GetDocumentTxt(MemoryStream file, NodeConfig nodeConfig);
        Task<List<ImageUploadCallBackDto>> ConvertDocumentToImg(MemoryStream ms);
    }
}
