using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Fields;
using Aspose.Words.Layout;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Application.Configuration;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Aspose.Pdf.Text;
using Aspose.Pdf;
using Aspose.Pdf.Devices;
using ShapeType = Aspose.Words.Drawing.ShapeType;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode
{
    public class BaseDecode : DomainServiceBase
    {
        private readonly MysoftApiService _mysoftApiDomainService;
        public readonly Kernel _kernel;
        public List<MemoryStream> imageStreams = new List<MemoryStream>();

        #region OCR重试相关常量
        private const int MaxRetryCount = 10; // 最大重试次数
        private const int InitialDelaySeconds = 1; // 初始延迟秒数
        private const int MinJitterMs = 500; // 最小抖动毫秒数
        private const int MaxJitterMs = 2000; // 最大抖动毫秒数
        private const string ResourceWaitingMessage = "当前请求较多，小助手正在为您安排资源，预计 1 分钟内完成计算，感谢您的耐心等待~ \n"; // 资源等待消息
        #endregion

        public BaseDecode(Kernel kernel, MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper) : base(mysoftContextFactory, httpContextAccessor, mapper)
        {
            _mysoftApiDomainService = mysoftApiDomainService;
            _kernel = kernel;
        }

        public void TocExcute(Aspose.Words.Document doc, NodeConfig nodeConfig)
        {
            if (nodeConfig.UsePageRange == "all")
            {
                return;
            }

            // 创建 LayoutCollector 实例
            LayoutCollector layoutCollector = new LayoutCollector(doc);

            // 更新布局信息
            doc.UpdatePageLayout();

            // 获取文档总页数
            int totalPages = layoutCollector.GetEndPageIndex(doc);

            if (nodeConfig.UseLastFewPages + nodeConfig.UseFirstFewPages >= doc.PageCount)
            {
                return;
            }

            // 计算需要保留的最后一页的索引（从0开始计数）
            int lastKeptPageIndex = totalPages - (nodeConfig.UseLastFewPages - 1);

            // 遍历所有节点
            NodeCollection nodes = doc.GetChildNodes(NodeType.Any, true);

            // 用于存储待删除节点的列表
            List<Node> nodesToRemove = new List<Node>();
            for (int i = 0; i < nodes.Count; i++)
            {
                Node node = nodes[i];

                // 获取当前节点的起始页索引（从0开始计数）
                int startPageIndex = layoutCollector.GetStartPageIndex(node);
                int endPageIndex = layoutCollector.GetEndPageIndex(node);

                // 如果当前节点不在保留范围内，则标记该节点为待删除
                if (startPageIndex <= nodeConfig.UseFirstFewPages || endPageIndex >= lastKeptPageIndex)
                {
                    continue;
                }
                else
                {
                    nodesToRemove.Add(node);
                }
            }

            // 反向遍历待删除节点列表，并删除它们
            for (int i = nodesToRemove.Count - 1; i >= 0; i--)
            {
                nodesToRemove[i].Remove();
            }
        }
        protected static void LoadPdfFonts()
        {
            FontRepository.Sources.Add(new Aspose.Pdf.Text.FolderFontSource(GetFontDir()));
            FontRepository.LoadFonts();
        }

        public static string GetFontDir()
        {
#if !NETFRAMEWORK
            var baseDir = AppContext.BaseDirectory;
#else
            var baseDir = System.Web.HttpRuntime.AppDomainAppPath;
#endif
            return Path.Combine(baseDir, "Fonts");
        }

        public Aspose.Pdf.Document TocExcutePdf(Aspose.Pdf.Document doc, Aspose.Pdf.Document newPdfDocument, NodeConfig nodeConfig)
        {
            LoadPdfFonts();
            // 获取文档的总页数
            int totalPages = doc.Pages.Count;

            //如果开启了全文图片转换，并且整个PDF的页数小于等于10
            if (nodeConfig.ImagConverter && totalPages <= 10)
            {
                //转图片PDF
                doc = ConvertAndCreatePdfInMemory(doc);
            }

            if (nodeConfig.UsePageRange == "all" || nodeConfig.UseFirstFewPages + nodeConfig.UseLastFewPages >= totalPages)
            {
                return doc;
            }

            // 复制前几页
            for (int i = 1; i <= nodeConfig.UseFirstFewPages && i <= totalPages; i++)
            {
                Aspose.Pdf.Page page = doc.Pages[i];
                newPdfDocument.Pages.Add(page);
            }

            // 复制后几页
            for (int i = Math.Max(1, totalPages - nodeConfig.UseLastFewPages + 1); i <= totalPages; i++)
            {
                Aspose.Pdf.Page page = doc.Pages[i];
                newPdfDocument.Pages.Add(page);
            }
            return newPdfDocument;
        }

        //移除目录
        public void RemoveMenu(Aspose.Words.Document document)
        {
            //将 TOC 字段的 FieldStart 节点存储在文档中以便快速访问。
            List<FieldStart> fieldStarts = new List<FieldStart>();

            //这是一个列表，用于存储在指定目录中找到的节点。它们将在此方法结束时被删除。
            List<Node> nodeList = new List<Node>();
            foreach (FieldStart start in document.GetChildNodes(NodeType.FieldStart, true))
            {
                if (start.FieldType == FieldType.FieldTOC)
                {
                    fieldStarts.Add(start);
                }
            }
            for (int index = 0; index < fieldStarts.Count; index++)
            {
                //确保传递的索引指定的目录存在。
                if (index > fieldStarts.Count - 1)
                    throw new ArgumentOutOfRangeException("TOC index is out of range");
                bool isRemoving = true;

                Node currentNode = fieldStarts[index];
                if (currentNode == null)
                {
                    continue;
                }
                while (isRemoving)
                {
                    //存储这些节点并稍后将它们全部删除会更安全。
                    nodeList.Add(currentNode);
                    if (currentNode == null)
                    {
                        break;
                    }
                    currentNode = currentNode.NextPreOrder(document);
                    //一旦我们遇到 FieldTOC 类型的 FieldEnd 节点，
                    //我们知道我们已经到了当前目录的末尾并在此停止。
                    if (currentNode != null && currentNode.NodeType == NodeType.FieldEnd)
                    {
                        FieldEnd fieldEnd = (FieldEnd)currentNode;
                        if (fieldEnd.FieldType == FieldType.FieldTOC)
                            isRemoving = false;
                    }
                }

                foreach (Node node in nodeList)
                {
                    if (node != null && node.ParentNode != null)
                    {
                        node.Remove();
                    }
                }
            }

            // 删除所有节的页眉和页脚
            foreach (Section section in document.Sections)
            {
                section.ClearHeadersFooters();
            }
        }

        public void InitHyperLink(List<HyperLinkDto> hyperLinkList, Aspose.Words.Document document)
        {
            try
            {
                // 遍历所有段落
                foreach (Paragraph para in document.GetChildNodes(NodeType.Paragraph, true))
                {
                    // 检查段落中是否有超链接
                    for (int i = 0; i < para.Runs.Count; i++)
                    {
                        Run run = para.Runs[i];
                        if (run.Font.StyleIdentifier == StyleIdentifier.Hyperlink ||
                            run.Font.StyleIdentifier == StyleIdentifier.FollowedHyperlink)
                        {
                            // 移除超链接，但保留文本
                            string text = run.Text.Contains("HYPERLINK") ? "" : run.Text;
                            run.Text = "";
                            para.Runs.Insert(i, new Run(document, text));
                            i++; // 跳过新插入的Run
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 处理超链接降噪的时候，有些文档会提示文档结构非法，先catch一下，不影响后续功能
                Console.WriteLine("文档解析有不支持的内容：" + ex.Message);
            }
        }

        public string ExtractSentenceBeforeKeyword(string paragraph, string keyword)
        {
            // 查找关键词在段落中的位置
            int keywordPosition = paragraph.IndexOf(keyword);

            if (keywordPosition == -1) return ""; // 如果关键词不在段落中，返回空字符串

            // 定义停止字符
            char[] stopChars = new char[] { '\r', '\n', '.', '?', '!', ';', ',', ')', '。', '，', '}', '）' };

            // 从关键词位置开始向前搜索，直到遇到停止字符
            int startIndex = keywordPosition;
            while (startIndex > 0 && !stopChars.Contains(paragraph[startIndex - 1]))
            {
                startIndex--;
            }

            // 如果在关键词前找到了停止字符，返回从停止字符到关键词之间的文本
            if (startIndex < keywordPosition)
            {
                return paragraph.Substring(startIndex, keywordPosition - startIndex);
            }

            // 如果在关键词前没有找到任何停止字符，返回空字符串
            return "";
        }

        public async Task<List<ImageUploadCallBackDto>> UploadDocument(List<BatchDocumentUploadParam> batchDocumentUploadParams)
        {
            if (batchDocumentUploadParams.Count == 0)
            {
                return new List<ImageUploadCallBackDto>();
            }

            string gptBuilderUrl = this._mysoftContextFactory.GetMysoftContext().GptBuilderUrl + GPTBuilderRequestPathConst.UploadDocument;

            string res = await _mysoftApiDomainService.PostAsync(gptBuilderUrl, JsonConvert.SerializeObject(batchDocumentUploadParams));
            //用同步的方式拿到图片信息
            List<ImageUploadCallBackDto> imageUploadCallBackDtos = JsonConvert.DeserializeObject<ReturnDto<List<ImageUploadCallBackDto>>>(res).Data;

            return imageUploadCallBackDtos;
        }

        /// <summary>
        /// OCR识别
        /// </summary>
        /// <param name="document"></param>
        public async Task OCRExtract(Aspose.Words.Document document, string ocrService)
        {
            var ocrRecognizeService = _kernel.GetRequiredService<IOcrRecognizeService>(ocrService);
            IEnumerable<Shape> shapes = document.GetChildNodes(NodeType.Shape, true)
                .OfType<Shape>().Where(s => s.HasImage);
            foreach (Shape shape in shapes)
            {
                // 获取图片的宽度和高度
                double imageWidth = shape.ImageData.ImageSize.WidthPoints;
                double imageHeight = shape.ImageData.ImageSize.HeightPoints;

                // 判断图片大小是否小于 300 px（宽度和高度）
                if (imageWidth < ImageHandleParam.OCRLimitWidth || imageHeight < ImageHandleParam.OCRLimitHeight)
                {
                    // 如果图片小于 300 px，不识别，直接替换为空字符串
                    try
                    {
                        var shapeParentNode = shape.ParentNode;
                        Node placeholder;
                        if (shapeParentNode is GroupShape)
                        {
                            placeholder = new Shape(shapeParentNode.Document, ShapeType.TextPlainText);
                            // 把占位符插入到原图前面
                            NodeCollection nodes = shapeParentNode.ChildNodes;
                            int index = nodes.IndexOf(shape);
                            nodes.Insert(index, placeholder);
                        }
                        else
                        {
                            placeholder = new Run(document, "");
                            shape.ParentNode.InsertBefore(placeholder, shape);
                        }
                        
                        shape.Remove();
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine("清理小图片，插入占位符失败：{0}", e.Message);
                    }
                    
                    continue;
                }
                //拿到图片调用OCR
                MemoryStream imageMemoryStream = null;
                try
                {
                    imageMemoryStream = new MemoryStream();
                    shape.ImageData.Save(imageMemoryStream);
                    // 使用重试机制调用OCR服务
                    var imgContent = await ExecuteOcrWithRetry(ocrRecognizeService, new OcrRequest { memoryStream = imageMemoryStream,ocrCode = ocrService }, CancellationToken.None);
                    var chatRunDto = await GetChatRunDto();
                    if (imgContent.WordsResult != null)
                    {
                        chatRunDto.AddWordsResult(imgContent.WordsResult);
                    }
                    if (!imgContent.Success)
                    {
                        continue;
                    }

                    try
                    {
                        var shapeParentNode = shape.ParentNode;
                        Node placeholder;
                        if (shapeParentNode is GroupShape)
                        {
                            placeholder = new Shape(shapeParentNode.Document, ShapeType.TextPlainText);
                            // 把占位符插入到原图前面
                            NodeCollection nodes = shapeParentNode.ChildNodes;
                            int index = nodes.IndexOf(shape);
                            nodes.Insert(index, placeholder);
                        }
                        else
                        {
                            // 创建一个新的文本节点，用于替换图片
                            placeholder = new Run(document, imgContent.Content);
                            // 将新的文本节点插入到图片节点之前
                            shape.ParentNode.InsertBefore(placeholder, shape);
                        }

                        // 从文档中移除图片节点
                        shape.Remove();
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine("识别图片，插入占位符失败：{0}", e.Message);
                    }
                }
                finally
                {
                    // 确保MemoryStream被正确释放
                    imageMemoryStream?.Dispose();
                }
            }
            await Task.CompletedTask;
        }

        /// <summary>
        /// OCR识别PDF
        /// </summary>
        /// <param name="document"></param>
        public async Task OCRExtractPdf(Aspose.Pdf.Document document, string ocrService, Dictionary<Guid, string> ocrReplaceList)
        {
            var ocrRecognizeService = _kernel.GetRequiredService<IOcrRecognizeService>(ocrService);
            // 遍历每个页面
            foreach (Page page in document.Pages)
            {
                // 获取页面中的图片集合
                ImagePlacementAbsorber absorber = new ImagePlacementAbsorber();
                absorber.Visit(page);

                // 遍历每个图片
                foreach (ImagePlacement imagePlacement in absorber.ImagePlacements)
                {
                    // 不满足大小的直接返回
                    if (imagePlacement.Image.Height < ImageHandleParam.OCRLimitWidth || imagePlacement.Image.Width < ImageHandleParam.OCRLimitHeight)
                    {
                        continue;
                    }
                    MemoryStream imageStream = null;
                    try
                    {
                        imageStream = new MemoryStream();
                        imagePlacement.Save(imageStream);
                        // 转成web压缩图片
                        byte[] webpData = ImageExtractionHelper.ConvertToWebPAsync(document.FileName, imageStream.ToArray());
                        if (webpData != null)
                        {
                            imageStream.SetLength(0); // 清空当前内容
                            imageStream.Write(webpData, 0, webpData.Length); // 写入新的数据
                            imageStream.Position = 0; // 将流的位置重置为开头
                        }
                        // 使用重试机制调用OCR服务
                        var imgContent = await ExecuteOcrWithRetry(ocrRecognizeService, new OcrRequest { memoryStream = imageStream,ocrCode = ocrService }, CancellationToken.None);
                        var chatRunDto = await GetChatRunDto();
                        if (!imgContent.Success)
                        {
                            continue;
                        }
                        
                        if (imgContent.WordsResult != null && imgContent.WordsResult.Count > 0)
                        {
                            chatRunDto.AddWordsResult(imgContent.WordsResult);
                        }
                        
                        Guid ocrGUID = Guid.NewGuid();

                        // OCR识别出来的文本
                        string ocrText = imgContent.Content;
                        ocrReplaceList.Add(ocrGUID, ocrText);
                        // 创建文本框，并设置文本内容和位置
                        TextFragment textFragment = new TextFragment(ocrGUID.ToString());
                        textFragment.Position = new Position((float)imagePlacement.Rectangle.LLX, (float)imagePlacement.Rectangle.LLY + (float)imagePlacement.Rectangle.Height - 10);
                        textFragment.TextState.FontSize = 12;
                        // 嵌入字体
                        textFragment.TextState.Font.IsEmbedded = true;

                        //// 替换页面中的图片为文本占位符
                        TextBuilder textBuilder = new TextBuilder(page);
                        textBuilder.AppendText(textFragment);
                    }
                    finally
                    {
                        // 确保MemoryStream被正确释放
                        imageStream?.Dispose();
                    }
                }
            }

            await Task.CompletedTask;
        }

        public async Task<ChatRunDto> GetChatRunDto()
        {
            var chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto)) ?? new ChatRunDto();
            return await Task.FromResult(chatRunDto);
        }

        public class BaseKeyValueClass
        {
            public string Key;
            public string Value;
        }

        /// <summary>
        /// 将输入文档转换成图像流
        /// </summary>
        /// <param name="inputDoc"></param>
        /// <returns></returns>
        public Aspose.Pdf.Document ConvertPdfPagesToImagesInMemory(Aspose.Pdf.Document inputDoc)
        {
            // 创建一个新的PDF文档
            Aspose.Pdf.Document newPdfDocument = new Aspose.Pdf.Document();
            // 遍历每一页
            for (int i = 0; i < inputDoc.Pages.Count; i++)
            {
                // 获取当前页
                Page page = inputDoc.Pages[i + 1];

                // 获取页面的宽度和高度
                int width = (int)(page.PageInfo.Width * 1.5);
                int height = (int)(page.PageInfo.Height * 1.5);
                // 设置图像分辨率
                int resolution = 150;
                // 转换页面为内存中的PNG图像流
                MemoryStream ms = ConvertToPngMemoryStream(page, resolution, width, height);
                imageStreams.Add(ms);
                // 创建一个新页面
                Aspose.Pdf.Page newPage = newPdfDocument.Pages.Add();

                // 将图像数据加载到Aspose.Pdf.Image对象
                Aspose.Pdf.Image pdfImage = new Aspose.Pdf.Image();
                pdfImage.ImageStream = ms;

                // 设置页面大小与图像相同
                using (var img = System.Drawing.Image.FromStream(ms))
                {
                    newPage.PageInfo.Height = img.Height;
                    newPage.PageInfo.Width = img.Width;
                }

                // 添加图像到页面
                newPage.Paragraphs.Add(pdfImage);
            }

            return newPdfDocument;
        }


        private MemoryStream ConvertToPngMemoryStream(Page page, int resolution, int width, int height)
        {
            // 创建一个PngDevice对象
            PngDevice pngDevice = new PngDevice(width, height, new Resolution(resolution));

            // 创建一个MemoryStream对象

            MemoryStream ms = new MemoryStream();
            // 将页面渲染到MemoryStream
            pngDevice.Process(page, ms);

            // 重置MemoryStream的位置
            ms.Position = 0;

            return ms;
        }

        private void UseSystemFonts(Aspose.Pdf.Document doc)
        {
            ;
            // 遍历每一页
            for (int i = 1; i <= doc.Pages.Count; i++)
            {
                Page page = doc.Pages[i];
                TextFragmentAbsorber absorber = new TextFragmentAbsorber();
                page.Accept(absorber);
                foreach (TextFragment fragment in absorber.TextFragments)
                {
                    // 替换为系统字体
                    if (fragment.TextState.Font.IsEmbedded == false)
                    {
                        fragment.TextState.Font = FontRepository.FindFont("simsun");
                        fragment.TextState.FontSize = 8;
                    }
                }
            }
        }

        public Aspose.Pdf.Document ConvertAndCreatePdfInMemory(Aspose.Pdf.Document inputDoc)
        {
            for (int i = 1; i <= inputDoc.Pages.Count; i++)
            {
                SetDefaultFont(inputDoc.Pages[i]);
            }
            Aspose.Pdf.Document newPdfDocument = ConvertPdfPagesToImagesInMemory(inputDoc);
            MemoryStream msNew = new MemoryStream();
            newPdfDocument.Save(msNew, Aspose.Pdf.SaveFormat.Pdf);
            msNew.Position = 0;
            Aspose.Pdf.Document pdfDocumentFinal = new Aspose.Pdf.Document(msNew);
            imageStreams.Add(msNew);
            return pdfDocumentFinal;
        }

        private void SetDefaultFont(Page page)
        {
            var defaultFontFamily = "simsun";
            var notSupportFontFamily = "DejaVuSansCondensed$DejaVuSansCondensed-Bold$STSongStd-Light-Acro-UniGB-UTF16-H$STSongStd-Light-Acro,Bold-UniGB-UTF16-H$STSongStd-Light-Acro,Italic-UniGB-UTF16-H$STSongStd-Light-Acro,BoldItalic-UniGB-UTF16-H$Jomolhari";
            bool isLog;
            if (string.IsNullOrEmpty(defaultFontFamily) == false && string.IsNullOrEmpty(notSupportFontFamily) == false)
            {
                #region  对比字体,查找是否有不存在的字体

                //Aspose.Pdf.Text.Font[] fonts = doc.FontUtilities.GetAllFonts();
                //var pdfFont = fonts.Select(f => f.FontName).ToList();

                //InstalledFontCollection myFont = new InstalledFontCollection();
                //var myFontFamilies = myFont.Families.Select(f => f.Name).ToList();
                var notSupportFonts =
                    notSupportFontFamily.Split(new char[] { '$' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                //pdfFont.Except(myFontFamilies).ToList();


                #endregion

                #region  如果存在不支持的字体用默认字体

                if (notSupportFonts.Count > 0)
                {
                    try
                    {
                        TextFragmentAbsorber textFragmentAbsorber = new TextFragmentAbsorber();
                        page.Accept(textFragmentAbsorber);
                        TextFragmentCollection textFragmentCollection = textFragmentAbsorber.TextFragments;
                        var defaultFont = FontRepository.FindFont(defaultFontFamily, true);
                        foreach (TextFragment textFragment in textFragmentCollection)
                        {
                            textFragment.TextState.Font = defaultFont;
                            if (textFragment.TextState.CharacterSpacing < 0 && textFragment.Text.Contains(" "))
                            {
                                textFragment.TextState.CharacterSpacing = (float)-2.5;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                    }
                }

                #endregion
            }
        }

        #region OCR重试机制

        /// <summary>
        /// 使用重试机制执行OCR服务
        /// </summary>
        /// <param name="ocrRecognizeService">OCR识别服务</param>
        /// <param name="ocrRequest">OCR请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>OCR响应</returns>
        protected async Task<OcrResponse> ExecuteOcrWithRetry(IOcrRecognizeService ocrRecognizeService, OcrRequest ocrRequest, CancellationToken cancellationToken)
        {
            int retryCount = 0;
            var delay = TimeSpan.FromSeconds(InitialDelaySeconds);
            var random = new Random();

            while (true)
            {
                try
                {
                    return await ocrRecognizeService.Execute(ocrRequest);
                }
                catch (LLmCustomException ocrEx)
                {
                    // 处理OCR异常
                    var shouldRetry = await HandleOcrException(
                        ocrEx,
                        retryCount,
                        cancellationToken);

                    if (!shouldRetry)
                        throw;

                    retryCount++;
                    // 检查是否超过最大重试次数
                    if (retryCount > MaxRetryCount)
                    {
                        throw;
                    }

                    // 计算当前延迟时间（包含抖动）
                    var currentDelayWithJitter = CalculateRetryDelay(retryCount, delay, random);
                    await Task.Delay(currentDelayWithJitter, cancellationToken);

                    // 更新下次迭代的延迟时间
                    delay = GetNextRetryDelay(retryCount);
                }
            }
        }

        /// <summary>
        /// 处理OCR异常
        /// </summary>
        /// <param name="ocrEx">OCR异常</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否应该重试</returns>
        private async Task<bool> HandleOcrException(
            LLmCustomException ocrEx,
            int retryCount,
            CancellationToken cancellationToken)
        {
            // 判断是否应该重试的状态码和错误类型
            bool shouldRetry = ShouldRetryOcrException(ocrEx);

            if (!shouldRetry)
            {
                return false;
            }

            // 首次重试时显示等待消息
            if (retryCount == 0 && IsBrowserRequest())
            {
                var chatRunDto = await GetChatRunDto();
                await chatRunDto.EventBus.PublishAsync(new TextEvent("", ResourceWaitingMessage));
            }

            return true; // 应该重试
        }

        /// <summary>
        /// 判断OCR异常是否应该重试
        /// </summary>
        /// <param name="ocrEx">OCR异常</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryOcrException(LLmCustomException ocrEx)
        {
            // 只有当statusCode为"Ocr"且错误消息包含"异常编码：400"时才重试
            if (ocrEx.StatusCode == "Ocr" && !string.IsNullOrEmpty(ocrEx.Message))
            {
                return ocrEx.Message.Contains("异常编码：400");
            }

            return false; // 其他情况不重试
        }

        /// <summary>
        /// 计算重试延迟时间（包含抖动）
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <param name="baseDelay">基础延迟时间</param>
        /// <param name="random">随机数生成器</param>
        /// <returns>包含抖动的延迟时间</returns>
        private TimeSpan CalculateRetryDelay(int retryCount, TimeSpan baseDelay, Random random)
        {
            // 添加随机抖动以避免雷群效应
            var jitter = TimeSpan.FromMilliseconds(random.Next(MinJitterMs, MaxJitterMs));
            return baseDelay + jitter;
        }

        /// <summary>
        /// 获取下次重试的延迟时间
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <returns>下次重试的延迟时间</returns>
        private TimeSpan GetNextRetryDelay(int retryCount)
        {
            // 重试延迟策略：1s, 5s, 15s, 30s, 60s...
            return retryCount switch
            {
                1 => TimeSpan.FromSeconds(5),
                2 => TimeSpan.FromSeconds(15),
                3 => TimeSpan.FromSeconds(30),
                _ => TimeSpan.FromSeconds(59)
            };
        }

        /// <summary>
        /// 判断是否为浏览器请求
        /// </summary>
        /// <returns>是否为浏览器请求</returns>
        private bool IsBrowserRequest()
        {
            var userAgent = _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].FirstOrDefault();
            if (string.IsNullOrEmpty(userAgent))
                return false;

            var browserKeywords = new[] { "Chrome", "Firefox", "Safari", "Edge", "Opera", "MSIE", "Trident" };
            return browserKeywords.Any(keyword => userAgent.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        #endregion
    }
}
