using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Fields;
using DocumentFormat.OpenXml.Office2010.Word;
using Mysoft.GPTEngine.Application.Configuration;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Aspose.Words.Loading;
using static Mysoft.GPTEngine.Domain.TextExtractDecode.BaseDecode;
using Newtonsoft.Json;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using Aspose.Pdf;
using Aspose.Pdf.Text;
using UglyToad.PdfPig;
using Aspose.Words.Saving;
using static Aspose.Pdf.DocSaveOptions;
using Aspose.Words.Layout;
using static Aspose.Pdf.Text.TextExtractionOptions;
using Aspose.Pdf.Operators;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode
{
    public class PdfDecoder : BaseDecode, IDocumentDecoder
    {
        private readonly MysoftApiService _mysoftApiDomainService;

        public PdfDecoder(Kernel _kernel,MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper)
            : base(_kernel,mysoftApiDomainService, mysoftContextFactory, httpContextAccessor, mapper)
        {
            _mysoftApiDomainService = mysoftApiDomainService;
        }

        public async Task<FileContent> DecodeAsync(MemoryStream data, ExecutionSetting executionSetting)
        {
            try
            {
                using (data)
                {
                    // 初始化aspose LIcense
                    AsposeHelper.InitAspose();
                    AsposeHelper.InitAsposeWord();
                    int imageCount = 1;
                    var result = new FileContent("text/pdf");
                    List<HyperLinkDto> hyperLinkList = new List<HyperLinkDto>();
                    List<ImageUploadCallBackDto> imageUploadCallBackDtos = new List<ImageUploadCallBackDto>();

                    Aspose.Words.Document wordPdf = new Aspose.Words.Document(data);
                    // 遍历文档中的所有节点
                    // 设置了“HasImage”标志的形状节点包含并显示图像。
                    IEnumerable<Shape> shapes = wordPdf.GetChildNodes(NodeType.Shape, true)
                        .OfType<Shape>().Where(s => s.HasImage);

                    foreach (Field field in wordPdf.Range.Fields)
                    {
                        if (field.Type == Aspose.Words.Fields.FieldType.FieldHyperlink)
                        {
                            FieldHyperlink hyperlink = (FieldHyperlink)field;

                            //检查超链接是否不是本地链接（忽略书签）。
                            if (hyperlink.SubAddress != null)
                                continue;

                            HyperLinkDto hyperLinkDto = new HyperLinkDto();
                            hyperLinkDto.HyperLinkURL = hyperlink.Address;
                            hyperLinkDto.HyperLinkText = hyperlink.Result;
                            hyperLinkDto.HyperLinkGUID = Guid.NewGuid();
                            hyperLinkList.Add(hyperLinkDto);
                        }
                    }
                    // 循环遍历形状。
                    foreach (Shape shape in shapes)
                    {
                        // 获取图片的宽度和高度
                        double imageWidth = shape.ImageData.ImageSize.WidthPoints;
                        double imageHeight = shape.ImageData.ImageSize.HeightPoints;

                        if (executionSetting.IsUploadImage == (int)IsEnableEnum.No)
                        {
                            // 创建一个新的文本节点，用于替换图片
                            Aspose.Words.Run placeholder = new Aspose.Words.Run(wordPdf, "");
                            // 将新的文本节点插入到图片节点之前
                            shape.ParentNode.InsertBefore(placeholder, shape);
                            // 从文档中移除图片节点
                            shape.Remove();
                        }
                        else
                        {
                            // 判断图片大小是否小于 100 px（宽度和高度）
                            if (imageWidth < ImageHandleParam.Width || imageHeight < ImageHandleParam.Height)
                            {
                                // 如果图片小于 100 px，则跳过替换操作
                                continue;
                            }
                            //图片guid
                            Guid documentGUID = Guid.NewGuid();
                            result.BatchDocumentUploadParams.Add(ImageToBatchDocumentUploadParams(shape.ImageData, documentGUID, imageCount));
                            string placeHolder = string.Format(ImageHandleParam.ImagePlaceholder, documentGUID);
                            // 创建一个新的文本节点，用于替换图片
                            Aspose.Words.Run placeholder = new Aspose.Words.Run(wordPdf, placeHolder);
                            // 将新的文本节点插入到图片节点之前
                            shape.ParentNode.InsertBefore(placeholder, shape);
                            // 从文档中移除图片节点
                            shape.Remove();
                            if (result.BatchDocumentUploadParams.Count == 30)
                            {
                                //执行上传
                                imageUploadCallBackDtos.AddRange(await UploadDocument(result.BatchDocumentUploadParams));

                                result.BatchDocumentUploadParams = new List<BatchDocumentUploadParam>();
                            }
                            imageCount++;
                        }
                    }
                    if (result.BatchDocumentUploadParams.Count > 0)
                    {
                        //执行上传
                        imageUploadCallBackDtos.AddRange(await UploadDocument(result.BatchDocumentUploadParams));
                        result.BatchDocumentUploadParams = new List<BatchDocumentUploadParam>();
                    }
                    Aspose.Words.Saving.MarkdownSaveOptions options = new Aspose.Words.Saving.MarkdownSaveOptions();
                    options.SaveFormat = Aspose.Words.SaveFormat.Markdown;
                    options.UpdateFields = true;
                    options.ExportImagesAsBase64 = true;
                    string md = string.Empty;
                    using (MemoryStream ms = new MemoryStream())
                    {
                        wordPdf.Save(ms, options);
                        md = Encoding.UTF8.GetString(ms.ToArray());
                        // 移除base64格式的图片文件，因为有些Word带有复杂结构的格式，在转md时会被自动识别为图片格式
                        md = ImageExtractionHelper.RemoveBase64Img(md);
                    }
                    foreach (var hyperLink in hyperLinkList)
                    {
                        md = md.Replace($"[{hyperLink.HyperLinkText}]({hyperLink.HyperLinkURL})", $"{{{{hyperlink:{hyperLink.HyperLinkGUID}}}}}");
                    }

                    result.Sections.Add(new FileSection(1, md, false));
                    result.HyperLinkList = hyperLinkList;
                    //替换图片占位符
                    foreach (var item in imageUploadCallBackDtos)
                    {
                        //如果上传成功
                        if (item.isSuccess)
                        {
                            foreach (var m in result.Sections)
                            {
                                m.Content = m.Content.Replace($"{{{{image:{item.fileGUID}}}}}", $"{{{{image:{item.documentUploadResult.documentGuid}}}}}");
                            }
                        }
                    }
                    return result;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.StackTrace);
                Console.WriteLine(ex.Message);
                Console.WriteLine(ex.InnerException);
                return null;
            }
        }

        /// <summary>
        /// 图片转文档服务上传对象
        /// </summary>
        /// <returns></returns>
        BatchDocumentUploadParam ImageToBatchDocumentUploadParams(ImageData imageData, Guid fileGUID, int imageCount)
        {
            // 保存图片为文件
            using (MemoryStream imageMemoryStream = new MemoryStream())
            {
                imageData.Save(imageMemoryStream);
                byte[] imageBytes = imageMemoryStream.ToArray();

                BatchDocumentUploadParam batchDocumentUploadParam = new BatchDocumentUploadParam();
                DocumentUploadParam documentUploadParam = new DocumentUploadParam();
                documentUploadParam.fileName = string.Format(ImageHandleParam.ImageName, imageCount);
                documentUploadParam.fileSize = imageBytes.Length.ToString();
                documentUploadParam.fileGUID = fileGUID.ToString();

                //batchDocumentUploadParam.documentContent = imageBytes;
                batchDocumentUploadParam.documentContentBase64 = Convert.ToBase64String(imageBytes);
                batchDocumentUploadParam.documentUploadParam = documentUploadParam;

                imageBytes = null;

                return batchDocumentUploadParam;
            }
        }

        public bool CanDecode(string documentType)
        {
            return documentType == "Excel";
        }

        public async Task<DocumentReadingDto> DomentReadAsync(MemoryStream data, NodeConfig nodeConfig)
        {
            using (data)
            {
                var ocrService = nodeConfig.IsEnableOCR && !string.IsNullOrWhiteSpace(nodeConfig.OcrService)
                    ? nodeConfig.OcrService
                    : "";
                var isEnableOCR = !string.IsNullOrWhiteSpace(ocrService);
                DocumentReadingDto documentReadingDto = new DocumentReadingDto();
                AsposeHelper.InitAspose();

                // 加载 PDF 文档
                Aspose.Pdf.Document pdfDocument = new Aspose.Pdf.Document(data);
                // 创建一个新的Document对象用于保存新的PDF
                Aspose.Pdf.Document newPdfDocument = new Aspose.Pdf.Document();
                newPdfDocument = TocExcutePdf(pdfDocument, newPdfDocument, nodeConfig);
                // 直接替换ocr识别内容到PDF上会导致部分文字转义乱码，改为插入guid，在之后替换
                Dictionary<Guid, string> ocrReplaceList = new Dictionary<Guid, string>();
                // 判断一下是否开启了OCR
                if (isEnableOCR)
                {
                    await OCRExtractPdf(newPdfDocument, ocrService, ocrReplaceList);
                }

                TextExtractionOptions textExtractionOptions = new TextExtractionOptions(TextFormattingMode.Raw);

                // 遍历每一页并提取文本
                for (var i = 1; i <= newPdfDocument.Pages.Count; i++)
                {
                    // 初始化TextAbsorber对象
                    TextAbsorber textAbsorber = new TextAbsorber(textExtractionOptions);
                    // 将页面内容提取到文本提取器
                    textAbsorber.Visit(newPdfDocument.Pages[i]);
                    // 从文本提取器中获取提取的文本
                    String extractedText = textAbsorber.Text;
                    if (!string.IsNullOrWhiteSpace(extractedText))
                    {
                        documentReadingDto.Content += extractedText;
                    }
                }

                // 替换占位符
                foreach (var item in ocrReplaceList)
                {
                    documentReadingDto.Content = documentReadingDto.Content.Replace(item.Key.ToString(), item.Value);
                }

                if (imageStreams.Any())
                {
                    imageStreams.ForEach(i =>
                    {
                        i.Close();
                    });
                }
                return await Task.FromResult(documentReadingDto);
            }
        }

        private void TableToMd(List<string> markdownList, Aspose.Words.Document wordPdf)
        {
            foreach (Aspose.Words.Tables.Table table in wordPdf.GetChildNodes(NodeType.Table, true))
            {

                List<string> rows = new List<string>();
                rows.Add(""); // 开始新表头

                // 首先获取所有的列数，以便在Markdown表格中正确地放置分隔线
                int columnCount = 0;
                foreach (Aspose.Words.Tables.Row row in table.Rows)
                {
                    columnCount = 0;
                    foreach (Aspose.Words.Tables.Cell cell in row.Cells)
                    {
                        columnCount++;
                    }
                    break; // 只需要第一行的列数
                }

                // 表头下的分隔线
                string headerSeparator = "|";
                for (int i = 0; i < columnCount; i++)
                {
                    headerSeparator += " --- |";
                }

                foreach (Aspose.Words.Tables.Row row in table.Rows)
                {
                    string rowContent = "|"; // 开始新行

                    foreach (Aspose.Words.Tables.Cell cell in row.Cells)
                    {
                        string cellText = cell.GetText().Replace("\r","");
                        rowContent += " " + cellText.Trim() + " |"; // 添加单元格内容
                    }
                    rows.Add(rowContent); // 添加完成的行
                }
                // 将表头分隔线添加到行列表中
                rows.Insert(1, headerSeparator);
                // 构建完整的Markdown表格字符串
                string markdownTable = string.Join("\n", rows);
                markdownList.Add(markdownTable);
            }
        }

        public async Task<string> GetDocumentTxt(MemoryStream data, NodeConfig nodeConfig)
        {
            using (data)
            {
                var ocrService = nodeConfig.IsEnableOCR && !string.IsNullOrWhiteSpace(nodeConfig.OcrService)
                    ? nodeConfig.OcrService
                    : "";
                var isEnableOCR = !string.IsNullOrWhiteSpace(ocrService);
                AsposeHelper.InitAspose();
                Aspose.Pdf.Document pdfDocument = new Aspose.Pdf.Document(data);
                // 创建一个新的Document对象用于保存新的PDF
                Aspose.Pdf.Document newPdfDocument = new Aspose.Pdf.Document();
                newPdfDocument = TocExcutePdf(pdfDocument, newPdfDocument, nodeConfig);
                // 直接替换ocr识别内容到PDF上会导致部分文字转义乱码，改为插入guid，在之后替换
                Dictionary<Guid, string> ocrReplaceList = new Dictionary<Guid, string>();
                // 判断一下是否开启了OCR
                if (isEnableOCR)
                {
                    await OCRExtractPdf(newPdfDocument, ocrService, ocrReplaceList);
                }

                string result = string.Empty;
                TextExtractionOptions textExtractionOptions = new TextExtractionOptions(TextFormattingMode.Raw);
                // 遍历每一页并提取文本
                for (var i = 1; i <= newPdfDocument.Pages.Count; i++)
                {
                    // 初始化TextAbsorber对象
                    TextAbsorber textAbsorber = new TextAbsorber(textExtractionOptions);
                    // 将页面内容提取到文本提取器
                    textAbsorber.Visit(newPdfDocument.Pages[i]);
                    // 从文本提取器中获取提取的文本
                    String extractedText = textAbsorber.Text;
                    if (!string.IsNullOrWhiteSpace(extractedText))
                    {
                        result += extractedText;
                    }
                }

                // 替换占位符
                foreach (var item in ocrReplaceList)
                {
                    result = result.Replace(item.Key.ToString(), item.Value);
                }

                if (imageStreams.Any())
                {
                    imageStreams.ForEach(i => { i.Close(); });
                }

                return await Task.FromResult(result);
            }
        }

        public async Task<List<ImageUploadCallBackDto>> ConvertDocumentToImg(MemoryStream ms)
        {
            AsposeHelper.InitAspose();
            LoadPdfFonts();
            Aspose.Pdf.Document pdf = new Aspose.Pdf.Document(ms);
            if (pdf.Pages.Count > 10)
            {
                return null;
            }
            ConvertAndCreatePdfInMemory(pdf);
            byte[] documentByte = imageStreams.Last().ToArray();
            BatchDocumentUploadParam batchDocumentUploadParam = new BatchDocumentUploadParam();
            DocumentUploadParam documentUploadParam = new DocumentUploadParam();
            documentUploadParam.fileName = $"PDF转图片.pdf";
            documentUploadParam.fileSize = documentByte.Length.ToString();
            documentUploadParam.fileGUID = Guid.NewGuid().ToString();
            batchDocumentUploadParam.documentContentBase64 = Convert.ToBase64String(documentByte);
            batchDocumentUploadParam.documentUploadParam = documentUploadParam;
            if (imageStreams.Any())
            {
                imageStreams.ForEach(i => { i.Close(); });
            }
            return await UploadDocument(new List<BatchDocumentUploadParam>() { batchDocumentUploadParam });
        }
    }
}
