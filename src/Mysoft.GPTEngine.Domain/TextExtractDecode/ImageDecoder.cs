using Aspose.Pdf.Text;
using Aspose.Pdf;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode
{
    public class ImageDecoder : BaseDecode, IDocumentDecoder
    {
        public ImageDecoder(Kernel kernel, MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper) : base(kernel, mysoftApiDomainService, mysoftContextFactory, httpContextAccessor, mapper)
        {
        }

        public bool CanDecode(string documentType)
        {
            return documentType == "Image";
        }

        public Task<FileContent> DecodeAsync(MemoryStream file, ExecutionSetting executionSetting)
        {
            throw new NotImplementedException();
        }

        public Task<DocumentReadingDto> DomentReadAsync(MemoryStream file, NodeConfig nodeConfig)
        {
            throw new NotImplementedException();
        }

        public Task<List<ImageUploadCallBackDto>> ConvertDocumentToImg(MemoryStream ms)
        {
            throw new NotImplementedException();
        }

        public async Task<string> GetDocumentTxt(MemoryStream data, NodeConfig nodeConfig)
        {
            using (data)
            {
                var ocrService = nodeConfig.IsEnableOCR && !string.IsNullOrWhiteSpace(nodeConfig.OcrService)
                    ? nodeConfig.OcrService
                    : "";
                if (string.IsNullOrWhiteSpace(ocrService))
                {
                    return await Task.FromResult(string.Empty);
                }

                var ocrRecognizeService = _kernel.GetRequiredService<IOcrRecognizeService>(ocrService);
                // 使用重试机制调用OCR服务
                var imgContent = await ExecuteOcrWithRetry(ocrRecognizeService, new OcrRequest { memoryStream = data,ocrCode = ocrService }, CancellationToken.None);
                if (!imgContent.Success || imgContent.WordsResult == null)
                {
                    return string.Empty;
                }
                return await Task.FromResult(imgContent.Content);
            }
        }
    }
}
