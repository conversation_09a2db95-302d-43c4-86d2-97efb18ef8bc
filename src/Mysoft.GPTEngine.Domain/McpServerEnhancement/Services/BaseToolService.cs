using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Services
{
    /// <summary>
    /// 基础工具服务 - 所有动态工具的底层调用实现
    /// </summary>
    public class BaseToolService
    {
        private readonly ILogger<BaseToolService> _logger;
        private readonly ApiClientService _apiClientService;

        public BaseToolService(ILogger<BaseToolService> logger, ApiClientService apiClientService)
        {
            _logger = logger;
            _apiClientService = apiClientService;
        }

        /// <summary>
        /// 执行工具调用的基础方法 (通过API获取数据)
        /// </summary>
        /// <param name="toolId">工具ID</param>
        /// <param name="toolName">工具名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>执行结果</returns>
        public async Task<string> ExecuteToolAsync(string toolId, string toolName, Dictionary<string, object> parameters)
        {
            _logger.LogInformation("执行工具调用: {ToolName} (ID: {ToolId})", toolName, toolId);

            try
            {
                // 通过API获取实际数据
                var apiResult = await _apiClientService.GetDataAsync(toolId, parameters);

                // 解析API返回的数据
                var apiResponse = JsonSerializer.Deserialize<JsonElement>(apiResult);

                // 提取并重构数据格式
                object responseData = null;

                // 检查API响应是否包含嵌套的data结构
                if (apiResponse.TryGetProperty("data", out var dataElement) &&
                    dataElement.ValueKind == JsonValueKind.Object)
                {
                    // 将data对象的内容作为响应数据，去掉嵌套
                    responseData = JsonSerializer.Deserialize<object>(dataElement.GetRawText());
                }
                else
                {
                    // 如果没有嵌套结构，直接使用原始响应
                    responseData = JsonSerializer.Deserialize<object>(apiResult);
                }

                // 构建统一的响应格式
                var response = new
                {
                    toolId = toolId,
                    toolName = toolName,
                    parameters = parameters,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    status = "success",
                    message = $"工具 '{toolName}' 执行成功",
                    data = responseData
                };

                var jsonResult = JsonSerializer.Serialize(response, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                _logger.LogInformation("工具执行完成: {ToolName}", toolName);
                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行工具时发生错误: {ToolName}", toolName);

                var errorResponse = new
                {
                    toolId = toolId,
                    toolName = toolName,
                    parameters = parameters,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    status = "error",
                    message = $"工具 '{toolName}' 执行失败: {ex.Message}"
                };

                return JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
            }
        }


    }
}
