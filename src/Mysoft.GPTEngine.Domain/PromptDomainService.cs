using AutoMapper;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Domain
{
    public class PromptDomainService : DomainServiceBase
    {
        private readonly PromptParamRepostory _promptParamRepostory;
        private readonly PromptRepostory _promptRepostory;
        private readonly PromptTemplateRepostory _promptTemplateRepostory;
        private readonly PromptTestSceneRepostory _promptTestSceneRepostory;

        public PromptDomainService(IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper
            , PromptParamRepostory promptParamRepostory
            , PromptRepostory promptRepostory
            , PromptTemplateRepostory promptTemplateRepostory
            , PromptTestSceneRepostory promptTestSceneRepostory) : base(mysoftContextFactory, httpContextAccessor, mapper)
        {
            _promptRepostory = promptRepostory;
            _promptParamRepostory = promptParamRepostory;
            _promptTemplateRepostory = promptTemplateRepostory;

        }

        public async Task<PromptDto> TestPromptSceneProcess(PromptTestSceneEntity promptTestScene)
        {
            var prompt = await _promptRepostory.GetAsync(x => x.PromptGUID == promptTestScene.PromptGUID);
            var promptTemplate = await _promptTemplateRepostory.GetAsync(x => x.PromptGUID == promptTestScene.PromptGUID);
            var promptParams = await _promptParamRepostory.GetListAsync(x => x.PromptGUID == promptTestScene.PromptGUID);
            var promptDto = new PromptDto
            {
                Id = promptTestScene.PromptGUID,
                PromptTemplate = promptTemplate.PromptTemplate,
                ModelInstanceCode = prompt.ModelInstanceCode,
                ExecutionSetting = prompt.ExecutionSetting,
                InputParam = promptParams?.Where(x => x.ParamType == Shared.Enums.ParamTypeEnum.Input)?.Select(x => new SemanticKernel.Core.Dtos.ParamDto
                {
                    Code = x.ParamCode,
                    Name = x.ParamName,
                    Required = x.IsRequired,
                    Type = x.FiledType,
                    Description = x.Describe,
                    DefaultValue = x.DefaultValue,
                    LiteralCode = x.ParamCode,
                    LiteralValue = JsonUtility.TryGetPropertyValue(promptTestScene.PromptParams, x.ParamCode)
                })?.ToList()
            };
            if (prompt.Mode == 1 && !String.IsNullOrEmpty(promptTemplate.MessageContent))
            {
                promptDto.MessageContents = JsonConvert.DeserializeObject<List<MessageContent>>(promptTemplate.MessageContent).OrderBy(x => x.Index).ToList();
            }

            return await Task.FromResult(promptDto);
        }

    }



}
