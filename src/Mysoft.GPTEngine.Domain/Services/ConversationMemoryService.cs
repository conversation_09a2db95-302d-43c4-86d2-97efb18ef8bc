using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System.Text.Json;

namespace Mysoft.GPTEngine.Domain.Services
{
    /// <summary>
    /// 会话记忆管理服务
    /// 负责管理基于用户ID的会话上下文记忆
    /// </summary>
    public class ConversationMemoryService
    {
        private readonly MysoftMemoryCache _memoryCache;
        private readonly ChatMessageRepostory _chatMessageRepository;
        private readonly ILogger<ConversationMemoryService> _logger;
        private Dictionary<string, string> _toolCallIdMapping = new Dictionary<string, string>();

        // 默认记忆轮数
        private const int DefaultMemoryTurns = 100;
        // 缓存时间（分钟）
        private const int CacheExpirationMinutes = 30;

        public ConversationMemoryService(
            MysoftMemoryCache memoryCache,
            ChatMessageRepostory chatMessageRepository,
            ILogger<ConversationMemoryService> logger)
        {
            _memoryCache = memoryCache;
            _chatMessageRepository = chatMessageRepository;
            _logger = logger;
        }

        /// <summary>
        /// 为ChatHistory加载用户的历史会话记忆（基于用户ID和会话ID）
        /// </summary>
        /// <param name="chatHistory">要加载记忆的ChatHistory</param>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="maxTurns">最大记忆轮数</param>
        /// <returns>加载了记忆的ChatHistory</returns>
        public async Task<ChatHistory> LoadConversationMemoryAsync(ChatHistory chatHistory, string userId, string chatGuid, int maxTurns = DefaultMemoryTurns)
        {
            try
            {
                _logger.LogInformation("[ConversationMemoryService] 开始为用户 {userId} 会话 {chatGuid} 加载会话记忆，最大轮数: {maxTurns}", userId, chatGuid, maxTurns);

                // 首先尝试从缓存获取
                var cachedMessages = _memoryCache.GetConversationMemoryCache(userId, chatGuid);
                List<ChatMessageDto> memoryMessages;

                if (cachedMessages != null && cachedMessages.Any())
                {
                    _logger.LogInformation("[ConversationMemoryService] 从缓存获取到 {count} 条历史消息", cachedMessages.Count);
                    memoryMessages = cachedMessages;
                }
                else
                {
                    // 从数据库获取最近的对话记录
                    _logger.LogInformation("[ConversationMemoryService] 缓存未命中，从数据库获取历史消息");
                    memoryMessages = await GetRecentMessagesFromDatabaseAsync(userId, chatGuid, maxTurns * 4); // 获取更多消息以确保有足够的轮次（考虑工具调用）

                    // 更新缓存
                    if (memoryMessages.Any())
                    {
                        _memoryCache.SetConversationMemoryCache(userId, chatGuid, memoryMessages, CacheExpirationMinutes);
                        _logger.LogInformation("[ConversationMemoryService] 已将 {count} 条消息缓存", memoryMessages.Count);
                    }
                }

                // 将历史消息添加到ChatHistory中（按时间顺序，保持最近的对话）
                var recentMessages = GetRecentConversationTurns(memoryMessages, maxTurns);

                // 验证和修复工具调用配对
                var (validatedMessages, toolCallIdMapping) = recentMessages.ValidateAndFixToolCallPairs(_logger);

                // 保存ID映射供后续使用
                _toolCallIdMapping = toolCallIdMapping;

                foreach (var message in validatedMessages)
                {
                    switch (message.Role)
                    {
                        case ChatRoleConstant.User:
                            chatHistory.AddUserMessage(message.Content);
                            break;
                        case ChatRoleConstant.Assistant:
                            // 检查是否是工具调用消息
                            if (message.Content.StartsWith("[TOOL_CALL]"))
                            {
                                // 重建工具调用消息
                                var toolCallMessage = ReconstructToolCallMessage(message.Content);
                                if (toolCallMessage != null)
                                {
                                    chatHistory.Add(toolCallMessage);
                                    _logger.LogDebug("[ConversationMemoryService] 重建工具调用消息: {content}", message.Content);
                                }
                                continue;
                            }
                            chatHistory.AddAssistantMessage(message.Content);
                            break;
                        case ChatRoleConstant.Tool:
                            // 检查前面是否有Assistant消息包含tool_calls
                            bool hasPrecedingToolCall = false;
                            if (chatHistory.Count > 0)
                            {
                                var lastMessage = chatHistory[chatHistory.Count - 1];
                                if (lastMessage.Role == AuthorRole.Assistant && lastMessage.Items != null)
                                {
                                    hasPrecedingToolCall = lastMessage.Items.Any(item => item is FunctionCallContent);
                                }
                            }

                            if (!hasPrecedingToolCall)
                            {
                                _logger.LogWarning("[ConversationMemoryService] 跳过孤立的Tool消息（没有对应的tool_calls）: {content}",
                                    message.Content.Length > 100 ? message.Content.Substring(0, 100) + "..." : message.Content);
                                continue;
                            }

                            // 检查是否是格式化的工具返回消息
                            if (message.Content.StartsWith("[TOOL_RESULT:"))
                            {
                                // 重建工具返回消息
                                var toolResultMessage = ReconstructToolResultMessage(message.Content);
                                if (toolResultMessage != null)
                                {
                                    chatHistory.Add(toolResultMessage);
                                    _logger.LogDebug("[ConversationMemoryService] 重建工具返回消息: {content}", message.Content);
                                }
                            }
                            else
                            {
                                // 直接添加Tool消息的content（只有在有对应tool_calls时）
                                var toolMessage = new ChatMessageContent(AuthorRole.Tool, message.Content);
                                chatHistory.Add(toolMessage);
                                _logger.LogDebug("[ConversationMemoryService] 添加Tool消息content: {content}", message.Content);
                            }
                            continue;
                        default:
                            _logger.LogWarning("[ConversationMemoryService] 未知的消息角色: {role}，跳过该消息", message.Role);
                            continue;
                    }
                }

                _logger.LogInformation("[ConversationMemoryService] 成功为用户 {userId} 会话 {chatGuid} 加载了 {count} 条历史消息到ChatHistory", userId, chatGuid, recentMessages.Count);
                return chatHistory;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 为用户 {userId} 会话 {chatGuid} 加载会话记忆时发生错误", userId, chatGuid);
                return chatHistory; // 发生错误时返回原始的ChatHistory
            }
        }

        /// <summary>
        /// 保存新的会话消息到记忆中（基于用户ID和会话ID）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="userMessage">用户消息</param>
        /// <param name="assistantMessage">助手回复</param>
        /// <param name="tenantCode">租户代码</param>
        /// <param name="tenantName">租户名称</param>
        /// <param name="batchGuid">批次GUID</param>
        /// <param name="userName">用户名</param>
        public async Task SaveConversationMemoryAsync(string userId, string chatGuid, string userMessage, string assistantMessage, string tenantCode = null, string tenantName = null, Guid? batchGuid = null, string userName = null)
        {
            try
            {
                var actualBatchGuid = batchGuid ?? Guid.NewGuid();
                _logger.LogInformation("[ConversationMemoryService] 开始为用户 {userId} 会话 {chatGuid} 保存新的会话记忆，BatchGUID: {batchGuid}", userId, chatGuid, actualBatchGuid);

                // 获取当前缓存的消息
                var cachedMessages = _memoryCache.GetConversationMemoryCache(userId, chatGuid) ?? new List<ChatMessageDto>();

                // 添加新的用户消息
                cachedMessages.Add(new ChatMessageDto
                {
                    Role = ChatRoleConstant.User,
                    Content = userMessage,
                    Answer = assistantMessage, // 将助手回复保存到用户消息的Answer字段
                    UserGUID = userId,
                    UserName = userName ?? string.Empty, // 设置用户名
                    ChatGUID = Guid.Parse(chatGuid),
                    Index = cachedMessages.Count,
                    BatchGUID = actualBatchGuid,
                    IsHidden = 0 // 用户消息不隐藏
                });

                // 添加新的助手回复
                cachedMessages.Add(new ChatMessageDto
                {
                    Role = ChatRoleConstant.Assistant,
                    Content = assistantMessage,
                    UserGUID = userId,
                    UserName = userName ?? string.Empty, // 设置用户名
                    ChatGUID = Guid.Parse(chatGuid),
                    Index = cachedMessages.Count,
                    BatchGUID = actualBatchGuid,
                    IsHidden = 0 // 助手回复不隐藏
                });

                // 保持最近的消息数量在合理范围内（最多保留20轮对话，即40条消息）
                if (cachedMessages.Count > 40)
                {
                    cachedMessages = cachedMessages.Skip(cachedMessages.Count - 40).ToList();
                    // 重新设置索引
                    for (int i = 0; i < cachedMessages.Count; i++)
                    {
                        cachedMessages[i].Index = i;
                    }
                }

                // 更新缓存
                _memoryCache.SetConversationMemoryCache(userId, chatGuid, cachedMessages, CacheExpirationMinutes);

                // 保存新消息到数据库
                var newMessagesToSave = new List<ChatMessageDto>
                {
                    new ChatMessageDto
                    {
                        Role = ChatRoleConstant.User,
                        Content = userMessage,
                        Answer = assistantMessage, // 将助手回复保存到用户消息的Answer字段
                        UserGUID = userId,
                        UserName = userName ?? string.Empty, // 设置用户名
                        ChatGUID = Guid.Parse(chatGuid),
                        Index = cachedMessages.Count - 2, // 用户消息的索引
                        TenantCode = tenantCode ?? string.Empty,
                        TenantName = tenantName ?? string.Empty,
                        BatchGUID = actualBatchGuid,
                        IsHidden = 0 // 用户消息不隐藏
                    },
                    new ChatMessageDto
                    {
                        Role = ChatRoleConstant.Assistant,
                        Content = assistantMessage,
                        UserGUID = userId,
                        UserName = userName ?? string.Empty, // 设置用户名
                        ChatGUID = Guid.Parse(chatGuid),
                        Index = cachedMessages.Count - 1, // 助手消息的索引
                        TenantCode = tenantCode ?? string.Empty,
                        TenantName = tenantName ?? string.Empty,
                        BatchGUID = actualBatchGuid,
                        IsHidden = 0 // 助手回复不隐藏
                    }
                };

                await SaveMessagesToDatabaseAsync(newMessagesToSave);

                _logger.LogInformation("[ConversationMemoryService] 成功为用户 {userId} 会话 {chatGuid} 保存会话记忆到缓存和数据库，当前缓存消息数: {count}，BatchGUID: {batchGuid}", userId, chatGuid, cachedMessages.Count, actualBatchGuid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 为用户 {userId} 会话 {chatGuid} 保存会话记忆时发生错误", userId, chatGuid);
            }
        }

        /// <summary>
        /// 保存完整的对话消息列表到记忆中（支持工具调用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="messages">要保存的消息列表</param>
        /// <param name="tenantCode">租户代码</param>
        /// <param name="tenantName">租户名称</param>
        /// <param name="batchGuid">批次GUID</param>
        /// <param name="userName">用户名</param>
        public async Task SaveConversationMessagesAsync(string userId, string chatGuid, List<ChatMessageDto> messages, string tenantCode = null, string tenantName = null, Guid? batchGuid = null, string userName = null)
        {
            await SaveConversationMessagesAsync(userId, chatGuid, messages, tenantCode, tenantName, batchGuid, userName, null);
        }

        /// <summary>
        /// 保存完整的对话消息列表到记忆中（支持工具调用和Answer字段）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="messages">要保存的消息列表</param>
        /// <param name="tenantCode">租户代码</param>
        /// <param name="tenantName">租户名称</param>
        /// <param name="batchGuid">批次GUID</param>
        /// <param name="userName">用户名</param>
        /// <param name="answer">大模型回复，将保存到用户消息的Answer字段</param>
        public async Task SaveConversationMessagesAsync(string userId, string chatGuid, List<ChatMessageDto> messages, string tenantCode = null, string tenantName = null, Guid? batchGuid = null, string userName = null, string answer = null)
        {
            try
            {
                var actualBatchGuid = batchGuid ?? Guid.NewGuid();
                _logger.LogInformation("[ConversationMemoryService] 开始为用户 {userId} 会话 {chatGuid} 保存 {count} 条对话消息，BatchGUID: {batchGuid}", userId, chatGuid, messages.Count, actualBatchGuid);

                // 获取当前缓存的消息
                var cachedMessages = _memoryCache.GetConversationMemoryCache(userId, chatGuid) ?? new List<ChatMessageDto>();

                // 添加新的消息
                foreach (var message in messages)
                {
                    // 确保消息有正确的用户ID和会话ID
                    message.UserGUID = userId;
                    message.ChatGUID = Guid.Parse(chatGuid);
                    message.Index = cachedMessages.Count;

                    // 设置用户名（如果消息本身没有设置或为空）
                    if (string.IsNullOrEmpty(message.UserName) && !string.IsNullOrEmpty(userName))
                        message.UserName = userName;

                    // 设置租户信息
                    if (!string.IsNullOrEmpty(tenantCode))
                        message.TenantCode = tenantCode;
                    if (!string.IsNullOrEmpty(tenantName))
                        message.TenantName = tenantName;

                    // 设置BatchGUID（如果消息本身没有设置或为空）
                    if (message.BatchGUID == Guid.Empty)
                        message.BatchGUID = actualBatchGuid;

                    cachedMessages.Add(message);
                }

                // 保持最近的消息数量在合理范围内（最多保留30轮对话，考虑工具调用可能更多消息）
                if (cachedMessages.Count > 90) // 估算每轮对话最多3条消息
                {
                    cachedMessages = cachedMessages.Skip(cachedMessages.Count - 90).ToList();
                    // 重新设置索引
                    for (int i = 0; i < cachedMessages.Count; i++)
                    {
                        cachedMessages[i].Index = i;
                    }
                }

                // 更新缓存
                _memoryCache.SetConversationMemoryCache(userId, chatGuid, cachedMessages, CacheExpirationMinutes);

                // 如果提供了answer参数，将其设置到用户消息的Answer字段
                if (!string.IsNullOrEmpty(answer))
                {
                    var userMessage = messages.FirstOrDefault(m => m.Role == ChatRoleConstant.User);
                    if (userMessage != null)
                    {
                        userMessage.Answer = answer;
                        _logger.LogInformation("[ConversationMemoryService] 已将Answer字段设置到用户消息，Answer长度: {length}", answer.Length);
                    }
                }

                // 保存新消息到数据库
                await SaveMessagesToDatabaseAsync(messages);

                _logger.LogInformation("[ConversationMemoryService] 成功为用户 {userId} 会话 {chatGuid} 保存对话消息到缓存和数据库，当前缓存消息数: {count}，BatchGUID: {batchGuid}", userId, chatGuid, cachedMessages.Count, actualBatchGuid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 为用户 {userId} 会话 {chatGuid} 保存对话消息时发生错误", userId, chatGuid);
            }
        }

        /// <summary>
        /// 为ChatHistory加载用户的历史会话记忆（兼容旧版本，不推荐使用）
        /// </summary>
        /// <param name="chatHistory">要加载记忆的ChatHistory</param>
        /// <param name="userId">用户ID</param>
        /// <param name="maxTurns">最大记忆轮数</param>
        /// <returns>加载了记忆的ChatHistory</returns>
        [Obsolete("建议使用基于ChatGuid的LoadConversationMemoryAsync方法")]
        public async Task<ChatHistory> LoadConversationMemoryAsync(ChatHistory chatHistory, string userId, int maxTurns = DefaultMemoryTurns)
        {
            _logger.LogWarning("[ConversationMemoryService] 使用了已过时的LoadConversationMemoryAsync方法，建议升级到基于ChatGuid的版本");

            // 为了向后兼容，使用空的ChatGuid调用新方法
            // 注意：这种情况下无法实现会话级别隔离
            return await LoadConversationMemoryAsync(chatHistory, userId, Guid.Empty.ToString(), maxTurns);
        }

        /// <summary>
        /// 保存新的会话消息到记忆中（兼容旧版本，不推荐使用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userMessage">用户消息</param>
        /// <param name="assistantMessage">助手回复</param>
        [Obsolete("建议使用基于ChatGuid的SaveConversationMemoryAsync方法")]
        public async Task SaveConversationMemoryAsync(string userId, string userMessage, string assistantMessage)
        {
            _logger.LogWarning("[ConversationMemoryService] 使用了已过时的SaveConversationMemoryAsync方法，建议升级到基于ChatGuid的版本");

            // 为了向后兼容，使用空的ChatGuid调用新方法
            // 注意：这种情况下无法实现会话级别隔离，BatchGUID将自动生成
            await SaveConversationMemoryAsync(userId, Guid.Empty.ToString(), userMessage, assistantMessage, null, null, null, null);
        }

        /// <summary>
        /// 清除用户指定会话的记忆（仅清除缓存）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        public void ClearConversationMemory(string userId, string chatGuid)
        {
            try
            {
                _memoryCache.ClearConversationMemoryCache(userId, chatGuid);
                _logger.LogInformation("[ConversationMemoryService] 成功清除用户 {userId} 会话 {chatGuid} 的会话记忆缓存", userId, chatGuid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 清除用户 {userId} 会话 {chatGuid} 会话记忆缓存时发生错误", userId, chatGuid);
            }
        }

        /// <summary>
        /// 清除用户的会话记忆（仅清除缓存，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        [Obsolete("建议使用基于ChatGuid的ClearConversationMemory方法")]
        public void ClearConversationMemory(string userId)
        {
            _logger.LogWarning("[ConversationMemoryService] 使用了已过时的ClearConversationMemory方法，建议升级到基于ChatGuid的版本");

            try
            {
                _memoryCache.ClearConversationMemoryCache(userId);
                _logger.LogInformation("[ConversationMemoryService] 成功清除用户 {userId} 的会话记忆缓存（所有会话）", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 清除用户 {userId} 会话记忆缓存时发生错误", userId);
            }
        }

        /// <summary>
        /// 彻底清除用户指定会话的记忆（包括缓存和数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <returns></returns>
        public async Task ClearConversationMemoryCompletelyAsync(string userId, string chatGuid)
        {
            try
            {
                // 1. 清除缓存
                _memoryCache.ClearConversationMemoryCache(userId, chatGuid);
                _logger.LogInformation("[ConversationMemoryService] 已清除用户 {userId} 会话 {chatGuid} 的会话记忆缓存", userId, chatGuid);

                // 2. 从数据库删除历史消息
                await DeleteUserChatMessagesFromDatabaseAsync(userId, chatGuid);
                _logger.LogInformation("[ConversationMemoryService] 已从数据库删除用户 {userId} 会话 {chatGuid} 的历史消息", userId, chatGuid);

                _logger.LogInformation("[ConversationMemoryService] 成功彻底清除用户 {userId} 会话 {chatGuid} 的所有会话记忆", userId, chatGuid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 彻底清除用户 {userId} 会话 {chatGuid} 会话记忆时发生错误", userId, chatGuid);
                throw;
            }
        }

        /// <summary>
        /// 彻底清除用户的所有会话记忆（包括缓存和数据库，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [Obsolete("建议使用基于ChatGuid的ClearConversationMemoryCompletelyAsync方法")]
        public async Task ClearConversationMemoryCompletelyAsync(string userId)
        {
            _logger.LogWarning("[ConversationMemoryService] 使用了已过时的ClearConversationMemoryCompletelyAsync方法，建议升级到基于ChatGuid的版本");

            try
            {
                // 1. 清除缓存（使用旧方法）
                _memoryCache.ClearConversationMemoryCache(userId);
                _logger.LogInformation("[ConversationMemoryService] 已清除用户 {userId} 的会话记忆缓存（所有会话）", userId);

                // 2. 从数据库删除历史消息（所有会话）
                await DeleteUserMessagesFromDatabaseAsync(userId);
                _logger.LogInformation("[ConversationMemoryService] 已从数据库删除用户 {userId} 的历史消息（所有会话）", userId);

                _logger.LogInformation("[ConversationMemoryService] 成功彻底清除用户 {userId} 的所有会话记忆", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 彻底清除用户 {userId} 会话记忆时发生错误", userId);
                throw;
            }
        }

        /// <summary>
        /// 从数据库删除用户指定会话的历史消息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <returns></returns>
        private async Task DeleteUserChatMessagesFromDatabaseAsync(string userId, string chatGuid)
        {
            try
            {
                // 获取用户指定会话的所有消息
                var chatGuidParsed = Guid.Parse(chatGuid);
                var userMessages = await _chatMessageRepository.GetListAsync(x =>
                    x.UserGUID == userId && x.ChatGUID == chatGuidParsed);

                if (userMessages != null && userMessages.Count > 0)
                {
                    // 逐条删除消息
                    foreach (var message in userMessages)
                    {
                        await _chatMessageRepository.DeleteAsync(message);
                    }

                    _logger.LogInformation("[ConversationMemoryService] 从数据库删除了用户 {userId} 会话 {chatGuid} 的 {count} 条历史消息", userId, chatGuid, userMessages.Count);
                }
                else
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 会话 {chatGuid} 在数据库中没有历史消息需要删除", userId, chatGuid);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 从数据库删除用户 {userId} 会话 {chatGuid} 历史消息时发生错误", userId, chatGuid);
                throw;
            }
        }

        /// <summary>
        /// 从数据库删除用户的历史消息（所有会话，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [Obsolete("建议使用基于ChatGuid的DeleteUserChatMessagesFromDatabaseAsync方法")]
        private async Task DeleteUserMessagesFromDatabaseAsync(string userId)
        {
            try
            {
                // 获取用户的所有消息
                var userMessages = await _chatMessageRepository.GetListAsync(x => x.UserGUID == userId);

                if (userMessages != null && userMessages.Count > 0)
                {
                    // 逐条删除消息
                    foreach (var message in userMessages)
                    {
                        await _chatMessageRepository.DeleteAsync(message);
                    }

                    _logger.LogInformation("[ConversationMemoryService] 从数据库删除了用户 {userId} 的 {count} 条历史消息", userId, userMessages.Count);
                }
                else
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 在数据库中没有历史消息需要删除", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 从数据库删除用户 {userId} 历史消息时发生错误", userId);
                throw;
            }
        }

        /// <summary>
        /// 从数据库获取用户最近的消息（基于用户ID和会话ID）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="maxMessages">最大消息数</param>
        /// <returns>消息列表</returns>
        private async Task<List<ChatMessageDto>> GetRecentMessagesFromDatabaseAsync(string userId, string chatGuid, int maxMessages)
        {
            try
            {
                // 从数据库获取用户指定会话的所有消息，包括用户、助手和工具消息，过滤掉系统消息和隐藏消息
                var chatGuidParsed = Guid.Parse(chatGuid);
                var messages = await _chatMessageRepository.GetListAsync(x =>
                    x.UserGUID == userId &&
                    x.ChatGUID == chatGuidParsed &&
                    (x.Role == ChatRoleConstant.User || x.Role == ChatRoleConstant.Assistant || x.Role == ChatRoleConstant.Tool) &&
                    x.IsHidden == 0); // 过滤掉隐藏消息

                _logger.LogInformation("[ConversationMemoryService] 从数据库获取到用户 {userId} 会话 {chatGuid} 的 {count} 条对话消息（包含工具调用）", userId, chatGuid, messages.Count);

                // 按时间倒序排列并取最近的消息
                var recentMessages = messages
                    .OrderByDescending(m => m.CreatedTime)
                    .Take(maxMessages)
                    .OrderBy(m => m.CreatedTime) // 重新按时间正序排列（最早的在前面）
                    .ToList();

                // 转换为DTO
                return recentMessages.Select(m => new ChatMessageDto
                {
                    ChatMessageGUID = m.ChatMessageGUID,
                    ChatGUID = m.ChatGUID,
                    Content = m.Content,
                    Role = m.Role,
                    UserGUID = m.UserGUID,
                    BatchGUID = m.BatchGUID,
                    Index = 0 // 索引将在后续重新设置
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 从数据库获取用户 {userId} 历史消息时发生错误", userId);
                return new List<ChatMessageDto>();
            }
        }

        /// <summary>
        /// 从数据库获取用户最近的消息（兼容旧版本，不推荐使用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="maxMessages">最大消息数</param>
        /// <returns>消息列表</returns>
        [Obsolete("建议使用基于ChatGuid的GetRecentMessagesFromDatabaseAsync方法")]
        private async Task<List<ChatMessageDto>> GetRecentMessagesFromDatabaseAsync(string userId, int maxMessages)
        {
            _logger.LogWarning("[ConversationMemoryService] 使用了已过时的GetRecentMessagesFromDatabaseAsync方法，建议升级到基于ChatGuid的版本");

            // 为了向后兼容，获取用户所有会话的消息（不按会话隔离）
            try
            {
                var messages = await _chatMessageRepository.GetListAsync(x =>
                    x.UserGUID == userId &&
                    (x.Role == ChatRoleConstant.User || x.Role == ChatRoleConstant.Assistant || x.Role == ChatRoleConstant.Tool) &&
                    x.IsHidden == 0);

                _logger.LogInformation("[ConversationMemoryService] 从数据库获取到用户 {userId} 的 {count} 条对话消息（包含工具调用，跨所有会话）", userId, messages.Count);

                var recentMessages = messages
                    .OrderByDescending(m => m.CreatedTime)
                    .Take(maxMessages)
                    .OrderBy(m => m.CreatedTime)
                    .ToList();

                return recentMessages.Select(m => new ChatMessageDto
                {
                    ChatMessageGUID = m.ChatMessageGUID,
                    ChatGUID = m.ChatGUID,
                    Content = m.Content,
                    Role = m.Role,
                    UserGUID = m.UserGUID,
                    BatchGUID = m.BatchGUID,
                    Index = 0
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 从数据库获取用户 {userId} 历史消息时发生错误", userId);
                return new List<ChatMessageDto>();
            }
        }

        /// <summary>
        /// 从消息列表中获取最近的对话轮次（支持工具调用）
        /// </summary>
        /// <param name="messages">消息列表</param>
        /// <param name="maxTurns">最大轮数</param>
        /// <returns>最近的消息列表</returns>
        private List<ChatMessageDto> GetRecentConversationTurns(List<ChatMessageDto> messages, int maxTurns)
        {
            if (!messages.Any()) return new List<ChatMessageDto>();

            // 按时间排序确保消息顺序正确
            var sortedMessages = messages.OrderBy(m => m.CreatedTime).ToList();

            // 如果消息总数较少，直接返回所有消息
            if (sortedMessages.Count <= maxTurns * 4) // 估算每轮对话最多4条消息（用户+工具调用+工具返回+助手回复）
            {
                return sortedMessages;
            }

            // 从后往前查找完整的对话轮次
            var recentMessages = new List<ChatMessageDto>();
            var turnCount = 0;

            // 从最新的消息开始往前遍历
            for (int i = sortedMessages.Count - 1; i >= 0 && turnCount < maxTurns; i--)
            {
                var message = sortedMessages[i];
                recentMessages.Insert(0, message); // 插入到开头保持时间顺序

                // 如果遇到用户消息，说明一轮对话开始
                if (message.Role == ChatRoleConstant.User)
                {
                    turnCount++;
                }
            }

            _logger.LogDebug("[ConversationMemoryService] 从 {totalCount} 条消息中提取了 {recentCount} 条消息，包含 {turnCount} 轮对话",
                sortedMessages.Count, recentMessages.Count, turnCount);

            return recentMessages;
        }

        /// <summary>
        /// 清除用户指定会话指定时间之前的会话记忆
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="beforeTime">时间点</param>
        /// <returns></returns>
        public async Task ClearConversationMemoryBeforeAsync(string userId, string chatGuid, DateTime beforeTime)
        {
            try
            {
                // 1. 清除缓存
                _memoryCache.ClearConversationMemoryCache(userId, chatGuid);
                _logger.LogInformation("[ConversationMemoryService] 已清除用户 {userId} 会话 {chatGuid} 的会话记忆缓存", userId, chatGuid);

                // 2. 从数据库删除指定时间之前的历史消息
                await DeleteUserChatMessagesBeforeTimeAsync(userId, chatGuid, beforeTime);
                _logger.LogInformation("[ConversationMemoryService] 已从数据库删除用户 {userId} 会话 {chatGuid} 在 {beforeTime} 之前的历史消息", userId, chatGuid, beforeTime);

                _logger.LogInformation("[ConversationMemoryService] 成功清除用户 {userId} 会话 {chatGuid} 在 {beforeTime} 之前的会话记忆", userId, chatGuid, beforeTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 清除用户 {userId} 会话 {chatGuid} 在 {beforeTime} 之前的会话记忆时发生错误", userId, chatGuid, beforeTime);
                throw;
            }
        }

        /// <summary>
        /// 清除用户指定时间之前的会话记忆（所有会话，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="beforeTime">时间点</param>
        /// <returns></returns>
        [Obsolete("建议使用基于ChatGuid的ClearConversationMemoryBeforeAsync方法")]
        public async Task ClearConversationMemoryBeforeAsync(string userId, DateTime beforeTime)
        {
            _logger.LogWarning("[ConversationMemoryService] 使用了已过时的ClearConversationMemoryBeforeAsync方法，建议升级到基于ChatGuid的版本");

            try
            {
                // 1. 清除缓存（使用旧方法）
                _memoryCache.ClearConversationMemoryCache(userId);
                _logger.LogInformation("[ConversationMemoryService] 已清除用户 {userId} 的会话记忆缓存（所有会话）", userId);

                // 2. 从数据库删除指定时间之前的历史消息（所有会话）
                await DeleteUserMessagesBeforeTimeAsync(userId, beforeTime);
                _logger.LogInformation("[ConversationMemoryService] 已从数据库删除用户 {userId} 在 {beforeTime} 之前的历史消息（所有会话）", userId, beforeTime);

                _logger.LogInformation("[ConversationMemoryService] 成功清除用户 {userId} 在 {beforeTime} 之前的会话记忆", userId, beforeTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 清除用户 {userId} 在 {beforeTime} 之前的会话记忆时发生错误", userId, beforeTime);
                throw;
            }
        }

        /// <summary>
        /// 只保留用户指定会话最近N轮对话
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="keepTurns">保留的轮数</param>
        /// <returns></returns>
        public async Task KeepRecentConversationsAsync(string userId, string chatGuid, int keepTurns)
        {
            try
            {
                // 1. 清除缓存
                _memoryCache.ClearConversationMemoryCache(userId, chatGuid);
                _logger.LogInformation("[ConversationMemoryService] 已清除用户 {userId} 会话 {chatGuid} 的会话记忆缓存", userId, chatGuid);

                // 2. 从数据库删除旧的历史消息，只保留最近的N轮对话
                await KeepRecentMessagesInDatabaseAsync(userId, chatGuid, keepTurns);
                _logger.LogInformation("[ConversationMemoryService] 已为用户 {userId} 会话 {chatGuid} 保留最近 {keepTurns} 轮对话", userId, chatGuid, keepTurns);

                _logger.LogInformation("[ConversationMemoryService] 成功为用户 {userId} 会话 {chatGuid} 保留最近 {keepTurns} 轮对话，其余历史记录已清除", userId, chatGuid, keepTurns);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 为用户 {userId} 会话 {chatGuid} 保留最近 {keepTurns} 轮对话时发生错误", userId, chatGuid, keepTurns);
                throw;
            }
        }

        /// <summary>
        /// 只保留用户最近N轮对话（所有会话，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="keepTurns">保留的轮数</param>
        /// <returns></returns>
        [Obsolete("建议使用基于ChatGuid的KeepRecentConversationsAsync方法")]
        public async Task KeepRecentConversationsAsync(string userId, int keepTurns)
        {
            _logger.LogWarning("[ConversationMemoryService] 使用了已过时的KeepRecentConversationsAsync方法，建议升级到基于ChatGuid的版本");

            try
            {
                // 1. 清除缓存（使用旧方法）
                _memoryCache.ClearConversationMemoryCache(userId);
                _logger.LogInformation("[ConversationMemoryService] 已清除用户 {userId} 的会话记忆缓存（所有会话）", userId);

                // 2. 从数据库删除旧的历史消息，只保留最近的N轮对话（所有会话）
                await KeepRecentMessagesInDatabaseAsync(userId, keepTurns);
                _logger.LogInformation("[ConversationMemoryService] 已为用户 {userId} 保留最近 {keepTurns} 轮对话（所有会话）", userId, keepTurns);

                _logger.LogInformation("[ConversationMemoryService] 成功为用户 {userId} 保留最近 {keepTurns} 轮对话，其余历史记录已清除", userId, keepTurns);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 为用户 {userId} 保留最近 {keepTurns} 轮对话时发生错误", userId, keepTurns);
                throw;
            }
        }

        /// <summary>
        /// 从数据库删除用户指定会话指定时间之前的历史消息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="beforeTime">时间点</param>
        /// <returns></returns>
        private async Task DeleteUserChatMessagesBeforeTimeAsync(string userId, string chatGuid, DateTime beforeTime)
        {
            try
            {
                // 获取用户指定会话指定时间之前的消息
                var chatGuidParsed = Guid.Parse(chatGuid);
                var oldMessages = await _chatMessageRepository.GetListAsync(x =>
                    x.UserGUID == userId && x.ChatGUID == chatGuidParsed && x.CreatedTime < beforeTime);

                if (oldMessages != null && oldMessages.Count > 0)
                {
                    // 逐条删除消息
                    foreach (var message in oldMessages)
                    {
                        await _chatMessageRepository.DeleteAsync(message);
                    }

                    _logger.LogInformation("[ConversationMemoryService] 从数据库删除了用户 {userId} 会话 {chatGuid} 在 {beforeTime} 之前的 {count} 条历史消息", userId, chatGuid, beforeTime, oldMessages.Count);
                }
                else
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 会话 {chatGuid} 在 {beforeTime} 之前没有历史消息需要删除", userId, chatGuid, beforeTime);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 从数据库删除用户 {userId} 会话 {chatGuid} 在 {beforeTime} 之前的历史消息时发生错误", userId, chatGuid, beforeTime);
                throw;
            }
        }

        /// <summary>
        /// 从数据库删除用户指定时间之前的历史消息（所有会话，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="beforeTime">时间点</param>
        /// <returns></returns>
        [Obsolete("建议使用基于ChatGuid的DeleteUserChatMessagesBeforeTimeAsync方法")]
        private async Task DeleteUserMessagesBeforeTimeAsync(string userId, DateTime beforeTime)
        {
            try
            {
                // 获取用户指定时间之前的消息
                var oldMessages = await _chatMessageRepository.GetListAsync(x =>
                    x.UserGUID == userId && x.CreatedTime < beforeTime);

                if (oldMessages != null && oldMessages.Count > 0)
                {
                    // 逐条删除消息
                    foreach (var message in oldMessages)
                    {
                        await _chatMessageRepository.DeleteAsync(message);
                    }

                    _logger.LogInformation("[ConversationMemoryService] 从数据库删除了用户 {userId} 在 {beforeTime} 之前的 {count} 条历史消息", userId, beforeTime, oldMessages.Count);
                }
                else
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 在 {beforeTime} 之前没有历史消息需要删除", userId, beforeTime);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 从数据库删除用户 {userId} 在 {beforeTime} 之前的历史消息时发生错误", userId, beforeTime);
                throw;
            }
        }

        /// <summary>
        /// 在数据库中只保留用户指定会话最近的N轮对话
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="keepTurns">保留的轮数</param>
        /// <returns></returns>
        private async Task KeepRecentMessagesInDatabaseAsync(string userId, string chatGuid, int keepTurns)
        {
            try
            {
                // 获取用户指定会话的所有消息，按时间倒序排列
                var chatGuidParsed = Guid.Parse(chatGuid);
                var allMessages = await _chatMessageRepository.GetListAsync(x =>
                    x.UserGUID == userId && x.ChatGUID == chatGuidParsed);
                var sortedMessages = allMessages.OrderByDescending(m => m.CreatedTime).ToList();

                if (sortedMessages.Count <= keepTurns * 2) // 每轮对话包含用户消息和助手回复
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 会话 {chatGuid} 的消息数量 {count} 不超过保留数量 {keepCount}，无需删除", userId, chatGuid, sortedMessages.Count, keepTurns * 2);
                    return;
                }

                // 计算需要删除的消息（保留最近的N轮对话，即最近的N*2条消息）
                var messagesToDelete = sortedMessages.Skip(keepTurns * 2).ToList();

                if (messagesToDelete.Count > 0)
                {
                    // 逐条删除旧消息
                    foreach (var message in messagesToDelete)
                    {
                        await _chatMessageRepository.DeleteAsync(message);
                    }

                    _logger.LogInformation("[ConversationMemoryService] 为用户 {userId} 会话 {chatGuid} 保留最近 {keepTurns} 轮对话，删除了 {deletedCount} 条旧消息", userId, chatGuid, keepTurns, messagesToDelete.Count);
                }
                else
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 会话 {chatGuid} 没有需要删除的旧消息", userId, chatGuid);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 为用户 {userId} 会话 {chatGuid} 保留最近 {keepTurns} 轮对话时发生错误", userId, chatGuid, keepTurns);
                throw;
            }
        }

        /// <summary>
        /// 在数据库中只保留用户最近的N轮对话（所有会话，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="keepTurns">保留的轮数</param>
        /// <returns></returns>
        [Obsolete("建议使用基于ChatGuid的KeepRecentMessagesInDatabaseAsync方法")]
        private async Task KeepRecentMessagesInDatabaseAsync(string userId, int keepTurns)
        {
            try
            {
                // 获取用户的所有消息，按时间倒序排列
                var allMessages = await _chatMessageRepository.GetListAsync(x => x.UserGUID == userId);
                var sortedMessages = allMessages.OrderByDescending(m => m.CreatedTime).ToList();

                if (sortedMessages.Count <= keepTurns * 2) // 每轮对话包含用户消息和助手回复
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 的消息数量 {count} 不超过保留数量 {keepCount}，无需删除", userId, sortedMessages.Count, keepTurns * 2);
                    return;
                }

                // 计算需要删除的消息（保留最近的N轮对话，即最近的N*2条消息）
                var messagesToDelete = sortedMessages.Skip(keepTurns * 2).ToList();

                if (messagesToDelete.Count > 0)
                {
                    // 逐条删除旧消息
                    foreach (var message in messagesToDelete)
                    {
                        await _chatMessageRepository.DeleteAsync(message);
                    }

                    _logger.LogInformation("[ConversationMemoryService] 为用户 {userId} 保留最近 {keepTurns} 轮对话，删除了 {deletedCount} 条旧消息", userId, keepTurns, messagesToDelete.Count);
                }
                else
                {
                    _logger.LogInformation("[ConversationMemoryService] 用户 {userId} 没有需要删除的旧消息", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 为用户 {userId} 保留最近 {keepTurns} 轮对话时发生错误", userId, keepTurns);
                throw;
            }
        }

        /// <summary>
        /// 保存消息到数据库
        /// </summary>
        /// <param name="messages">要保存的消息列表</param>
        /// <returns></returns>
        private async Task SaveMessagesToDatabaseAsync(List<ChatMessageDto> messages)
        {
            try
            {
                foreach (var message in messages)
                {
                    var entity = new ChatMessageEntity
                    {
                        ChatMessageGUID = Guid.NewGuid(),
                        ChatGUID = message.ChatGUID,
                        NodeGUID = message.NodeGUID,
                        Content = message.Content,
                        Role = message.Role,
                        Index = message.Index,
                        UserGUID = message.UserGUID,
                        UserName = message.UserName ?? "",
                        CustomerId = message.CustomerId ?? "",
                        CustomerName = message.CustomerName ?? "",
                        TenantCode = message.TenantCode ?? "",
                        TenantName = message.TenantName ?? "",
                        IsHidden = message.IsHidden,
                        BatchGUID = message.BatchGUID,
                        Error = message.Error ?? "",
                        Answer = message.Answer ?? "",
                        PlanGUID = message.PlanGUID ?? null, // 允许为null，匹配数据库DEFAULT NULL
                        CreatedTime = DateTime.Now,
                        ModifiedTime = DateTime.Now
                    };

                    await _chatMessageRepository.InsertAsync(entity);
                }

                _logger.LogInformation("[ConversationMemoryService] 成功保存 {count} 条消息到数据库", messages.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 保存消息到数据库时发生错误");
                throw;
            }
        }







        /// <summary>
        /// 重建工具调用消息
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <returns>ChatMessageContent或null</returns>
        private ChatMessageContent ReconstructToolCallMessage(string content)
        {
            try
            {
                if (!content.StartsWith("[TOOL_CALL] "))
                {
                    return null;
                }

                var jsonContent = content.Substring("[TOOL_CALL] ".Length);
                var toolCallData = JsonSerializer.Deserialize<JsonElement>(jsonContent);

                var functionName = toolCallData.GetProperty("function_name").GetString();
                var id = toolCallData.TryGetProperty("id", out var idElement) ? idElement.GetString() : Guid.NewGuid().ToString();

                // 解析插件名和函数名
                var parts = functionName.Split('.');
                var pluginName = parts.Length > 1 ? parts[0] : "";
                var funcName = parts.Length > 1 ? parts[1] : functionName;

                // 解析参数
                var arguments = new KernelArguments();
                if (toolCallData.TryGetProperty("arguments", out var argsElement))
                {
                    foreach (var arg in argsElement.EnumerateObject())
                    {
                        arguments[arg.Name] = arg.Value.GetString();
                    }
                }

                var functionCall = new FunctionCallContent(funcName, pluginName, id, arguments);

                var message = new ChatMessageContent(AuthorRole.Assistant, content: null);
                message.Items.Add(functionCall);
                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 重建工具调用消息失败: {content}", content);
                return null;
            }
        }

        /// <summary>
        /// 重建工具返回消息
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <returns>ChatMessageContent或null</returns>
        private ChatMessageContent ReconstructToolResultMessage(string content)
        {
            try
            {
                if (!content.StartsWith("[TOOL_RESULT:"))
                {
                    return null;
                }

                var endIndex = content.IndexOf("] ");
                if (endIndex == -1)
                {
                    return null;
                }

                var functionNamePart = content.Substring("[TOOL_RESULT:".Length, endIndex - "[TOOL_RESULT:".Length);
                var result = content.Substring(endIndex + 2);

                // 解析插件名和函数名
                var parts = functionNamePart.Split('.');
                var pluginName = parts.Length > 1 ? parts[0] : "";
                var funcName = parts.Length > 1 ? parts[1] : functionNamePart;

                // 尝试从映射中获取对应的工具调用ID，如果没有则生成新的
                var toolCallId = _toolCallIdMapping.ContainsKey(content)
                    ? _toolCallIdMapping[content]
                    : Guid.NewGuid().ToString();

                var functionResult = new FunctionResultContent(funcName, pluginName, toolCallId, result);

                var message = new ChatMessageContent(AuthorRole.Tool, content: null);
                message.Items.Add(functionResult);
                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConversationMemoryService] 重建工具返回消息失败: {content}", content);
                return null;
            }
        }
    }
}
