using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain
{
    public class MysoftIPassApiService : ApiDomainService, IMysoftIPassApiService
    {
        private HttpClient _httpClient;
        private IHttpContextAccessor _httpContextAccessor;
        private const string _mysoftCookies = "Mysoft-Cookies";
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;
        public MysoftIPassApiService(IHttpClientFactory clientFactory, IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, 
            MysoftConfigurationDomain mysoftConfigurationDomain,IConfigurationService configurationService):base(configurationService)
        {
            _httpContextAccessor = httpContextAccessor;
            _httpClient = clientFactory.CreateClient("Mysoft_CustomerService");
            _mysoftContextFactory = mysoftContextFactory;
            _mysoftConfigurationDomain = mysoftConfigurationDomain;
        }
        public async Task<string> AuthCallBack(HttpHeaders heades, string uri, CancellationToken cancellationToken = default)
        {
            // var httpCookies = _httpContextAccessor.HttpContext.Request.Headers[_mysoftCookies];
            // if (string.IsNullOrWhiteSpace(httpCookies) == false)
            // {
            //     var cookies = await CookieStrHandler(httpCookies);
            //     if (cookies?.Count > 0)
            //     {
            //         var myApiAuthorizationCookie = cookies?.FirstOrDefault(x => x != null && x.Name != null && x.Name.Equals("my-api-Authorization"));
            //         if (myApiAuthorizationCookie != null)
            //         {
            //             Console.WriteLine(myApiAuthorizationCookie.Value);
            //             heades.Add("my-api-Authorization", myApiAuthorizationCookie.Value);
            //             return await Task.FromResult("");
            //         }
            //     }
            // }
            var mysoftContext = _mysoftContextFactory.GetMysoftContext();
            var myApiAuthorizationToken = await GetMyApiAuthorization(mysoftContext);
            if (string.IsNullOrWhiteSpace(myApiAuthorizationToken) == false)
            {
                heades.Add("my-api-Authorization", myApiAuthorizationToken);
                return await Task.FromResult("");
            }
            var token = await GetIPassApiToken(mysoftContext);
            return await Task.FromResult(token);
        }
        public async Task<string> PostAsync(string uri, string body, CancellationToken cancellationToken = default)
        {
            var httpContent = new StringContent(body, Encoding.UTF8, "application/json");
            var token = await AuthCallBack(httpContent.Headers, uri, cancellationToken);
            AuthenticationHeaderValue authenticationHeaderValue = null;
            if (!string.IsNullOrEmpty(token))
            {
                authenticationHeaderValue = new AuthenticationHeaderValue("Bearer", token);
            }

            var result = await this.SendRequestAsync(_httpClient, uri, HttpMethod.Post, httpContent, cancellationToken, authenticationHeaderValue);
            //var askResult = JsonConvert.DeserializeObject<MipApiResultDto>(result);
            //if (askResult.Errcode != 0)
            //{
            //    throw new BusinessException(askResult.Errmsg);
            //}
            return await Task.FromResult(result);
        }
        
        public async Task<string> GetAsync(string uri, Dictionary<string,  object> requestParams, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }

        private async Task<string> GetIPassApiToken(MysoftContext mysoftContext)
        {
            var mipInfo = _mysoftConfigurationDomain.GetMipInfo();
            var tokenParam = new
            {
                client_id = mipInfo.ErpAppName,
                client_secret = mipInfo.ErpAppPwd
            };
            var jsonBody = JsonConvert.SerializeObject(tokenParam);
            var httpContent = new StringContent(jsonBody, Encoding.UTF8, "application/json");
            var uri = mipInfo.ServiceUrl + "/MIPApiAuth/Token";
            var result = await this.SendRequestAsync(_httpClient, uri, HttpMethod.Post, httpContent, default);
            MipApiTokenDto mipApiTokenDto = JsonConvert.DeserializeObject<MipApiTokenDto>(result);
            return await Task.FromResult(mipApiTokenDto.Access_token);
        }
    }
    public class MipApiTokenDto
    {
        public string Access_token { get; set; }
        public long Expires_in { get; set; }
        public string Token_type { get; set; }
        public string Scope { get; set; }
    }

    public class MipApiResultDto
    {
        public string Errmsg { get; set; }
        public long Errcode { get; set; }
        public object Data { get; set; }
    }
}