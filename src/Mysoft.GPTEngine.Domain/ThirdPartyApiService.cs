using Mysoft.GPTEngine.Domain.Shared;
using System;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.SemanticKernel;
using Microsoft.AspNetCore.Http;
using System.Net.Http;
using System.IO;
using static Humanizer.In;

namespace Mysoft.GPTEngine.Domain
{
    public class ThirdPartyApiService : ApiDomainService, IThirdPartyApiService
    {
        private IHttpContextAccessor _httpContextAccessor;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private Kernel _kernel;
        public ThirdPartyApiService(IHttpClientFactory clientFactory, IHttpContextAccessor httpContextAccessor, Kernel kernel,IConfigurationService configurationService, IMysoftContextFactory mysoftContextFactory):base(configurationService)
        {
            _httpContextAccessor = httpContextAccessor;
            _mysoftContextFactory = mysoftContextFactory;
            _kernel = kernel;
        }
        public async Task<string> AuthCallBack(HttpHeaders heades, string uri, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }

        public async Task<string> PostAsync(string uri, string body, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }

        public async Task<string> GetAsync(string uri, Dictionary<string, object> requestParams, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }
    }
}
