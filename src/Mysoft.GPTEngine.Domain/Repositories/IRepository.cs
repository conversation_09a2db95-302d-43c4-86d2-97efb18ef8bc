using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    /// <summary>
    /// 定义一个泛型的仓库接口，用于对实体进行CRUD操作。
    /// 该接口适用于所有继承自IBaseEntity接口且为class类型的数据实体。
    /// </summary>
    /// <typeparam name="TEntity">泛型参数，代表一个实体类型，该实体必须继承自IBaseEntity接口并且是一个class类型。</typeparam>
    public interface IRepository<TEntity> where TEntity : class, IBaseEntity
    {
        /// <summary>
        /// 异步查找单个实体，根据给定的条件。
        /// </summary>
        /// <param name="predicate">用于查找实体的条件表达式。</param>
        /// <param name="includeDetails">是否包含详细信息，默认为 true。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>找到的实体，如果未找到则为 null。</returns>
        Task<TEntity> FindAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = true, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步获取单个实体，根据给定的条件。
        /// </summary>
        /// <param name="predicate">用于获取实体的条件表达式。</param>
        /// <param name="includeDetails">是否包含详细信息，默认为 true。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>找到的实体。</returns>
        Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = true, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步删除满足条件的所有实体。
        /// </summary>
        /// <param name="predicate">用于删除实体的条件表达式。</param>
        /// <param name="autoSave">是否自动保存更改，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>无返回值。</returns>
        Task DeleteAsync(Expression<Func<TEntity, bool>> predicate, bool autoSave = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步直接删除满足条件的所有实体，不进行自动保存。
        /// </summary>
        /// <param name="predicate">用于删除实体的条件表达式。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>无返回值。</returns>
        Task DeleteDirectAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步获取包含详细信息的实体查询。
        /// </summary>
        /// <returns>包含详细信息的实体查询。</returns>
        Task<IQueryable<TEntity>> WithDetailsAsync();

        /// <summary>
        /// 异步获取包含指定详细信息的实体查询。
        /// </summary>
        /// <param name="propertySelectors">用于选择详细信息的表达式数组。</param>
        /// <returns>包含指定详细信息的实体查询。</returns>
        Task<IQueryable<TEntity>> WithDetailsAsync(params Expression<Func<TEntity, object>>[] propertySelectors);

        /// <summary>
        /// 异步获取基本实体查询。
        /// </summary>
        /// <returns>基本实体查询。</returns>
        Task<IQueryable<TEntity>> GetQueryableAsync();

        /// <summary>
        /// 异步获取实体列表。
        /// </summary>
        /// <param name="includeDetails">是否包含详细信息，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>实体列表。</returns>
        Task<List<TEntity>> GetListAsync(bool includeDetails = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步获取实体数量。
        /// </summary>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>实体数量。</returns>
        Task<long> GetCountAsync(CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步获取分页列表。
        /// </summary>
        /// <param name="skipCount">跳过的记录数。</param>
        /// <param name="maxResultCount">最大结果数。</param>
        /// <param name="sorting">排序条件。</param>
        /// <param name="includeDetails">是否包含详细信息，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>分页实体列表。</returns>
        Task<List<TEntity>> GetPagedListAsync(int skipCount, int maxResultCount, string sorting, bool includeDetails = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步插入单个实体。
        /// </summary>
        /// <param name="entity">要插入的实体。</param>
        /// <param name="autoSave">是否自动保存，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>插入的实体。</returns>
        Task<TEntity> InsertAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步插入多个实体。
        /// </summary>
        /// <param name="entities">要插入的实体集合。</param>
        /// <param name="autoSave">是否自动保存，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>无返回值。</returns>
        Task InsertManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步更新单个实体。
        /// </summary>
        /// <param name="entity">要更新的实体。</param>
        /// <param name="autoSave">是否自动保存，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>更新的实体。</returns>
        Task<TEntity> UpdateAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步更新多个实体。
        /// </summary>
        /// <param name="entities">要更新的实体集合。</param>
        /// <param name="autoSave">是否自动保存，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>无返回值。</returns>
        Task UpdateManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步删除单个实体。
        /// </summary>
        /// <param name="entity">要删除的实体。</param>
        /// <param name="autoSave">是否自动保存，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>无返回值。</returns>
        Task DeleteAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default(CancellationToken));

        /// <summary>
        /// 异步删除多个实体。
        /// </summary>
        /// <param name="entities">要删除的实体集合。</param>
        /// <param name="autoSave">是否自动保存，默认为 false。</param>
        /// <param name="cancellationToken">取消令牌，默认为 default。</param>
        /// <returns>无返回值。</returns>
        Task DeleteManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default(CancellationToken));
    }
}
