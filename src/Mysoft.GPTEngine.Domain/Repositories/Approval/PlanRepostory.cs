using Mysoft.GPTEngine.Domain.Entity.Approval;
using Mysoft.GPTEngine.Domain.Shared;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Repositories.Approval
{
    public class PlanRepostory : SqlSugarRepository<PlanEntity>
    {
        public PlanRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}
