using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    public class KnowledgeTaskDetailRepostory : SqlSugarRepository<KnowledgeEvaluatingTaskDetailEntity>
    {
        public KnowledgeTaskDetailRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {

        }
    }
}
