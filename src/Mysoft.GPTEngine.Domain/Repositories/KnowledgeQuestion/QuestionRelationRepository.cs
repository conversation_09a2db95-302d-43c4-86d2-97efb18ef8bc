using Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion;
using Mysoft.GPTEngine.Domain.Shared;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Repositories.KnowledgeQuestion
{
    public class QuestionRelationRepository : SqlSugarRepository<QuestionRelationEntity>
    {
        /**
         * 构造函数：初始化ModelRepostory实例。
         * 
         * @param sqlSugarClient SqlSugarClient实例，用于数据库操作。
         */
        public QuestionRelationRepository(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}
