using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Repositories.KnowledgeQuestion;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.SemanticKernel.Embeddings;
using DocumentFormat.OpenXml.Office2010.Word;
using System.Diagnostics;
using Mysoft.GPTEngine.Common.CustomerException;
using Array = DocumentFormat.OpenXml.Office2019.Excel.RichData2.Array;

namespace Mysoft.GPTEngine.Domain
{
    public class KnowledgeDomainService : DomainServiceBase, IKnowledgeDomainService
    {
        private readonly KnowledgeRepository _knowledgeRepository;
        private readonly KnowledgeFileRepository _knowledgeFileRepository;
        private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
        private readonly KnowledgeQuestionRepostory _knowledgeQuestionRepostory;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly Kernel _kernel;
        private readonly IMilvusMemoryDomainService _milvusMemoryDomainService;
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        private readonly QuestionRepository _questionRepository;
        private readonly QuestionRelationRepository _questionRelationRepository;
        private readonly KnowledgeHyperLinkRepostory _knowledgeHyperLinkRepostory;

        public KnowledgeDomainService(Kernel kernel, ModelInstanceRepostory modelInstanceRepostory, IMilvusMemoryDomainService milvusMemoryDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper
            , KnowledgeFileRepository knowledgeFileRepository, KnowledgeRepository knowledgeRepository, QuestionRepository questionRepository, QuestionRelationRepository questionRelationRepository
            , KnowledgeHyperLinkRepostory knowledgeHyperLinkRepostory, KnowledgeFileSectionRepository knowledgeFileSectionRepository, KnowledgeQuestionRepostory knowledgeQuestionRepostory, MysoftApiService mysoftApiDomainService) : base(mysoftContextFactory, httpContextAccessor, mapper)
        {
            _knowledgeRepository = knowledgeRepository;
            _knowledgeFileRepository = knowledgeFileRepository;
            _knowledgeFileSectionRepository = knowledgeFileSectionRepository;
            _mysoftApiDomainService = mysoftApiDomainService;
            _knowledgeQuestionRepostory = knowledgeQuestionRepostory;
            _kernel = kernel;
            _milvusMemoryDomainService = milvusMemoryDomainService;
            _modelInstanceRepostory = modelInstanceRepostory;
            _questionRepository = questionRepository;
            _questionRelationRepository = questionRelationRepository;
            _knowledgeHyperLinkRepostory = knowledgeHyperLinkRepostory;
        }

        public async Task<string> GetDownloadUrl(Guid documentGUID)
        {
            //从gptbuilder拿到文档服务下载地址
            string gptBuilderUrl = _mysoftContextFactory.GetMysoftContext().GptBuilderUrl + GPTBuilderRequestPathConst.GetDownloadUrl + "?documentGUID=" + documentGUID;
            var res = await _mysoftApiDomainService.PostAsync(gptBuilderUrl, "");
            return await Task.FromResult(JsonConvert.DeserializeObject<ReturnDto<string>>(res).Data);
        }

        public async Task<List<DocumentInfoBaseDto>> GetDocumentInfo(List<Guid> documentGUIDs, int isReturnUrl = 1)
        {
            string gptBuilderUrl = _mysoftContextFactory.GetMysoftContext().GptBuilderUrl + GPTBuilderRequestPathConst.GetDocumentBaseInfo;
            DocumentQueryDto documentQueryDto = new DocumentQueryDto();
            documentQueryDto.IsReturnUrl = isReturnUrl;
            documentQueryDto.DocumentGUIDs = documentGUIDs;
            var res = await _mysoftApiDomainService.PostAsync(gptBuilderUrl, JsonConvert.SerializeObject(documentQueryDto));
            return await Task.FromResult(JsonConvert.DeserializeObject<ReturnDto<List<DocumentInfoBaseDto>>>(res).Data);
        }

        //根据问题guid查询问题信息
        public async Task<List<KnowledgeQuestionDto>> GetQuestionByQuestionGUID(List<Guid> questionGuids)
        {
            List<KnowledgeQuestionDto> knowledgeQuestionDtos = new List<KnowledgeQuestionDto>();
            if (questionGuids.Count == 0) return knowledgeQuestionDtos;
            List<QuestionEntity> knowledgeQuestionEntities = await _questionRepository.GetListAsync(f => questionGuids.Contains(f.KnowledgeQuestionGUID)).ConfigureAwait(false);
            List<QuestionRelationEntity> questionRelationEntities = await _questionRelationRepository.GetListAsync(f => questionGuids.Contains(f.KnowledgeQuestionGUID) && f.Disable == 0).ConfigureAwait(false);
            foreach(var questionRelation in questionRelationEntities)
            {
                QuestionEntity questionEntity = knowledgeQuestionEntities.FirstOrDefault(x => x.KnowledgeQuestionGUID == questionRelation.KnowledgeQuestionGUID);
                if (questionEntity == null)
                {
                    continue;
                }
                KnowledgeQuestionDto knowledgeQuestion = new KnowledgeQuestionDto();
                knowledgeQuestion.KnowledgeFileSectionGUID = questionRelation.KnowledgeSectionGUID;
                knowledgeQuestion.Question = questionEntity.Question;
                knowledgeQuestion.KnowledgeQuestionGUID = questionRelation.KnowledgeQuestionGUID;
                knowledgeQuestionDtos.Add(knowledgeQuestion);
            }
            return knowledgeQuestionDtos;
        }
        //查询切片信息
        public async Task<ImageUrlDto> GetKnowledgeFileSections(List<Guid> sectionGuids)
        {
            ImageUrlDto imageUrlDto = new ImageUrlDto();
            var sectionEntities = await _knowledgeFileSectionRepository.GetListAsync(x => sectionGuids.Contains(x.KnowledgeFileSectionGUID)).ConfigureAwait(false);
            if (sectionEntities?.Count == 0) return imageUrlDto;
            //文档服务GUID对象
            KnowledgeDocumentURLDto knowledgeDocumentURLDto = new KnowledgeDocumentURLDto();

            var knowledgeFileSections = _mapper.Map<List<KnowledgeFileSectionDto>>(sectionEntities);
            var fileGuids = knowledgeFileSections.Select(x => x.KnowledgeFileGUID).ToList();
            var files = await _knowledgeFileRepository.GetListAsync(x => fileGuids.Contains(x.KnowledgeFileGUID)).ConfigureAwait(false);
            var keyValues = files.ToDictionary(x => x.KnowledgeFileGUID);
            // 图片地址
            Dictionary<Guid, string> imagerUrlMap = new Dictionary<Guid, string>();
            foreach (var knowledgeFileSection in knowledgeFileSections)
            {
                if (!keyValues.ContainsKey(knowledgeFileSection.KnowledgeFileGUID))
                {
                    continue;
                }
                var file =  keyValues[knowledgeFileSection.KnowledgeFileGUID];
                knowledgeFileSection.FileName = file.FileName;
                knowledgeFileSection.FileType = file.FileType;
                knowledgeFileSection.FileSize = file.FileSize;
                knowledgeFileSection.DocumentGUID = file.DocumentGUID;
                knowledgeFileSection.FileSourceEnum = file.FileSourceEnum;
                knowledgeFileSection.ThirdViewURL = file.ThirdViewURL;
                //解析图片占位符
                List<Guid> imageGuids = ImageExtractionHelper.ImageExtraction(knowledgeFileSection.Content);
                imageGuids.ForEach(f =>
                {
                    DocumentURLDto documentURLDto = new DocumentURLDto();
                    documentURLDto.DocumentGUID = f;
                    knowledgeDocumentURLDto.knowledgeImageURLDtos.Add(documentURLDto);
                });
                knowledgeFileSection.imageGUIDs = imageGuids;
                knowledgeFileSection.Content =
                    ImageExtractionHelper.ReplaceMarkdownImageWithUuid(knowledgeFileSection.Content, imagerUrlMap, imageGuids);
            }

            var fileDocumentGUIDs = knowledgeFileSections.Select(s => new { documentGUID = s.DocumentGUID, thirdViewUrl = s.ThirdViewURL }).Distinct().ToList();
            fileDocumentGUIDs.ForEach(f =>
            {
                DocumentURLDto documentURLDto = new DocumentURLDto();
                documentURLDto.DocumentGUID = f.documentGUID;
                documentURLDto.DocumentUrl = f.thirdViewUrl;
                knowledgeDocumentURLDto.knowledgeDocumentURLDtos.Add(documentURLDto);
            });

            //查询平台，获取图片地址
            KnowledgeDocumentURLDto documentURLDto = await QueryDocumentURL(knowledgeDocumentURLDto).ConfigureAwait(false);

            foreach (var kvp in imagerUrlMap)
            {
                DocumentURLDto dto = new DocumentURLDto();
                dto.DocumentGUID = kvp.Key;
                dto.DocumentUrl = kvp.Value;
                documentURLDto.knowledgeImageURLDtos.Add(dto);
            }

            //图片占位符替换
            await KnowledgeURLReplaceHandle(documentURLDto, knowledgeFileSections, imageUrlDto).ConfigureAwait(false);
            //超链接替换
            await KnowledgeHyperLinkReplaceHandle(knowledgeFileSections, imageUrlDto).ConfigureAwait(false);
            return await Task.FromResult(imageUrlDto);
        }

        public async Task KnowledgeURLReplaceHandle(KnowledgeDocumentURLDto documentURLDto, List<KnowledgeFileSectionDto> knowledgeFileSections, ImageUrlDto imageUrlDto)
        {
            int imageCount = 1;
            knowledgeFileSections.ForEach(m =>
            {
                //替换文档图片
                var documentInfo = documentURLDto.knowledgeDocumentURLDtos.Find(f => f.DocumentGUID == m.DocumentGUID);
                if (documentInfo != null)
                {
                    m.FileUrl = documentInfo.DocumentUrl;
                }

                //图片占位符替换
                m.imageGUIDs.ForEach((f) =>
                {
                    var imageInfo = documentURLDto.knowledgeImageURLDtos.Find(p => p.DocumentGUID == f);
                    if (imageInfo != null)
                    {
                        ReplaceDto replaceDto = new ReplaceDto() { 
                            key = $"![图片{imageCount}](图片{imageCount})",
                            value = $"![图片{imageCount}]({imageInfo.DocumentUrl})" 
                        };
                        m.Content = m.Content.Replace($"{{{{image:{f}}}}}", $"![图片{imageCount}](图片{imageCount})");
                        m.ImagePreviewUrl = imageInfo.DocumentUrl;
                        imageUrlDto.replaceDtos.Add(replaceDto);
                        imageCount++;
                    }
                });
            });
            imageUrlDto.knowledgeFileSectionDtos = knowledgeFileSections;
            await Task.CompletedTask;
        }

        public async Task KnowledgeHyperLinkReplaceHandle(List<KnowledgeFileSectionDto> knowledgeFileSections, ImageUrlDto imageUrlDto)
        {
            int hyperlinkCount = 1;
            //解析图片占位符
            List<Guid> hyperlinkGuids = new List<Guid>();
            foreach (var item in knowledgeFileSections)
            {
                hyperlinkGuids.AddRange(ImageExtractionHelper.HyperLinkExtraction(item.Content));
            }
            if(hyperlinkGuids.Count > 0)
            {
                //查询超链接地址
                List<KnowledgeHyperLinkEntity> hyperLinkEntities = await _knowledgeHyperLinkRepostory.GetListAsync(f=> hyperlinkGuids.Contains(f.KnowledgeHyperLinkGUID)).ConfigureAwait(false);
                knowledgeFileSections.ForEach(m =>
                {
                    hyperLinkEntities.ForEach(h =>
                    {
                        ReplaceDto replaceDto = new ReplaceDto()
                        {
                            key = $"[超链接{hyperlinkCount}](超链接{hyperlinkCount})",
                            value = $"[{h.Name}]({h.HyperLinkURL})"
                        };
                        m.Content = m.Content.Replace($"{{{{hyperlink:{h.KnowledgeHyperLinkGUID}}}}}", $"[超链接{hyperlinkCount} ](超链接{hyperlinkCount})");
                        imageUrlDto.replaceDtos.Add(replaceDto);
                        hyperlinkCount++;
                    });
                });
            }
        }

        /// <summary>
        /// 查询对应文档的URL
        /// </summary>
        /// <param name="knowledgeDocumentURLDto"></param>
        /// <returns></returns>
        public async Task<KnowledgeDocumentURLDto> QueryDocumentURL(KnowledgeDocumentURLDto knowledgeDocumentURLDto)
        {
            if (knowledgeDocumentURLDto.knowledgeDocumentURLDtos.Count == 0 && knowledgeDocumentURLDto.knowledgeImageURLDtos.Count == 0)
            {
                return knowledgeDocumentURLDto;
            }

            //组装地址
            string gptBuilderUrl = _mysoftContextFactory.GetMysoftContext().GptBuilderUrl + GPTBuilderRequestPathConst.QueryDocumentURL;
            string res = await _mysoftApiDomainService.PostAsync(gptBuilderUrl, JsonConvert.SerializeObject(knowledgeDocumentURLDto)).ConfigureAwait(false);
            return await Task.FromResult(JsonConvert.DeserializeObject<ReturnDto<KnowledgeDocumentURLDto>>(res).Data);
        }

        /// <summary>
        /// 查询向量库数据
        /// </summary>
        /// <param name="knowledgeCodes"></param>
        /// <param name="query"></param>
        /// <param name="limit"></param>
        /// <param name="minScore"></param>
        /// <returns></returns>
        public async Task<List<QueryResultDto>> GetQueryTopResult(string knowledgeCodes,string query,int limit,double minScore)
        {

            var knowledgeQueryEmbeddingMap = await GetKnowledgeQueryEmbeddingMap(knowledgeCodes, query, _kernel);

            return await GetQueryTopResult(knowledgeQueryEmbeddingMap, limit, minScore);
        }

        public async Task<Dictionary<string, ReadOnlyMemory<float>>> GetKnowledgeQueryEmbeddingMap(string knowledgeCodes,string query, Kernel _kernel)
        {
            Dictionary<string, ITextEmbeddingGenerationService> embeddingGenerationServicesMap = new Dictionary<string, ITextEmbeddingGenerationService>();
            Dictionary<string, ReadOnlyMemory<float>> queryEmbeddingMap = new Dictionary<string, ReadOnlyMemory<float>>();
            foreach (string memoryCollectionName in knowledgeCodes.Split(","))
            {
                if (queryEmbeddingMap.ContainsKey(memoryCollectionName))
                {
                    continue;
                }
                ITextEmbeddingGenerationService embeddingGenerationService = null;
                if (!embeddingGenerationServicesMap.ContainsKey(memoryCollectionName))
                {
                    var serviceKey = await GetEmbeddingModelCodeByKnowledgeCode(memoryCollectionName).ConfigureAwait(false);
                    embeddingGenerationService = _kernel.GetRequiredService<ITextEmbeddingGenerationService>(serviceKey);
                    embeddingGenerationServicesMap[memoryCollectionName] = embeddingGenerationService;
                }
                else
                {
                    embeddingGenerationService = embeddingGenerationServicesMap[memoryCollectionName];
                }
                var embedding =await embeddingGenerationService.GenerateEmbeddingAsync(query, _kernel).ConfigureAwait(false);
                queryEmbeddingMap[memoryCollectionName] = embedding;
            }

            return queryEmbeddingMap;
        }

        /// <summary>
        /// 查询向量库数据
        /// </summary>
        /// <param name="knowledgeCodes"></param>
        /// <param name="query"></param>
        /// <param name="limit"></param>
        /// <param name="minScore"></param>
        /// <returns></returns>

        public async Task<List<KnowledgeEmbeddingsResultDto>> GetEmbeddingsText(string modelInstanceCode, List<string> input)
        {
            var embeddingInstance = await _modelInstanceRepostory.GetFirstAsync(x => x.InstanceCode == modelInstanceCode);
            if (embeddingInstance == null)
            {
                throw new NoRequiredArgumentException($"模型编码【{modelInstanceCode}】对应的模型不存在");
            }
            
            string chatCompletionType = null;
            var serviceId = embeddingInstance.IsDefault ? chatCompletionType : embeddingInstance.InstanceCode;

            ITextEmbeddingGenerationService embeddingGenerationService = 
                _kernel.GetRequiredService<ITextEmbeddingGenerationService>(serviceId);

            var results = new List<KnowledgeEmbeddingsResultDto>();
            int index = 0;
            foreach (var inputText in input)
            {
                ReadOnlyMemory<float> embedding = await embeddingGenerationService.GenerateEmbeddingAsync(inputText);
                results.Add(new KnowledgeEmbeddingsResultDto
                {
                    Embedding = embedding.ToArray().ToList(),
                    Index = index
                });
                index++;
                // ✅ 每处理10个文本休眠100毫秒，防止API请求过载
                if (index % 10 == 0)
                {
                    await Task.Delay(100);
                }
            }

            return results;
        }

        public async Task<List<QueryResultDto>> GetQueryTopResult(Dictionary<string, ReadOnlyMemory<float>> knowledgeQueryEmbeddingMap,int limit,double minScore)
        {
            List<QueryResultDto> memoryQueryResults = new List<QueryResultDto>();
            List<QueryResultDto> questionQueryResults = new List<QueryResultDto>();
            
            foreach (var knowledgeQueryEmbedding in knowledgeQueryEmbeddingMap)
            {
                // 查询文档
                var hasFile = await HasFileByKnowledgeCode(knowledgeQueryEmbedding.Key);
                if (hasFile)
                {
                    await foreach (var answer in this._milvusMemoryDomainService.SearchAsync(knowledgeQueryEmbedding.Key, knowledgeQueryEmbedding.Value, limit, minScore, false, (int)SourceTypeEnum.FileSection).ConfigureAwait(false))
                    {
                        QueryResultDto queryResultDto = new QueryResultDto();
                        queryResultDto.id = Guid.Parse(answer.Metadata.Id);
                        queryResultDto.score = answer.Relevance;
                        queryResultDto.knowledgeCode = knowledgeQueryEmbedding.Key;
                        memoryQueryResults.Add(queryResultDto);
                    }
                }

                // 查询问题
                var hasQuestion = await HasQuestionByKnowledgeCode(knowledgeQueryEmbedding.Key);
                if (hasQuestion)
                {
                    await foreach (var answer in this._milvusMemoryDomainService.SearchAsync(knowledgeQueryEmbedding.Key, knowledgeQueryEmbedding.Value, limit, minScore, false, (int)SourceTypeEnum.Question).ConfigureAwait(false))
                    {
                        QueryResultDto queryResultDto = new QueryResultDto();
                        queryResultDto.id = Guid.Parse(answer.Metadata.Id);
                        queryResultDto.score = answer.Relevance;
                        queryResultDto.knowledgeCode = knowledgeQueryEmbedding.Key;
                        questionQueryResults.Add(queryResultDto);
                    }
                }
            }

            return await MargerQueryResult(memoryQueryResults, questionQueryResults, limit).ConfigureAwait(false);

        }

        private async Task<List<QueryResultDto>> MargerQueryResult(List<QueryResultDto> memoryQueryResults,
            List<QueryResultDto> questionQueryResults, int limit)
        {
            if ((memoryQueryResults == null || memoryQueryResults.Count == 0) &&(questionQueryResults == null || questionQueryResults.Count == 0)) return new List<QueryResultDto>();

            List<Guid> questionGUIDs = questionQueryResults.Select(s => s.id).ToList();
            // 根据问题id查询问题对象
            var questionDtos = await GetQuestionByQuestionGUID(questionGUIDs);
            questionGUIDs = questionDtos.Select(s => s.KnowledgeQuestionGUID).Distinct().ToList();
            //前几条标题id，测试环境有偶发的报错，先加个校验，避免问题没有查询到的情况
            var topQuesList = questionDtos != null && questionDtos.Count > 0 ? questionDtos
                .Select(x => new QueryResultDto
            {
                score = questionQueryResults.Find(f=>f.id == x.KnowledgeQuestionGUID).score,
                knowledgeCode = questionQueryResults.Find(f=>f.id == x.KnowledgeQuestionGUID).knowledgeCode,
                id = x.KnowledgeFileSectionGUID
            }).ToList() : new List<QueryResultDto>();
            //按最高分去重
            var distinctArray = topQuesList != null && topQuesList.Count > 0 ? topQuesList.GroupBy(x => x.id.ToString())
                .Select(g => g.OrderByDescending(x => x.score).First()) : null;
            //前几条切片id
            var topSectionList = memoryQueryResults.OrderByDescending(x => x.score).Take(limit).Select(x => new QueryResultDto
            {
                score = x.score,
                knowledgeCode = x.knowledgeCode,
                id = x.id
            }).ToList();
            if (distinctArray != null)
            {
                //汇总取前几条
                topSectionList.AddRange(distinctArray);
                //这里要再做个去重
                topSectionList = topSectionList.GroupBy(x => x.id.ToString()).Select(g => g.OrderByDescending(x => x.score).First()).ToList();
            }
            var totalIdList = topSectionList.OrderByDescending(x => x.score).Take(limit).ToList();
            //List<QueryResultDto> totalResultDto = CalculateScore(memoryQueryResults, topQuesList);

            ////前几条切片id
            //var topSectionList = totalResultDto.OrderByDescending(x => x.score).Take(limit).ToList();
            return totalIdList;
        }

        /// <summary>
        /// 根据平均法重排一下评分
        /// </summary>
        /// <param name="sectionResults"></param>
        /// <param name="titleRsults"></param>
        /// <returns></returns>
        public List<QueryResultDto> CalculateScore(List<QueryResultDto> sectionResults, List<QueryResultDto> titleRsults)
        {
            //切片id合并，去重
            List<Guid> sectionGUIDs = sectionResults.Select(s => s.id).ToList();
            List<Guid> titleGUIDs = titleRsults.Select(s => s.id).ToList();
            List<Guid> totalGUIDs = sectionGUIDs.Concat(titleGUIDs).Distinct().ToList();
            List<QueryResultDto> totalResults = new List<QueryResultDto>();
            totalGUIDs.ForEach(f =>
            {
                QueryResultDto queryResultDto = new QueryResultDto();
                queryResultDto.id = f;

                List<Double> scoreList = new List<Double>();
                List<Double> sectionScore = sectionResults.FindAll(s => s.id == f).Select(s => s.score).ToList();
                if (sectionScore.Count > 0)
                {
                    scoreList.AddRange(sectionScore);
                }
                else
                {
                    scoreList.Add(0);
                }
                //List<Double> titleScore = titleRsults.FindAll(s => s.id == f).Select(s => s.score).ToList();
                //if (titleScore.Count > 0)
                //{
                //    scoreList.AddRange(titleScore);
                //}
                //else
                //{
                //    scoreList.Add(0);
                //}
                queryResultDto.score = scoreList.Average();
                totalResults.Add(queryResultDto);
            });
            return totalResults;
        }

        /// <summary>
        /// 根据知识库编码查询向量模型
        /// </summary>
        /// <param name="knowledgeCode"></param>
        /// <returns></returns>
        public async Task<string> GetEmbeddingModelCodeByKnowledgeCode(string knowledgeCode)
        {
            var knowledge = await _knowledgeRepository.FindAsync(f => f.Code == knowledgeCode).ConfigureAwait(false);
            if (knowledge == null || string.IsNullOrEmpty(knowledge.EmbeddingModelCode))
            {
                return null;
            }
            int count = await _modelInstanceRepostory.CountAsync(f => f.IsDefault && f.InstanceCode == knowledge.EmbeddingModelCode).ConfigureAwait(false);
            if(count > 0)
            {
                return null;
            }
            return knowledge.EmbeddingModelCode;
        }
        
        /// <summary>
        /// 根据知识库编码查询关联问题
        /// </summary>
        /// <param name="knowledgeCode"></param>
        /// <returns></returns>
        public async Task<bool> HasQuestionByKnowledgeCode(string knowledgeCode)
        {
            var knowledge = await _knowledgeRepository.FindAsync(f => f.Code == knowledgeCode).ConfigureAwait(false);
            if (knowledge == null)
            {
                return false;
            }
            int count = await _questionRepository.CountAsync(f =>f.KnowledgeGUID == knowledge.KnowledgeGUID).ConfigureAwait(false);
            if(count > 0)
            {
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 根据知识库编码查询关联文档
        /// </summary>
        /// <param name="knowledgeCode"></param>
        /// <returns></returns>
        public async Task<bool> HasFileByKnowledgeCode(string knowledgeCode)
        {
            var knowledge = await _knowledgeRepository.FindAsync(f => f.Code == knowledgeCode).ConfigureAwait(false);
            if (knowledge == null)
            {
                return false;
            }
            int count = await _knowledgeFileRepository.CountAsync(f =>f.KnowledgeGUID == knowledge.KnowledgeGUID && f.IndexStatusEnum == (int)IndexStatusEnum.Completed).ConfigureAwait(false);
            if(count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
