using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.IO;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// HttpClient消息处理器，打印请求和响应内容到控制台。
    /// </summary>
    public class LoggingHttpHandler : DelegatingHandler
    {
        private readonly LoggingHttpHandlerOptions _options;

        public LoggingHttpHandler(HttpMessageHandler innerHandler = null, LoggingHttpHandlerOptions options = null)
            : base(CreateHandlerChain(innerHandler, options))
        {
            _options = options ?? new LoggingHttpHandlerOptions();
        }

        // 保持向后兼容性的构造函数
        public LoggingHttpHandler(HttpMessageHandler innerHandler, bool enableStreamLogging)
            : base(innerHandler ?? new HttpClientHandler())
        {
            _options = new LoggingHttpHandlerOptions { EnableStreamLogging = enableStreamLogging };
        }

        /// <summary>
        /// 创建处理器链，添加ThinkingModeHttpHandler和MultimodalHttpHandler用于处理额外的请求参数和多模态消息转换
        /// </summary>
        private static HttpMessageHandler CreateHandlerChain(HttpMessageHandler innerHandler, LoggingHttpHandlerOptions options)
        {
            var baseHandler = innerHandler ?? new HttpClientHandler();

            // 构建处理器链：MultimodalHttpHandler -> ThinkingModeHttpHandler -> BaseHandler
            // 这样多模态转换会在思考模式参数添加之前进行

            HttpMessageHandler currentHandler = baseHandler;

            // 总是添加ThinkingModeHttpHandler，它会根据配置决定是否添加参数
            var thinkingOptions = options?.ThinkingMode ?? new ThinkingModeOptions();
            currentHandler = new ThinkingModeHttpHandler(currentHandler, thinkingOptions);

            // 如果启用多模态支持，添加MultimodalHttpHandler
            if (options?.EnableMultimodalSupport == true)
            {
                currentHandler = new MultimodalHttpHandler(currentHandler);
            }

            return currentHandler;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            if (_options.EnableRequestLogging)
            {
                try
                {
                    Console.WriteLine($"{_options.LogPrefix} Request: {request.Method} {request.RequestUri}");
                    if (request.Content != null && !(request.Content is StreamContent))
                    {
                        var requestBody = await request.Content.ReadAsStringAsync();
                        Console.WriteLine($"{_options.LogPrefix} Request Headers: {request.Headers}");
                        // 修正：将Unicode转为正常字符
                        Console.WriteLine($"{_options.LogPrefix} Request Body: {System.Text.RegularExpressions.Regex.Unescape(requestBody)}");
                        var mediaType = request.Content.Headers.ContentType?.MediaType;
                        request.Content = new StringContent(requestBody, Encoding.UTF8, mediaType);
                    }
                    else
                    {
                        Console.WriteLine($"{_options.LogPrefix} Request Headers: {request.Headers}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{_options.LogPrefix} Exception logging request: {ex.Message}");
                }
            }

            var response = await base.SendAsync(request, cancellationToken);

            if (_options.EnableResponseLogging)
            {
                try
                {
                    Console.WriteLine($"{_options.LogPrefix} Response: {(int)response.StatusCode} {response.ReasonPhrase}");
                    Console.WriteLine($"{_options.LogPrefix} Response Headers: {response.Headers}");
                    var contentType = response.Content?.Headers?.ContentType?.MediaType;
                    if (response.Content != null && contentType == "text/event-stream")
                    {
                        // 对于流式响应，只有在启用流式日志时才创建包装流
                        if (_options.EnableStreamLogging)
                        {
                            var originalStream = await response.Content.ReadAsStreamAsync();
                            var loggingStream = new LoggingStream(originalStream, _options);
                            var streamContent = new StreamContent(loggingStream);

                            // 复制原始响应的所有头部信息
                            foreach (var header in response.Content.Headers)
                            {
                                streamContent.Headers.TryAddWithoutValidation(header.Key, header.Value);
                            }
                            response.Content = streamContent;
                        }
                        else
                        {
                            // 不启用流式日志时，只记录基本信息
                            Console.WriteLine($"{_options.LogPrefix} Streaming response detected (logging disabled for performance)");
                        }
                    }
                    else if (response.Content != null && _options.EnableResponseBodyLogging)
                    {
                        var responseBody = await response.Content.ReadAsStringAsync();
                        // 修正：将Unicode转为正常字符
                        Console.WriteLine($"{_options.LogPrefix} Response Body: {System.Text.RegularExpressions.Regex.Unescape(responseBody)}");
                        response.Content = new StringContent(responseBody, Encoding.UTF8, contentType);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{_options.LogPrefix} Exception logging response: {ex.Message}");
                }
            }

            return response;
        }
    }

    /// <summary>
    /// 用于流式响应日志记录的包装流
    /// </summary>
    public class LoggingStream : Stream
    {
        private readonly Stream _innerStream;
        private readonly StringBuilder _buffer = new StringBuilder();
        private readonly object _lockObject = new object();
        private readonly LoggingHttpHandlerOptions _options;

        public LoggingStream(Stream innerStream, LoggingHttpHandlerOptions options = null)
        {
            _innerStream = innerStream ?? throw new ArgumentNullException(nameof(innerStream));
            _options = options ?? new LoggingHttpHandlerOptions();
        }

        public override bool CanRead => _innerStream.CanRead;
        public override bool CanSeek => _innerStream.CanSeek;
        public override bool CanWrite => _innerStream.CanWrite;
        public override long Length => _innerStream.Length;
        public override long Position
        {
            get => _innerStream.Position;
            set => _innerStream.Position = value;
        }

        public override async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
        {
            var bytesRead = await _innerStream.ReadAsync(buffer, offset, count, cancellationToken);

            if (bytesRead > 0)
            {
                // 异步记录日志，避免阻塞流式处理
                _ = Task.Run(() => LogStreamDataAsync(buffer, offset, bytesRead), cancellationToken);
            }
            else
            {
                // 流结束时，异步输出剩余的内容
                _ = Task.Run(() => LogRemainingDataAsync(), cancellationToken);
            }

            return bytesRead;
        }

        private void LogStreamDataAsync(byte[] buffer, int offset, int bytesRead)
        {
            try
            {
                // 将读取的字节转换为字符串并记录
                var text = Encoding.UTF8.GetString(buffer, offset, bytesRead);

                lock (_lockObject)
                {
                    _buffer.Append(text);

                    // 检查是否有完整的行可以输出
                    var content = _buffer.ToString();
                    var newlineIndex = content.LastIndexOf('\n');

                    if (newlineIndex >= 0)
                    {
                        // 有完整的行，输出它们
                        var completedLines = content.Substring(0, newlineIndex);
                        var remainingContent = content.Substring(newlineIndex + 1);

                        if (!string.IsNullOrEmpty(completedLines))
                        {
                            // 批量输出完整的行，减少Console.WriteLine调用次数
                            var lines = completedLines.Split('\n');
                            foreach (var line in lines)
                            {
                                var trimmedLine = line.TrimEnd('\r');
                                if (!string.IsNullOrEmpty(trimmedLine))
                                {
                                    Console.WriteLine($"{_options.LogPrefix} Stream: {trimmedLine}");
                                }
                            }
                        }

                        // 保留未完成的行
                        _buffer.Clear();
                        _buffer.Append(remainingContent);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录日志异常，但不影响流式处理
                Console.WriteLine($"{_options.LogPrefix} Error logging stream data: {ex.Message}");
            }
        }

        private void LogRemainingDataAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    var remaining = _buffer.ToString().TrimEnd('\r', '\n');
                    if (!string.IsNullOrEmpty(remaining))
                    {
                        Console.WriteLine($"{_options.LogPrefix} Stream: {remaining}");
                    }
                    _buffer.Clear();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{_options.LogPrefix} Error logging remaining data: {ex.Message}");
            }
        }

        public override int Read(byte[] buffer, int offset, int count)
        {
            return ReadAsync(buffer, offset, count, CancellationToken.None).GetAwaiter().GetResult();
        }

        public override void Flush() => _innerStream.Flush();
        public override Task FlushAsync(CancellationToken cancellationToken) => _innerStream.FlushAsync(cancellationToken);
        public override long Seek(long offset, SeekOrigin origin) => _innerStream.Seek(offset, origin);
        public override void SetLength(long value) => _innerStream.SetLength(value);
        public override void Write(byte[] buffer, int offset, int count) => _innerStream.Write(buffer, offset, count);
        public override Task WriteAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken) => _innerStream.WriteAsync(buffer, offset, count, cancellationToken);

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _innerStream?.Dispose();
            }
            base.Dispose(disposing);
        }

        public override async ValueTask DisposeAsync()
        {
            if (_innerStream != null)
            {
                await _innerStream.DisposeAsync();
            }
            await base.DisposeAsync();
        }
    }
}
