using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// HTTP handler that converts Semantic Kernel multimodal messages to OpenAI standard format.
    /// Supports content arrays with image_url and text types for vision models.
    /// </summary>
    public class MultimodalHttpHandler : DelegatingHandler
    {
        private readonly ILogger<MultimodalHttpHandler> _logger;

        public MultimodalHttpHandler(HttpMessageHandler innerHandler = null, ILogger<MultimodalHttpHandler> logger = null)
            : base(innerHandler ?? new HttpClientHandler())
        {
            _logger = logger;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            // 只处理 chat/completions 请求
            if (request.RequestUri?.PathAndQuery.Contains("chat/completions") == true && 
                request.Content != null && 
                request.Method == HttpMethod.Post)
            {
                try
                {
                    var originalContent = await request.Content.ReadAsStringAsync(cancellationToken);
                    var convertedContent = ConvertToOpenAIMultimodalFormat(originalContent);
                    
                    if (convertedContent != originalContent)
                    {
                        request.Content = new StringContent(convertedContent, Encoding.UTF8, "application/json");
                        _logger?.LogDebug("[MultimodalHttpHandler] 已转换消息格式为OpenAI多模态标准格式");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "[MultimodalHttpHandler] 转换多模态消息格式时发生错误，使用原始格式");
                }
            }

            return await base.SendAsync(request, cancellationToken);
        }

        /// <summary>
        /// 将Semantic Kernel的消息格式转换为OpenAI标准的content数组格式
        /// </summary>
        /// <param name="jsonContent">原始JSON内容</param>
        /// <returns>转换后的JSON内容</returns>
        private string ConvertToOpenAIMultimodalFormat(string jsonContent)
        {
            try
            {
                var jsonDocument = JsonDocument.Parse(jsonContent);
                var root = jsonDocument.RootElement;

                if (!root.TryGetProperty("messages", out var messagesElement))
                {
                    return jsonContent;
                }

                // 检查是否是多模态模型
                bool isMultimodalModel = false;
                if (root.TryGetProperty("model", out var modelElement))
                {
                    var modelName = modelElement.GetString();
                    isMultimodalModel = modelName == "qwen-vl-ocr-latest";

                    if (isMultimodalModel)
                    {
                        _logger?.LogDebug("[MultimodalHttpHandler] 检测到多模态模型: {model}", modelName);
                    }
                }

                var messages = messagesElement.EnumerateArray().ToList();
                bool hasChanges = false;
                var convertedMessages = new List<JsonNode>();

                foreach (var message in messages)
                {
                    var convertedMessage = ConvertMessage(message, isMultimodalModel, ref hasChanges);
                    convertedMessages.Add(convertedMessage);
                }

                if (!hasChanges)
                {
                    return jsonContent;
                }

                // 重建完整的请求对象
                var newRequest = JsonNode.Parse(jsonContent);
                var messagesArray = new JsonArray();
                foreach (var message in convertedMessages)
                {
                    messagesArray.Add(message);
                }
                newRequest["messages"] = messagesArray;

                var result = newRequest.ToJsonString(new JsonSerializerOptions
                {
                    WriteIndented = false,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                _logger?.LogDebug("[MultimodalHttpHandler] 转换完成，hasChanges: {hasChanges}", hasChanges);
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[MultimodalHttpHandler] 解析JSON时发生错误");
                return jsonContent;
            }
        }

        /// <summary>
        /// 转换单个消息
        /// </summary>
        private JsonNode ConvertMessage(JsonElement message, bool isMultimodalModel, ref bool hasChanges)
        {
            var messageObj = JsonNode.Parse(message.GetRawText());

            if (!message.TryGetProperty("content", out var contentElement))
            {
                return messageObj;
            }

            // 如果content已经是数组格式，检查是否需要转换
            if (contentElement.ValueKind == JsonValueKind.Array)
            {
                var convertedContent = ConvertContentArray(contentElement, ref hasChanges);
                if (hasChanges)
                {
                    messageObj["content"] = convertedContent;
                }
                return messageObj;
            }

            // 如果content是字符串
            if (contentElement.ValueKind == JsonValueKind.String)
            {
                var contentStr = contentElement.GetString();

                // 对于多模态模型，强制将文本内容转换为数组格式
                if (isMultimodalModel && !string.IsNullOrEmpty(contentStr))
                {
                    _logger?.LogDebug("[MultimodalHttpHandler] 检测到多模态模型，将文本内容转换为数组格式: {content}", contentStr);

                    var contentArray = new JsonArray();
                    contentArray.Add(JsonNode.Parse($"{{\"type\": \"text\", \"text\": {JsonSerializer.Serialize(contentStr)}}}"));

                    messageObj["content"] = contentArray;
                    hasChanges = true;

                    return messageObj;
                }

                // 尝试解析是否为JSON格式的多模态内容
                if (IsMultimodalContent(contentStr))
                {
                    var convertedContent = ConvertStringToContentArray(contentStr, ref hasChanges);
                    if (hasChanges)
                    {
                        messageObj["content"] = convertedContent;
                    }
                }
                return messageObj;
            }

            return messageObj;
        }

        /// <summary>
        /// 转换content数组
        /// </summary>
        private JsonArray ConvertContentArray(JsonElement contentArray, ref bool hasChanges)
        {
            var convertedArray = new JsonArray();
            
            foreach (var item in contentArray.EnumerateArray())
            {
                var convertedItem = ConvertContentItem(item, ref hasChanges);
                convertedArray.Add(convertedItem);
            }
            
            return convertedArray;
        }

        /// <summary>
        /// 转换单个content项
        /// </summary>
        private JsonNode ConvertContentItem(JsonElement item, ref bool hasChanges)
        {
            var itemObj = JsonNode.Parse(item.GetRawText());

            // 检查是否是Semantic Kernel的格式需要转换
            if (item.TryGetProperty("text", out var textElement) && !item.TryGetProperty("type", out _))
            {
                // Semantic Kernel格式: { "text": "content" }
                // 转换为OpenAI格式: { "type": "text", "text": "content" }
                hasChanges = true;
                return JsonNode.Parse($"{{\"type\": \"text\", \"text\": {JsonSerializer.Serialize(textElement.GetString())}}}");
            }

            if (item.TryGetProperty("image", out var imageElement) && !item.TryGetProperty("type", out _))
            {
                // Semantic Kernel格式: { "image": "url" }
                // 转换为OpenAI格式: { "type": "image_url", "image_url": { "url": "url" } }
                hasChanges = true;
                var imageUrl = imageElement.GetString();
                return JsonNode.Parse($"{{\"type\": \"image_url\", \"image_url\": {{\"url\": {JsonSerializer.Serialize(imageUrl)}}}}}");
            }

            // 如果已经是OpenAI格式，直接返回
            return itemObj;
        }

        /// <summary>
        /// 检查字符串内容是否包含多模态信息
        /// </summary>
        private bool IsMultimodalContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            // 简单检查是否包含图片URL或base64数据
            return content.Contains("data:image/") || 
                   content.Contains("http://") || 
                   content.Contains("https://") ||
                   content.Contains("image_url") ||
                   content.Contains("\"image\":");
        }

        /// <summary>
        /// 将字符串内容转换为content数组
        /// </summary>
        private JsonArray ConvertStringToContentArray(string content, ref bool hasChanges)
        {
            try
            {
                // 尝试解析为JSON
                var parsed = JsonNode.Parse(content);
                if (parsed is JsonArray array)
                {
                    hasChanges = true;
                    return ConvertContentArray(JsonDocument.Parse(content).RootElement, ref hasChanges);
                }
            }
            catch
            {
                // 如果不是JSON，保持原样
            }

            var resultArray = new JsonArray();
            resultArray.Add(JsonNode.Parse($"{{\"type\": \"text\", \"text\": {JsonSerializer.Serialize(content)}}}"));
            return resultArray;
        }
    }
}
