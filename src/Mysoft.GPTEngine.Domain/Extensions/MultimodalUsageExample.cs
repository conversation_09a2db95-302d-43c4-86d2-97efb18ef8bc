using System;
using System.Collections.Generic;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// 多模态消息使用示例
    /// 展示如何在现有代码中使用OpenAI标准的content数组格式
    /// </summary>
    public static class MultimodalUsageExample
    {
        /// <summary>
        /// 示例1：创建包含文本和图片URL的多模态消息
        /// </summary>
        public static ChatHistory CreateTextAndImageUrlExample()
        {
            var messageCollection = new ChatMessageContentItemCollection() 
            { 
                new TextContent { Text = "这是什么" },
                new ImageContent { Uri = new Uri("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg") }
            };

            var chatHistory = new ChatHistory();
            chatHistory.AddUserMessage(messageCollection);

            return chatHistory;
        }

        /// <summary>
        /// 示例2：创建包含文本和base64图片的多模态消息
        /// </summary>
        public static ChatHistory CreateTextAndBase64ImageExample()
        {
            // 模拟base64图片数据
            var imageBytes = new byte[] { 0x89, 0x50, 0x4E, 0x47 }; // PNG文件头示例
            
            var messageCollection = new ChatMessageContentItemCollection() 
            { 
                new TextContent { Text = "请分析这张图片" },
                new ImageContent 
                { 
                    Data = new ReadOnlyMemory<byte>(imageBytes),
                    MimeType = "image/png"
                }
            };

            var chatHistory = new ChatHistory();
            chatHistory.AddUserMessage(messageCollection);

            return chatHistory;
        }

        /// <summary>
        /// 示例3：创建包含多个图片和文本的复杂多模态消息
        /// </summary>
        public static ChatHistory CreateComplexMultimodalExample()
        {
            var messageCollection = new ChatMessageContentItemCollection() 
            { 
                new TextContent { Text = "请比较这两张图片的差异：" },
                new ImageContent { Uri = new Uri("https://example.com/image1.jpg") },
                new TextContent { Text = "和" },
                new ImageContent { Uri = new Uri("https://example.com/image2.jpg") },
                new TextContent { Text = "，并告诉我它们的主要区别。" }
            };

            var chatHistory = new ChatHistory();
            chatHistory.AddUserMessage(messageCollection);

            return chatHistory;
        }

        /// <summary>
        /// 示例4：展示转换后的OpenAI格式
        /// </summary>
        public static void ShowConversionExample()
        {
            var chatHistory = CreateTextAndImageUrlExample();
            
            // 使用转换器转换为OpenAI格式
            var openAIMessages = OpenAIMultimodalMessageConverter.ConvertChatHistoryToOpenAIFormat(chatHistory);
            
            Console.WriteLine("转换后的OpenAI格式：");
            Console.WriteLine(System.Text.Json.JsonSerializer.Serialize(openAIMessages, new System.Text.Json.JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            }));
        }

        /// <summary>
        /// 示例5：在现有的ImageChatPlugin中的使用方式
        /// </summary>
        public static ChatHistory CreateImageAnalysisExample(List<DocumentDto> documents, string promptText)
        {
            var messageCollection = new ChatMessageContentItemCollection() 
            { 
                new TextContent { Text = promptText } 
            };
            
            foreach (var document in documents)
            {
                var image = new ImageContent()
                {
                    Metadata = new Dictionary<string, object?>()
                    {
                        { nameof(document.FileName), document.FileName },
                        { nameof(document.FileContent), document.FileContent }
                    }
                };
                messageCollection.Add(image);
            }

            var chatHistory = new ChatHistory();
            chatHistory.AddUserMessage(messageCollection);

            return chatHistory;
        }

        /// <summary>
        /// 文档DTO示例类（用于演示）
        /// </summary>
        public class DocumentDto
        {
            public string FileName { get; set; }
            public byte[] FileContent { get; set; }
        }

        /// <summary>
        /// 获取所有使用示例的说明
        /// </summary>
        public static string GetAllExamples()
        {
            return @"
多模态消息使用示例：

1. 文本 + 图片URL：
   messages=[{
     ""role"": ""user"",
     ""content"": [
       {""type"": ""text"", ""text"": ""这是什么""},
       {""type"": ""image_url"", ""image_url"": {""url"": ""https://example.com/image.jpg""}}
     ]
   }]

2. 文本 + Base64图片：
   messages=[{
     ""role"": ""user"",
     ""content"": [
       {""type"": ""text"", ""text"": ""请分析这张图片""},
       {""type"": ""image_url"", ""image_url"": {""url"": ""data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...""}}
     ]
   }]

3. 复杂多模态（多个图片和文本）：
   messages=[{
     ""role"": ""user"",
     ""content"": [
       {""type"": ""text"", ""text"": ""请比较这两张图片的差异：""},
       {""type"": ""image_url"", ""image_url"": {""url"": ""https://example.com/image1.jpg""}},
       {""type"": ""text"", ""text"": ""和""},
       {""type"": ""image_url"", ""image_url"": {""url"": ""https://example.com/image2.jpg""}},
       {""type"": ""text"", ""text"": ""，并告诉我它们的主要区别。""}}
     ]
   }]

现有代码兼容性：
- 现有的 ChatMessageContentItemCollection 用法保持不变
- MultimodalHttpHandler 会自动转换为OpenAI标准格式
- 支持 ImageContent 的 Uri、DataUri、Data 和 Metadata 方式
- 完全向后兼容，不影响现有功能
";
        }
    }
}
