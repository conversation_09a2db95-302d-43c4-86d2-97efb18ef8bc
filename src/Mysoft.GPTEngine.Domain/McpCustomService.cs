using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using ModelContextProtocol.Client;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.McpServerEnhancement.Services;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.UnifiedApplicationAuthentication.Client;
using Mysoft.UnifiedApplicationAuthentication.Client.Models;

namespace Mysoft.GPTEngine.Domain
{
    /// <summary>
    /// MCP自定义服务，整合McpToolWrapper和OfficialMcpToolLoader功能
    /// </summary>
    public class McpCustomService : DomainServiceBase, IDisposable
    {
        private readonly ILogger<McpCustomService> _logger;
        private readonly ILoggerFactory _loggerFactory;
        private readonly IHttpClientFactory _httpClientFactory;
        private TimeSpan _connectTimeout;
        private TimeSpan _readTimeout;
        private TimeSpan _writeTimeout;
        private TimeSpan _keepAliveInterval;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IConfigurationService _configurationService;
        private IMcpClient _mcpClient;
        private string _mcpServerUrl;
        private Dictionary<string, string> _customHeaders;
        private bool _disposed = false;

        public McpCustomService(
            IMysoftContextFactory mysoftContextFactory,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper,
            ILogger<McpCustomService> logger,
            ILoggerFactory loggerFactory,
            IHttpClientFactory httpClientFactory,
            IConfigurationService configurationService)
            : this(mysoftContextFactory, httpContextAccessor, mapper, logger, loggerFactory,
                  httpClientFactory,
                  configurationService,
                  TimeSpan.FromSeconds(30),
                  TimeSpan.FromMinutes(2),
                  TimeSpan.FromSeconds(30),
                  TimeSpan.FromSeconds(30))
        {
        }

        public McpCustomService(
            IMysoftContextFactory mysoftContextFactory,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper,
            ILogger<McpCustomService> logger,
            ILoggerFactory loggerFactory,
            IHttpClientFactory httpClientFactory,
            IConfigurationService configurationService,
            TimeSpan connectTimeout,
            TimeSpan readTimeout,
            TimeSpan writeTimeout,
            TimeSpan keepAliveInterval)
            : base(mysoftContextFactory, httpContextAccessor, mapper)
        {
            _logger = logger;
            _loggerFactory = loggerFactory;
            _httpClientFactory = httpClientFactory;
            _httpContextAccessor = httpContextAccessor;
            _configurationService = configurationService;
            _connectTimeout = connectTimeout;
            _readTimeout = readTimeout;
            _writeTimeout = writeTimeout;
            _keepAliveInterval = keepAliveInterval;
            _customHeaders = new Dictionary<string, string>();

            _logger.LogInformation("[McpCustomService] 初始化，连接超时: {connectTimeout}, 读取超时: {readTimeout}, 写入超时: {writeTimeout}, 保活间隔: {keepAlive}",
                _connectTimeout, _readTimeout, _writeTimeout, _keepAliveInterval);
        }

        /// <summary>
        /// 创建配置好的HttpClient实例
        /// </summary>
        /// <returns>配置好的HttpClient实例</returns>
        private HttpClient CreateConfiguredHttpClient()
        {
            var httpClient = _httpClientFactory.CreateClient();

            // 设置超时
            httpClient.Timeout = _readTimeout;

            _logger.LogDebug("[McpCustomService] 创建新的HttpClient实例，超时: {timeout}", _readTimeout);

            return httpClient;
        }

        /// <summary>
        /// 应用请求头到HttpClient
        /// </summary>
        /// <param name="httpClient">HttpClient实例</param>
        private void ApplyHeadersToHttpClient(HttpClient httpClient)
        {
            foreach (var header in _customHeaders)
            {
                try
                {
                    // 过滤掉内容头，只处理请求头
                    if (IsContentHeader(header.Key))
                    {
                        _logger.LogDebug("[McpCustomService] 跳过内容头 {headerName}，不设置到请求中", header.Key);
                        continue;
                    }

                    // 设置请求头
                    if (httpClient.DefaultRequestHeaders.Contains(header.Key))
                    {
                        httpClient.DefaultRequestHeaders.Remove(header.Key);
                    }
                    httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
                    _logger.LogDebug("[McpCustomService] 设置请求头: {headerName} = {headerValue}",
                        header.Key,
                        header.Key.Contains("authorization", StringComparison.OrdinalIgnoreCase) ||
                        header.Key.Contains("key", StringComparison.OrdinalIgnoreCase) ? "***" : header.Value);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "[McpCustomService] 设置请求头失败: {headerName} = {headerValue}", header.Key, header.Value);
                }
            }
        }

        /// <summary>
        /// 判断是否为内容头
        /// </summary>
        /// <param name="headerName">头名称</param>
        /// <returns>是否为内容头</returns>
        private static bool IsContentHeader(string headerName)
        {
            // 常见的内容头
            var contentHeaders = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Content-Type",
                "Content-Length",
                "Content-Encoding",
                "Content-Language",
                "Content-Location",
                "Content-MD5",
                "Content-Range",
                "Content-Disposition",
                "Expires",
                "Last-Modified"
            };

            return contentHeaders.Contains(headerName);
        }

        /// <summary>
        /// 设置自定义请求头
        /// </summary>
        /// <param name="headers">请求头字典</param>
        public void SetCustomHeaders(Dictionary<string, string> headers)
        {
            _customHeaders = headers ?? new Dictionary<string, string>();
            _logger.LogInformation("[McpCustomService] 设置自定义请求头: {headers}",
                JsonSerializer.Serialize(_customHeaders));
        }

        /// <summary>
        /// 设置超时配置
        /// </summary>
        /// <param name="timeoutSeconds">超时秒数</param>
        public void SetTimeout(int timeoutSeconds)
        {
            var timeout = TimeSpan.FromSeconds(timeoutSeconds > 0 ? timeoutSeconds : 30);
            _connectTimeout = timeout;
            _readTimeout = timeout;
            _writeTimeout = timeout;

            _logger.LogInformation("[McpCustomService] 设置超时配置: {timeoutSeconds}秒", timeoutSeconds);
        }

        /// <summary>
        /// 设置详细的超时配置
        /// </summary>
        /// <param name="connectTimeout">连接超时</param>
        /// <param name="readTimeout">读取超时</param>
        /// <param name="writeTimeout">写入超时</param>
        /// <param name="keepAliveInterval">保活间隔</param>
        public void SetTimeouts(TimeSpan connectTimeout, TimeSpan readTimeout, TimeSpan writeTimeout, TimeSpan keepAliveInterval)
        {
            _connectTimeout = connectTimeout;
            _readTimeout = readTimeout;
            _writeTimeout = writeTimeout;
            _keepAliveInterval = keepAliveInterval;

            _logger.LogInformation("[McpCustomService] 设置详细超时配置 - 连接: {connectTimeout}, 读取: {readTimeout}, 写入: {writeTimeout}, 保活: {keepAlive}",
                _connectTimeout, _readTimeout, _writeTimeout, _keepAliveInterval);
        }

        /// <summary>
        /// 从当前 HTTP 请求中提取上下文信息（使用 MysoftContext）
        /// </summary>
        /// <returns>提取的上下文 headers</returns>
        private async Task<Dictionary<string, string>> ExtractCurrentRequestHeadersAsync()
        {
            var headers = new Dictionary<string, string>();

            try
            {
                var mysoftContext = _mysoftContextFactory.GetMysoftContext();
                if (mysoftContext != null)
                {
                    // 1. 添加 tenantCode
                    if (!string.IsNullOrWhiteSpace(mysoftContext.TenantCode))
                    {
                        headers["tenantCode"] = mysoftContext.TenantCode;
                        _logger.LogDebug("[McpCustomService] 添加 tenantCode: {TenantCode}", mysoftContext.TenantCode);
                    }

                    // 2. 根据认证类型生成 auth_user_info（确保总是包含）
                    string authUserInfo = null;
                    if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 1)
                    {
                        var accessTokenContent = _httpContextAccessor.GetItem<AccessTokenContent>(nameof(AccessTokenContent));
                        authUserInfo = "{\"UserCode\":\"" + (accessTokenContent?.UserCode ?? "admin") + "\"}";
                    }
                    else if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 0)
                    {
                        authUserInfo = "{\"UserCode\":\"" + (mysoftContext.ApplicationPublisherUserCode ?? "admin") + "\"}";
                    }
                    else if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 0)
                    {
                        authUserInfo = "{\"UserCode\":\"admin\"}";
                    }
                    else if (mysoftContext.AuthType == 1)
                    {
                        authUserInfo = "{\"UserCode\":\"" + (mysoftContext.UserContext?.UserCode ?? "admin") + "\"}";
                    }
                    else
                    {
                        // 默认情况，确保总是有 auth_user_info
                        authUserInfo = "{\"UserCode\":\"admin\"}";
                    }

                    // 确保 auth_user_info 总是被添加
                    if (!string.IsNullOrWhiteSpace(authUserInfo))
                    {
                        headers["auth_user_info"] = authUserInfo;
                        headers["userinfo"] = authUserInfo; // 添加 userinfo 字段作为兼容
                        _logger.LogDebug("[McpCustomService] 添加 auth_user_info: {AuthUserInfo}", authUserInfo);
                        _logger.LogDebug("[McpCustomService] 添加 userinfo (兼容字段): {AuthUserInfo}", authUserInfo);
                    }

                    // 3. 获取 my-api-Authorization
                    var myApiAuthorizationToken = await GetMyApiAuthorization(mysoftContext);
                    if (!string.IsNullOrWhiteSpace(myApiAuthorizationToken))
                    {
                        headers["my-api-authorization"] = myApiAuthorizationToken;
                        _logger.LogDebug("[McpCustomService] 添加 my-api-authorization: ***");
                    }
                    else
                    {
                        // 如果没有 my-api-Authorization，使用 appid 和 appkey
                        headers["appid"] = GPTAppInfo.AppId;
                        headers["appkey"] = GPTAppInfo.AppKey;
                        _logger.LogDebug("[McpCustomService] 使用 appid/appkey 认证");
                    }

                    _logger.LogInformation("[McpCustomService] 从 MysoftContext 中提取到 {Count} 个上下文 headers", headers.Count);
                }
                else
                {
                    _logger.LogWarning("[McpCustomService] 无法获取 MysoftContext");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpCustomService] 从 MysoftContext 提取上下文信息时发生错误");
            }

            return headers;
        }

        /// <summary>
        /// 获取 my-api-Authorization token
        /// </summary>
        /// <param name="mysoftContext">Mysoft 上下文</param>
        /// <returns>Authorization token</returns>
        private async Task<string> GetMyApiAuthorization(MysoftContext mysoftContext)
        {
            if (mysoftContext.AuthInfo == null)
            {
                return await Task.FromResult("");
            }

            UnifiedApplicationAuthenticationOptions options = new UnifiedApplicationAuthenticationOptions
            {
                Enable = true,
                JwkSetJson = _configurationService.GetConfigurationItemByKey("appSecret"),
                ClientId = mysoftContext.AuthInfo?.ClientId ?? "4200"
            };

            UnifiedApplicationAuthenticationClient client = new UnifiedApplicationAuthenticationClient(options);
            var jwt = client.CreateToken();
            return await Task.FromResult(jwt);
        }

        /// <summary>
        /// 自动设置当前请求的上下文 headers
        /// </summary>
        private async Task SetCurrentRequestHeadersAsync()
        {
            var currentHeaders = await ExtractCurrentRequestHeadersAsync();
            if (currentHeaders.Count > 0)
            {
                // 合并当前请求的 headers 和已设置的自定义 headers
                var mergedHeaders = new Dictionary<string, string>(_customHeaders);
                foreach (var header in currentHeaders)
                {
                    mergedHeaders[header.Key] = header.Value; // 当前请求的 headers 优先级更高
                }

                _customHeaders = mergedHeaders;
                _logger.LogInformation("[McpCustomService] 已自动设置当前请求的上下文 headers，总计 {Count} 个", _customHeaders.Count);
            }
        }

        /// <summary>
        /// 连接到MCP服务器
        /// </summary>
        /// <param name="mcpServerUrl">MCP服务器URL</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> ConnectAsync(string mcpServerUrl, CancellationToken cancellationToken = default)
        {
            try
            {
                _mcpServerUrl = mcpServerUrl;
                _logger.LogInformation("[McpCustomService] 连接到MCP服务器: {serverUrl}", _mcpServerUrl);

                // 自动设置当前请求的上下文 headers
                await SetCurrentRequestHeadersAsync();

                // 配置传输选项
                var transportOptions = new SseClientTransportOptions
                {
                    Endpoint = new Uri(_mcpServerUrl)
                };

                // 创建新的HttpClient实例并配置
                var httpClient = CreateConfiguredHttpClient();

                // 应用所有 headers 到 HttpClient
                ApplyHeadersToHttpClient(httpClient);

                _logger.LogInformation("[McpCustomService] MCP传输配置 - 服务器: {serverUrl}, HttpClient超时: {httpTimeout}, 连接超时: {connectTimeout}, 读取超时: {readTimeout}, 写入超时: {writeTimeout}, 保活间隔: {keepAlive}",
                    _mcpServerUrl, httpClient.Timeout, _connectTimeout, _readTimeout, _writeTimeout, _keepAliveInterval);

                // 创建MCP客户端，使用新的HttpClient实例
                _mcpClient = await McpClientFactory.CreateAsync(
                    new SseClientTransport(transportOptions, httpClient, _loggerFactory)
                );

                _logger.LogInformation("[McpCustomService] MCP客户端创建成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpCustomService] 连接MCP服务器失败: {serverUrl}", mcpServerUrl);
                return false;
            }
        }



        /// <summary>
        /// 获取全部工具列表详细信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>工具列表</returns>
        public async Task<List<McpToolInfo>> GetAllToolsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (_mcpClient == null)
                {
                    throw new InvalidOperationException("MCP客户端未连接，请先调用ConnectAsync方法");
                }

                _logger.LogInformation("[McpCustomService] 获取MCP服务器工具列表");

                // 获取工具列表
                var tools = await _mcpClient.ListToolsAsync();
                var toolInfos = new List<McpToolInfo>();

                _logger.LogInformation("[McpCustomService] 从MCP服务器获取到 {count} 个工具", tools.Count);

                foreach (var tool in tools)
                {
                    var toolInfo = new McpToolInfo
                    {
                        Name = tool.Name,
                        Description = tool.Description ?? "无描述"
                    };

                    // 提取输入架构
                    var inputSchema = ExtractInputSchema(tool);
                    toolInfo.InputSchema = inputSchema;

                    // 解析参数信息
                    toolInfo.Parameters = ExtractParameters(inputSchema);

                    toolInfos.Add(toolInfo);

                    _logger.LogInformation("[McpCustomService] 工具: {toolName} - {description}, 参数数量: {paramCount}",
                        tool.Name, tool.Description ?? "无描述", toolInfo.Parameters.Count);
                }

                return toolInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpCustomService] 获取工具列表失败");
                throw;
            }
        }

        /// <summary>
        /// 执行指定的工具方法
        /// </summary>
        /// <param name="request">执行请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行响应</returns>
        public async Task<McpExecuteResponse> ExecuteToolAsync(McpExecuteRequest request, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var response = new McpExecuteResponse
            {
                ToolName = request.ToolName,
                ExecutedAt = DateTime.Now
            };

            try
            {
                if (_mcpClient == null)
                {
                    throw new InvalidOperationException("MCP客户端未连接，请先调用ConnectAsync方法");
                }

                _logger.LogInformation("[McpCustomService] 执行MCP工具: {toolName}, 参数: {arguments}",
                    request.ToolName, JsonSerializer.Serialize(request.Arguments));

                // 注意：临时请求头在当前架构下不支持，因为MCP客户端已经创建
                // 如果需要设置临时请求头，应该在ConnectAsync之前调用SetCustomHeaders
                if (request.Headers?.Count > 0)
                {
                    _logger.LogWarning("[McpCustomService] 检测到临时请求头，但当前架构不支持在执行时设置。请在ConnectAsync之前调用SetCustomHeaders: {headers}",
                        JsonSerializer.Serialize(request.Headers));
                }

                // 转换参数类型
                var convertedArguments = ConvertArguments(request.Arguments, request.ToolName);

                // 调用MCP工具
                var result = await _mcpClient.CallToolAsync(request.ToolName, convertedArguments);

                stopwatch.Stop();
                response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

                // 处理结果（使用与AgentSkillDomainService相同的逻辑）
                _logger.LogDebug("[McpCustomService] MCP工具返回结果详情 - IsError: {isError}, Content数量: {contentCount}",
                    result.IsError, result.Content?.Count ?? 0);

                var resultText = "";
                if (result.Content?.Count > 0)
                {
                    for (int i = 0; i < result.Content.Count; i++)
                    {
                        var content = result.Content[i];
                        _logger.LogDebug("[McpCustomService] Content[{index}] - Type: {type}, 对象类型: {objectType}",
                            i, content.Type, content.GetType().FullName);

                        if (content.Type == "text")
                        {
                            var textContent = ExtractTextContent(content);
                            _logger.LogDebug("[McpCustomService] Content[{index}] 提取的文本内容: {content}", i, textContent);

                            if (!string.IsNullOrEmpty(textContent))
                            {
                                resultText += textContent;
                            }
                            else
                            {
                                _logger.LogWarning("[McpCustomService] 无法从MCP内容中提取文本，类型: {type}, ToString: {toString}",
                                    content.GetType().FullName, content.ToString());
                                resultText += content.ToString();
                            }
                        }
                        else
                        {
                            _logger.LogDebug("[McpCustomService] Content[{index}] 非文本类型，跳过: {type}", i, content.Type);
                        }
                    }
                }

                _logger.LogDebug("[McpCustomService] 最终合并的结果文本: {resultText}", resultText);

                // 无论IsError状态如何，都返回成功（与AgentSkillDomainService保持一致）
                response.Success = true;
                response.Result = resultText;

                if (result.IsError == true)
                {
                    _logger.LogInformation("[McpCustomService] MCP工具 {toolName} 返回错误状态但继续处理，内容: {content}, 耗时: {elapsed}ms",
                        request.ToolName, resultText, stopwatch.ElapsedMilliseconds);
                }
                else
                {
                    _logger.LogInformation("[McpCustomService] MCP工具 {toolName} 执行成功，耗时: {elapsed}ms",
                        request.ToolName, stopwatch.ElapsedMilliseconds);
                }

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                response.Success = false;
                response.ErrorMessage = ex.Message;
                response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

                _logger.LogError(ex, "[McpCustomService] 执行MCP工具 {toolName} 时发生异常", request.ToolName);
                return response;
            }
        }

        /// <summary>
        /// 加载MCP工具到Kernel
        /// </summary>
        /// <param name="kernel">Kernel实例</param>
        /// <param name="mcpServerUrl">MCP服务器URL</param>
        /// <param name="pluginName">插件名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否加载成功</returns>
        public async Task<bool> LoadMcpToolsToKernelAsync(Kernel kernel, string mcpServerUrl, string pluginName = "DataMcp", CancellationToken cancellationToken = default)
        {
            try
            {
                // 连接到MCP服务器
                var connected = await ConnectAsync(mcpServerUrl, cancellationToken);
                if (!connected)
                {
                    return false;
                }

                // 获取工具列表
                var tools = await _mcpClient.ListToolsAsync();
                var kernelFunctions = new List<KernelFunction>();

                // 优化插件名称长度，确保与函数名组合后不会超长
                var optimizedPluginName = OptimizePluginName(pluginName, tools);
                _logger.LogInformation("[McpCustomService] 开始将 {count} 个MCP工具加载到Kernel，插件名: {pluginName}", tools.Count, optimizedPluginName);

                foreach (var tool in tools)
                {
                    try
                    {
                        // 提取输入架构
                        var inputSchema = ExtractInputSchema(tool);

                        // 创建带类型转换支持的包装器，考虑插件名长度
                        var wrapper = new McpToolKernelWrapper(_mcpClient, _logger, tool.Name, tool.Description ?? "", inputSchema ?? new JsonObject(), optimizedPluginName);
                        var kernelFunction = wrapper.CreateKernelFunction();

                        kernelFunctions.Add(kernelFunction);
                        _logger.LogInformation("[McpCustomService] 成功包装工具: {toolName}", tool.Name);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[McpCustomService] 包装工具 {toolName} 失败，跳过", tool.Name);
                    }
                }

                kernel.Plugins.AddFromFunctions(optimizedPluginName, kernelFunctions);

                _logger.LogInformation("[McpCustomService] 已成功加载 {count} 个 MCP 工具到 kernel 中", kernelFunctions.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpCustomService] 加载MCP工具到Kernel失败");
                return false;
            }
        }

        /// <summary>
        /// 加载指定的MCP工具到Kernel
        /// </summary>
        /// <param name="kernel">Kernel实例</param>
        /// <param name="mcpServerUrl">MCP服务器URL</param>
        /// <param name="pluginName">插件名称</param>
        /// <param name="specificToolNames">指定要加载的工具名称列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否加载成功</returns>
        public async Task<bool> LoadSpecificMcpToolsToKernelAsync(Kernel kernel, string mcpServerUrl, string pluginName, List<string> specificToolNames, CancellationToken cancellationToken = default)
        {
            try
            {
                if (specificToolNames == null || !specificToolNames.Any())
                {
                    _logger.LogWarning("[McpCustomService] 指定的工具名称列表为空，跳过加载");
                    return false;
                }

                // 连接到MCP服务器
                var connected = await ConnectAsync(mcpServerUrl, cancellationToken);
                if (!connected)
                {
                    return false;
                }

                // 获取工具列表
                var allTools = await _mcpClient.ListToolsAsync();

                // 过滤出指定的工具
                var filteredTools = allTools.Where(tool => specificToolNames.Contains(tool.Name)).ToList();

                if (!filteredTools.Any())
                {
                    _logger.LogWarning("[McpCustomService] 在MCP服务器中未找到指定的工具: {@toolNames}", specificToolNames);
                    return false;
                }

                var kernelFunctions = new List<KernelFunction>();

                // 优化插件名称长度，确保与函数名组合后不会超长
                var optimizedPluginName = OptimizePluginName(pluginName, filteredTools);
                _logger.LogInformation("[McpCustomService] 开始将 {count} 个指定的MCP工具加载到Kernel，插件名: {pluginName}", filteredTools.Count, optimizedPluginName);

                foreach (var tool in filteredTools)
                {
                    try
                    {
                        // 提取输入架构
                        var inputSchema = ExtractInputSchema(tool);

                        // 创建带类型转换支持的包装器，考虑插件名长度
                        var wrapper = new McpToolKernelWrapper(_mcpClient, _logger, tool.Name, tool.Description ?? "", inputSchema ?? new JsonObject(), optimizedPluginName);
                        var kernelFunction = wrapper.CreateKernelFunction();

                        kernelFunctions.Add(kernelFunction);
                        _logger.LogInformation("[McpCustomService] 成功包装指定工具: {toolName}", tool.Name);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[McpCustomService] 包装指定工具 {toolName} 失败，跳过", tool.Name);
                    }
                }

                if (kernelFunctions.Any())
                {
                    kernel.Plugins.AddFromFunctions(optimizedPluginName, kernelFunctions);
                    _logger.LogInformation("[McpCustomService] 已成功加载 {count} 个指定的 MCP 工具到 kernel 中", kernelFunctions.Count);
                    return true;
                }
                else
                {
                    _logger.LogWarning("[McpCustomService] 没有成功包装任何指定的工具");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpCustomService] 加载指定的MCP工具到Kernel失败");
                return false;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 优化插件名称长度，确保与函数名组合后不会超长，并确保符合Semantic Kernel命名规范
        /// </summary>
        /// <param name="pluginName">原始插件名称</param>
        /// <param name="tools">工具列表，用于分析最长的函数名</param>
        /// <returns>优化后的插件名称</returns>
        private string OptimizePluginName(string pluginName, IEnumerable<object> tools)
        {
            const int maxTotalLength = 64;
            const int separatorLength = 1; // 插件名和函数名之间的分隔符长度

            if (string.IsNullOrEmpty(pluginName))
            {
                return "Mcp";
            }

            // 首先规范化插件名称，确保符合Semantic Kernel的命名要求
            var normalizedPluginName = NormalizePluginName(pluginName);

            // 找出最长的函数名
            var maxFunctionNameLength = 0;
            foreach (var tool in tools)
            {
                try
                {
                    var toolType = tool.GetType();
                    var nameProperty = toolType.GetProperty("Name");
                    if (nameProperty?.GetValue(tool) is string toolName)
                    {
                        // 考虑函数名也会被截断的情况
                        var truncatedFunctionName = toolName.Length > maxTotalLength ? toolName.Substring(0, maxTotalLength) : toolName;
                        maxFunctionNameLength = Math.Max(maxFunctionNameLength, truncatedFunctionName.Length);
                    }
                }
                catch
                {
                    // 忽略获取名称失败的情况
                }
            }

            // 如果没有找到函数名，使用默认值
            if (maxFunctionNameLength == 0)
            {
                maxFunctionNameLength = 20; // 假设平均函数名长度
            }

            // 计算插件名的最大允许长度
            var maxPluginNameLength = maxTotalLength - maxFunctionNameLength - separatorLength;

            // 确保插件名至少有3个字符
            maxPluginNameLength = Math.Max(maxPluginNameLength, 3);

            if (normalizedPluginName.Length <= maxPluginNameLength)
            {
                _logger.LogInformation("[McpCustomService] 插件名称规范化: {originalName} -> {normalizedName}",
                    pluginName, normalizedPluginName);
                return normalizedPluginName;
            }

            // 简单截断插件名
            var optimizedName = normalizedPluginName.Length > maxPluginNameLength ? normalizedPluginName.Substring(0, maxPluginNameLength) : normalizedPluginName;

            _logger.LogInformation("[McpCustomService] 插件名称优化: {originalName} ({originalLength}) -> {optimizedName} ({optimizedLength}), 最长函数名: {maxFunctionLength}",
                pluginName, pluginName.Length, optimizedName, optimizedName.Length, maxFunctionNameLength);

            return optimizedName;
        }

        /// <summary>
        /// 规范化插件名称，确保符合Semantic Kernel的命名要求
        /// 插件名称只能包含ASCII字母、数字和下划线
        /// </summary>
        /// <param name="pluginName">原始插件名称</param>
        /// <returns>规范化后的插件名称</returns>
        private string NormalizePluginName(string pluginName)
        {
            if (string.IsNullOrEmpty(pluginName))
            {
                return "Mcp";
            }

            var normalized = new StringBuilder();
            bool lastWasUnderscore = false;

            foreach (char c in pluginName)
            {
                if (char.IsLetterOrDigit(c))
                {
                    // ASCII字母和数字直接添加
                    normalized.Append(c);
                    lastWasUnderscore = false;
                }
                else if (c == '_')
                {
                    // 下划线直接添加，但避免连续的下划线
                    if (!lastWasUnderscore)
                    {
                        normalized.Append('_');
                        lastWasUnderscore = true;
                    }
                }
                else
                {
                    // 其他字符（如连字符、点号等）替换为下划线，但避免连续的下划线
                    if (!lastWasUnderscore)
                    {
                        normalized.Append('_');
                        lastWasUnderscore = true;
                    }
                }
            }

            var result = normalized.ToString();

            // 移除开头和结尾的下划线
            result = result.Trim('_');

            // 如果结果为空或只包含下划线，使用默认名称
            if (string.IsNullOrEmpty(result) || result.All(c => c == '_'))
            {
                result = "Mcp";
            }

            // 确保首字符是字母
            if (!char.IsLetter(result[0]))
            {
                result = "Mcp" + result;
            }

            return result;
        }

        /// <summary>
        /// 提取工具的输入架构
        /// </summary>
        private JsonObject ExtractInputSchema(object tool)
        {
            try
            {
                _logger.LogDebug("[McpCustomService] 开始提取工具输入架构，工具类型: {toolType}", tool.GetType().FullName);

                // 使用反射获取InputSchema属性
                var toolType = tool.GetType();

                // 记录所有属性
                var properties = toolType.GetProperties();
                _logger.LogDebug("[McpCustomService] 工具对象属性列表: {properties}",
                    string.Join(", ", properties.Select(p => $"{p.Name}({p.PropertyType.Name})")));

                // 尝试获取JsonSchema属性（MCP客户端工具使用这个属性名）
                var jsonSchemaProperty = toolType.GetProperty("JsonSchema");
                var inputSchemaProperty = toolType.GetProperty("InputSchema");

                if (jsonSchemaProperty != null)
                {
                    var jsonSchemaValue = jsonSchemaProperty.GetValue(tool);
                    _logger.LogDebug("[McpCustomService] JsonSchema属性值: {jsonSchemaValue} (类型: {valueType})",
                        jsonSchemaValue, jsonSchemaValue?.GetType().FullName ?? "null");

                    if (jsonSchemaValue != null)
                    {
                        // 处理JsonElement类型
                        if (jsonSchemaValue is JsonElement jsonElement)
                        {
                            var json = jsonElement.GetRawText();
                            _logger.LogDebug("[McpCustomService] JsonElement原始文本: {json}", json);

                            var jsonNode = JsonNode.Parse(json);
                            var result = jsonNode?.AsObject();
                            _logger.LogDebug("[McpCustomService] 最终提取的Schema: {schema}", JsonSerializer.Serialize(result));

                            return result;
                        }
                        else
                        {
                            // 尝试序列化和反序列化来获取JsonObject
                            var json = JsonSerializer.Serialize(jsonSchemaValue);
                            _logger.LogDebug("[McpCustomService] JsonSchema序列化结果: {json}", json);

                            var jsonNode = JsonNode.Parse(json);
                            var result = jsonNode?.AsObject();
                            _logger.LogDebug("[McpCustomService] 最终提取的Schema: {schema}", JsonSerializer.Serialize(result));

                            return result;
                        }
                    }
                }
                else if (inputSchemaProperty != null)
                {
                    var inputSchemaValue = inputSchemaProperty.GetValue(tool);
                    _logger.LogDebug("[McpCustomService] InputSchema属性值: {inputSchemaValue} (类型: {valueType})",
                        inputSchemaValue, inputSchemaValue?.GetType().FullName ?? "null");

                    if (inputSchemaValue != null)
                    {
                        // 尝试序列化和反序列化来获取JsonObject
                        var json = JsonSerializer.Serialize(inputSchemaValue);
                        _logger.LogDebug("[McpCustomService] InputSchema序列化结果: {json}", json);

                        var jsonNode = JsonNode.Parse(json);
                        var result = jsonNode?.AsObject();
                        _logger.LogDebug("[McpCustomService] 最终提取的Schema: {schema}", JsonSerializer.Serialize(result));

                        return result;
                    }
                }
                else
                {
                    _logger.LogWarning("[McpCustomService] 未找到JsonSchema或InputSchema属性");
                }

                return new JsonObject();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[McpCustomService] 提取工具输入架构失败");
                return new JsonObject();
            }
        }

        /// <summary>
        /// 从输入架构中提取参数信息
        /// </summary>
        private List<McpToolParameter> ExtractParameters(JsonObject inputSchema)
        {
            var parameters = new List<McpToolParameter>();

            try
            {
                if (inputSchema == null) return parameters;

                // 查找properties节点
                if (inputSchema.TryGetPropertyValue("properties", out var propertiesNode) &&
                    propertiesNode is JsonObject properties)
                {
                    // 获取必需参数列表
                    var requiredParams = new HashSet<string>();
                    if (inputSchema.TryGetPropertyValue("required", out var requiredNode) &&
                        requiredNode is JsonArray requiredArray)
                    {
                        foreach (var item in requiredArray)
                        {
                            if (item?.GetValue<string>() is string paramName)
                            {
                                requiredParams.Add(paramName);
                            }
                        }
                    }

                    // 遍历属性
                    foreach (var property in properties)
                    {
                        var paramName = property.Key;
                        if (property.Value is JsonObject paramDef)
                        {
                            var parameter = new McpToolParameter
                            {
                                Name = paramName,
                                Required = requiredParams.Contains(paramName)
                            };

                            // 提取类型
                            if (paramDef.TryGetPropertyValue("type", out var typeNode))
                            {
                                parameter.Type = typeNode?.GetValue<string>() ?? "string";
                            }

                            // 提取描述
                            if (paramDef.TryGetPropertyValue("description", out var descNode))
                            {
                                parameter.Description = descNode?.GetValue<string>() ?? "";
                            }

                            // 提取默认值
                            if (paramDef.TryGetPropertyValue("default", out var defaultNode))
                            {
                                parameter.DefaultValue = defaultNode?.GetValue<object>();
                            }

                            parameters.Add(parameter);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[McpCustomService] 提取参数信息失败");
            }

            return parameters;
        }

        /// <summary>
        /// 转换参数类型
        /// </summary>
        private Dictionary<string, object> ConvertArguments(Dictionary<string, object> arguments, string toolName)
        {
            var convertedArguments = new Dictionary<string, object>();

            _logger.LogDebug("[McpCustomService] ConvertArguments 输入参数: {arguments}",
                JsonSerializer.Serialize(arguments));

            try
            {
                foreach (var arg in arguments)
                {
                    var value = arg.Value;

                    _logger.LogDebug("[McpCustomService] 处理参数: {key} = {value} (类型: {type})",
                        arg.Key, value, value?.GetType().Name ?? "null");

                    // 过滤掉null值和空字符串，避免传递不需要的参数给MCP工具
                    if (value == null)
                    {
                        _logger.LogDebug("[McpCustomService] 跳过null参数: {key}", arg.Key);
                        continue;
                    }

                    // 处理字符串类型
                    if (value is string strVal && string.IsNullOrEmpty(strVal))
                    {
                        _logger.LogDebug("[McpCustomService] 跳过空字符串参数: {key} = '{value}'", arg.Key, strVal);
                        continue;
                    }

                    // 处理JsonElement类型
                    if (value is JsonElement jsonElement)
                    {
                        if (jsonElement.ValueKind == JsonValueKind.String)
                        {
                            var stringValue = jsonElement.GetString();
                            if (string.IsNullOrEmpty(stringValue))
                            {
                                _logger.LogDebug("[McpCustomService] 跳过空JsonElement字符串参数: {key} = '{value}'", arg.Key, stringValue);
                                continue;
                            }
                        }
                        else if (jsonElement.ValueKind == JsonValueKind.Null)
                        {
                            _logger.LogDebug("[McpCustomService] 跳过null JsonElement参数: {key}", arg.Key);
                            continue;
                        }
                    }

                    // 基本类型转换逻辑
                    if (value is string strValue)
                    {
                        // 尝试解析为数字
                        if (int.TryParse(strValue, out var intValue))
                        {
                            convertedArguments[arg.Key] = intValue;
                        }
                        else if (double.TryParse(strValue, out var doubleValue))
                        {
                            convertedArguments[arg.Key] = doubleValue;
                        }
                        else if (bool.TryParse(strValue, out var boolValue))
                        {
                            convertedArguments[arg.Key] = boolValue;
                        }
                        else
                        {
                            convertedArguments[arg.Key] = strValue;
                        }
                    }
                    else
                    {
                        convertedArguments[arg.Key] = value;
                    }
                }

                _logger.LogDebug("[McpCustomService] 工具 {toolName} 参数转换完成: {converted}",
                    toolName, JsonSerializer.Serialize(convertedArguments));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[McpCustomService] 工具 {toolName} 参数转换失败", toolName);
                return arguments; // 返回原始参数
            }

            return convertedArguments;
        }





        /// <summary>
        /// 从MCP内容中提取文本（与McpToolKernelWrapper中的方法保持一致）
        /// </summary>
        private string ExtractTextContent(object content)
        {
            try
            {
                if (content == null) return "";

                var contentType = content.GetType();
                var textProperty = contentType.GetProperty("Text");
                if (textProperty != null)
                {
                    var textValue = textProperty.GetValue(content)?.ToString();
                    if (!string.IsNullOrEmpty(textValue))
                    {
                        return textValue;
                    }
                }

                if (contentType.Name.Contains("TextContentBlock") || contentType.Name.Contains("TextContent"))
                {
                    try
                    {
                        var json = JsonSerializer.Serialize(content);
                        var jsonDoc = JsonDocument.Parse(json);
                        if (jsonDoc.RootElement.TryGetProperty("text", out var textElement))
                        {
                            var textValue = textElement.GetString();
                            if (!string.IsNullOrEmpty(textValue))
                            {
                                return textValue;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[McpCustomService] 序列化MCP内容失败");
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpCustomService] 从MCP内容提取文本时发生错误");
                return "";
            }
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        if (_mcpClient != null)
                        {
                            // MCP客户端可能没有实现IDisposable，所以我们只是将引用设为null
                            _mcpClient = null;
                            _logger.LogInformation("[McpCustomService] MCP客户端引用已清理");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[McpCustomService] 清理MCP客户端时发生异常");
                    }
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// MCP工具Kernel包装器，用于将MCP工具集成到Semantic Kernel
    /// </summary>
    internal class McpToolKernelWrapper
    {
        private readonly IMcpClient _mcpClient;
        private readonly ILogger _logger;
        private readonly string _toolName;
        private readonly string _description;
        private readonly JsonObject _inputSchema;
        private readonly string _pluginName;

        public McpToolKernelWrapper(IMcpClient mcpClient, ILogger logger, string toolName, string description, JsonObject inputSchema, string pluginName = "")
        {
            _mcpClient = mcpClient;
            _logger = logger;
            _toolName = toolName;
            _description = description;
            _inputSchema = inputSchema;
            _pluginName = pluginName ?? "";
        }

        /// <summary>
        /// 创建KernelFunction
        /// </summary>
        public KernelFunction CreateKernelFunction()
        {
            var parameters = ExtractKernelParameters();

            // 确保工具名称不超过64个字符（考虑插件名长度）
            var truncatedName = TruncateToolName(_toolName);
            if (truncatedName != _toolName)
            {
                _logger.LogWarning("[McpToolKernelWrapper] MCP工具名称过长，已截断: {originalName} -> {truncatedName}", _toolName, truncatedName);
            }

            return KernelFunctionFactory.CreateFromMethod(
                method: (Func<KernelArguments, Task<string>>)InvokeToolAsync,
                functionName: truncatedName,
                description: _description,
                parameters: parameters
            );
        }

        /// <summary>
        /// 调用MCP工具
        /// </summary>
        [KernelFunction, Description("MCP tool wrapper")]
        public async Task<string> InvokeToolAsync(KernelArguments arguments)
        {
            try
            {
                _logger.LogInformation("[McpToolKernelWrapper] 调用MCP工具 {toolName}", _toolName);

                // 转换参数
                var convertedArguments = ConvertKernelArguments(arguments);

                _logger.LogInformation("[McpToolKernelWrapper] 转换后的参数: {arguments}", JsonSerializer.Serialize(convertedArguments));

                // 验证参数类型
                foreach (var arg in convertedArguments)
                {
                    _logger.LogDebug("[McpToolKernelWrapper] 最终参数 {key}: {value} ({type})",
                        arg.Key, arg.Value, arg.Value?.GetType().Name ?? "null");
                }

                // 调用MCP工具
                var result = await _mcpClient.CallToolAsync(_toolName, convertedArguments);

                // 添加详细的调试日志
                _logger.LogDebug("[McpToolKernelWrapper] MCP工具返回结果详情 - IsError: {isError}, Content数量: {contentCount}",
                    result.IsError, result.Content?.Count ?? 0);

                // 处理结果
                if (result.IsError == true)
                {
                    var errorMessage = "工具执行失败";
                    if (result.Content?.Count > 0)
                    {
                        var firstContent = result.Content[0];
                        if (firstContent.Type == "text")
                        {
                            errorMessage = ExtractTextContent(firstContent) ?? errorMessage;
                        }
                    }

                    _logger.LogWarning("[McpToolKernelWrapper] MCP工具 {toolName} 返回错误: {error}", _toolName, errorMessage);
                    return $"Error: {errorMessage}";
                }

                // 提取文本内容
                var resultText = "";
                if (result.Content?.Count > 0)
                {
                    for (int i = 0; i < result.Content.Count; i++)
                    {
                        var content = result.Content[i];
                        _logger.LogDebug("[McpToolKernelWrapper] Content[{index}] - Type: {type}, 对象类型: {objectType}",
                            i, content.Type, content.GetType().FullName);

                        if (content.Type == "text")
                        {
                            var textContent = ExtractTextContent(content);
                            _logger.LogDebug("[McpToolKernelWrapper] Content[{index}] 提取的文本内容: {content}", i, textContent);

                            if (!string.IsNullOrEmpty(textContent))
                            {
                                resultText += textContent;
                            }
                            else
                            {
                                _logger.LogWarning("[McpToolKernelWrapper] 无法从MCP内容中提取文本，类型: {type}, ToString: {toString}",
                                    content.GetType().FullName, content.ToString());
                                resultText += content.ToString();
                            }
                        }
                        else
                        {
                            _logger.LogDebug("[McpToolKernelWrapper] Content[{index}] 非文本类型，跳过: {type}", i, content.Type);
                        }
                    }
                }

                _logger.LogDebug("[McpToolKernelWrapper] 最终合并的结果文本: {resultText}", resultText);

                _logger.LogInformation("[McpToolKernelWrapper] MCP工具 {toolName} 执行成功", _toolName);
                return resultText;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpToolKernelWrapper] 调用MCP工具 {toolName} 失败", _toolName);
                return $"Error invoking tool {_toolName}: {ex.Message}";
            }
        }

        private List<KernelParameterMetadata> ExtractKernelParameters()
        {
            var parameters = new List<KernelParameterMetadata>();

            _logger.LogDebug("[McpToolKernelWrapper] 开始提取Kernel参数，工具: {toolName}", _toolName);
            _logger.LogDebug("[McpToolKernelWrapper] 输入Schema: {schema}", JsonSerializer.Serialize(_inputSchema));

            try
            {
                // 使用与ConvertKernelArguments相同的访问方式
                if (_inputSchema?["properties"] is JsonObject properties)
                {
                    _logger.LogDebug("[McpToolKernelWrapper] 找到properties节点，包含 {count} 个属性", properties.Count);

                    var requiredParams = new HashSet<string>();
                    if (_inputSchema["required"] is JsonArray requiredArray)
                    {
                        foreach (var item in requiredArray)
                        {
                            if (item?.GetValue<string>() is string paramName)
                            {
                                requiredParams.Add(paramName);
                                _logger.LogDebug("[McpToolKernelWrapper] 必需参数: {paramName}", paramName);
                            }
                        }
                    }

                    foreach (var property in properties)
                    {
                        var paramName = property.Key;
                        if (property.Value is JsonObject paramDef)
                        {
                            var description = "";
                            var paramType = typeof(string);
                            var defaultValue = "";

                            if (paramDef["description"]?.GetValue<string>() is string desc)
                            {
                                description = desc;
                            }

                            if (paramDef["type"]?.GetValue<string>() is string typeStr)
                            {
                                paramType = typeStr switch
                                {
                                    "integer" => typeof(int),
                                    "number" => typeof(double),
                                    "boolean" => typeof(bool),
                                    _ => typeof(string)
                                };
                            }

                            if (paramDef["default"]?.ToString() is string defaultStr)
                            {
                                defaultValue = defaultStr;
                            }

                            var parameter = new KernelParameterMetadata(paramName)
                            {
                                Description = description,
                                ParameterType = paramType,
                                IsRequired = requiredParams.Contains(paramName),
                                DefaultValue = string.IsNullOrEmpty(defaultValue) ? null : defaultValue
                            };

                            parameters.Add(parameter);
                            _logger.LogDebug("[McpToolKernelWrapper] 添加参数: {paramName} ({paramType}), 必需: {isRequired}, 描述: {description}",
                                paramName, paramType.Name, parameter.IsRequired, description);
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("[McpToolKernelWrapper] 未找到properties节点或格式不正确");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[McpToolKernelWrapper] 提取Kernel参数失败");
            }

            _logger.LogInformation("[McpToolKernelWrapper] 工具 {toolName} 提取到 {count} 个参数", _toolName, parameters.Count);
            return parameters;
        }

        private Dictionary<string, object> ConvertKernelArguments(KernelArguments arguments)
        {
            var convertedArguments = new Dictionary<string, object>();

            _logger.LogInformation("[McpToolKernelWrapper] 开始转换参数，工具: {toolName}, 输入参数: {arguments}",
                _toolName, JsonSerializer.Serialize(arguments.ToDictionary(kv => kv.Key, kv => kv.Value)));

            try
            {
                _logger.LogDebug("[McpToolKernelWrapper] Schema内容: {schema}", JsonSerializer.Serialize(_inputSchema));

                // 使用与McpToolWrapper相同的schema访问方式
                if (_inputSchema?["properties"] is JsonObject properties)
                {
                    _logger.LogDebug("[McpToolKernelWrapper] 找到properties，包含 {count} 个属性", properties.Count);

                    foreach (var property in properties)
                    {
                        var paramName = property.Key;
                        var paramInfo = property.Value?.AsObject();

                        _logger.LogDebug("[McpToolKernelWrapper] 检查schema参数: {paramName}, 参数信息: {paramInfo}",
                            paramName, JsonSerializer.Serialize(paramInfo));

                        // 尝试精确匹配参数名
                        if (paramInfo != null && arguments.TryGetValue(paramName, out var value))
                        {
                            var paramType = paramInfo["type"]?.GetValue<string>() ?? "string";
                            var convertedValue = ConvertArgumentValue(value, paramType);
                            convertedArguments[paramName] = convertedValue;

                            _logger.LogDebug("[McpToolKernelWrapper] 已转换参数 {ParamName}: {OriginalValue} ({OriginalType}) -> {ConvertedValue} ({TargetType})",
                                paramName, value, value?.GetType().Name, convertedValue, paramType);
                        }
                        else if (paramInfo != null)
                        {
                            // 尝试大小写不敏感匹配
                            var matchedArg = arguments.FirstOrDefault(arg =>
                                string.Equals(arg.Key, paramName, StringComparison.OrdinalIgnoreCase));

                            if (!matchedArg.Equals(default(KeyValuePair<string, object>)))
                            {
                                var paramType = paramInfo["type"]?.GetValue<string>() ?? "string";
                                var convertedValue = ConvertArgumentValue(matchedArg.Value, paramType);
                                convertedArguments[paramName] = convertedValue;

                                _logger.LogDebug("[McpToolKernelWrapper] 大小写不敏感匹配参数: {originalKey} -> {schemaKey} = {value}",
                                    matchedArg.Key, paramName, matchedArg.Value);
                            }
                            else
                            {
                                _logger.LogDebug("[McpToolKernelWrapper] 未找到匹配的参数: {paramName}", paramName);
                            }
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("[McpToolKernelWrapper] 未找到properties节点或schema为空");
                }

                // 添加schema中未包含的其他参数
                foreach (var arg in arguments)
                {
                    if (!convertedArguments.ContainsKey(arg.Key))
                    {
                        convertedArguments[arg.Key] = arg.Value;
                        _logger.LogDebug("[McpToolKernelWrapper] 添加额外参数: {key} = {value}", arg.Key, arg.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[McpToolKernelWrapper] 转换工具 {ToolName} 的参数失败，使用原始参数", _toolName);

                // 回退：使用原始参数（与McpToolWrapper相同的回退逻辑）
                convertedArguments.Clear();
                foreach (var arg in arguments)
                {
                    convertedArguments[arg.Key] = arg.Value;
                }
            }

            _logger.LogInformation("[McpToolKernelWrapper] 参数转换完成，输出参数: {convertedArguments}",
                JsonSerializer.Serialize(convertedArguments));

            return convertedArguments;
        }

        private object ConvertArgumentValue(object value, string targetType)
        {
            if (value == null) return null;

            _logger.LogDebug("[McpToolKernelWrapper] 开始转换参数值: {Value} ({ValueType}) -> {TargetType}",
                value, value?.GetType().Name, targetType);

            try
            {
                object convertedValue;

                // 使用与McpToolWrapper完全相同的转换逻辑，并添加对array类型的支持
                convertedValue = targetType switch
                {
                    "array" => ConvertToArray(value),
                    "number" => Convert.ToDouble(value),
                    "float" => Convert.ToSingle(value),
                    "double" => Convert.ToDouble(value),
                    "integer" => Convert.ToInt32(value),
                    "int" => Convert.ToInt32(value),
                    "int32" => Convert.ToInt32(value),
                    "int64" => Convert.ToInt64(value),
                    "long" => Convert.ToInt64(value),
                    "boolean" => Convert.ToBoolean(value),
                    "bool" => Convert.ToBoolean(value),
                    "string" => value.ToString(),
                    _ => value
                };

                _logger.LogDebug("[McpToolKernelWrapper] 参数值转换成功: {OriginalValue} -> {ConvertedValue} ({ConvertedType})",
                    value, convertedValue, convertedValue?.GetType().Name);

                return convertedValue;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[McpToolKernelWrapper] 将值 {Value} ({ValueType}) 转换为类型 {TargetType} 失败，使用原始值",
                    value, value?.GetType().Name, targetType);
                return value;
            }
        }

        /// <summary>
        /// 将值转换为数组类型
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <returns>转换后的数组对象</returns>
        private object ConvertToArray(object value)
        {
            if (value == null) return null;

            _logger.LogDebug("[McpToolKernelWrapper] 开始转换数组参数: {Value} ({ValueType})",
                value, value?.GetType().Name);

            try
            {
                // 如果已经是数组类型，直接返回
                if (value is Array || value is IEnumerable<object>)
                {
                    _logger.LogDebug("[McpToolKernelWrapper] 值已经是数组类型，直接返回");
                    return value;
                }

                // 如果是字符串，尝试解析为JSON数组
                if (value is string strValue)
                {
                    if (string.IsNullOrWhiteSpace(strValue))
                    {
                        _logger.LogDebug("[McpToolKernelWrapper] 空字符串，返回空数组");
                        return new object[0];
                    }

                    // 尝试解析JSON字符串
                    try
                    {
                        // 首先尝试解析为JsonElement数组以保持对象结构
                        using var document = JsonDocument.Parse(strValue);
                        if (document.RootElement.ValueKind == JsonValueKind.Array)
                        {
                            var jsonArray = new List<object>();
                            foreach (var element in document.RootElement.EnumerateArray())
                            {
                                // 如果是对象，保持为JsonElement以保留结构
                                if (element.ValueKind == JsonValueKind.Object)
                                {
                                    // 将JsonElement转换为Dictionary以便MCP工具处理
                                    var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(element.GetRawText());
                                    jsonArray.Add(dict);
                                }
                                else
                                {
                                    // 对于基本类型，直接获取值
                                    jsonArray.Add(GetJsonElementValue(element));
                                }
                            }
                            _logger.LogDebug("[McpToolKernelWrapper] 成功解析JSON字符串为对象数组，元素数量: {Count}", jsonArray.Count);
                            return jsonArray.ToArray();
                        }
                        else
                        {
                            // 如果不是数组，尝试作为单个元素处理
                            var singleValue = JsonSerializer.Deserialize<object>(strValue);
                            _logger.LogDebug("[McpToolKernelWrapper] 解析为单个对象，包装为数组");
                            return new[] { singleValue };
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger.LogWarning(jsonEx, "[McpToolKernelWrapper] 解析JSON字符串失败，尝试其他方式: {Value}", strValue);

                        // 如果JSON解析失败，尝试按逗号分割
                        if (strValue.Contains(','))
                        {
                            var parts = strValue.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                               .Select(s => s.Trim())
                                               .ToArray();
                            _logger.LogDebug("[McpToolKernelWrapper] 按逗号分割字符串，得到 {Count} 个元素", parts.Length);
                            return parts;
                        }

                        // 如果都失败了，将字符串作为单元素数组
                        _logger.LogDebug("[McpToolKernelWrapper] 将字符串作为单元素数组处理");
                        return new[] { strValue };
                    }
                }

                // 如果是JsonElement类型
                if (value is JsonElement jsonElement)
                {
                    if (jsonElement.ValueKind == JsonValueKind.Array)
                    {
                        var array = jsonElement.EnumerateArray().Select(e => e.GetRawText()).ToArray();
                        _logger.LogDebug("[McpToolKernelWrapper] 从JsonElement解析数组，元素数量: {Count}", array.Length);
                        return array;
                    }
                    else if (jsonElement.ValueKind == JsonValueKind.String)
                    {
                        var stringValue = jsonElement.GetString();
                        return ConvertToArray(stringValue); // 递归处理字符串
                    }
                }

                // 其他情况，尝试将单个值包装为数组
                _logger.LogDebug("[McpToolKernelWrapper] 将单个值包装为数组");
                return new[] { value };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpToolKernelWrapper] 转换数组参数时发生错误，返回原始值");
                return value;
            }
        }

        /// <summary>
        /// 从JsonElement中提取对应的.NET类型值
        /// </summary>
        /// <param name="element">JsonElement</param>
        /// <returns>对应的.NET类型值</returns>
        private object GetJsonElementValue(JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.String => element.GetString(),
                JsonValueKind.Number => element.TryGetInt32(out var intVal) ? intVal : element.GetDouble(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null,
                JsonValueKind.Object => JsonSerializer.Deserialize<Dictionary<string, object>>(element.GetRawText()),
                JsonValueKind.Array => element.EnumerateArray().Select(GetJsonElementValue).ToArray(),
                _ => element.GetRawText()
            };
        }

        private string ExtractTextContent(object content)
        {
            try
            {
                if (content == null) return "";

                var contentType = content.GetType();
                var textProperty = contentType.GetProperty("Text");
                if (textProperty != null)
                {
                    var textValue = textProperty.GetValue(content)?.ToString();
                    if (!string.IsNullOrEmpty(textValue))
                    {
                        return textValue;
                    }
                }

                if (contentType.Name.Contains("TextContentBlock") || contentType.Name.Contains("TextContent"))
                {
                    try
                    {
                        var json = JsonSerializer.Serialize(content);
                        var jsonDoc = JsonDocument.Parse(json);
                        if (jsonDoc.RootElement.TryGetProperty("text", out var textElement))
                        {
                            var textValue = textElement.GetString();
                            if (!string.IsNullOrEmpty(textValue))
                            {
                                return textValue;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[McpToolKernelWrapper] 序列化MCP内容失败");
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[McpToolKernelWrapper] 从MCP内容提取文本时发生错误");
                return "";
            }
        }

        private string TruncateToolName(string toolName)
        {
            const int maxTotalLength = 64;
            const int separatorLength = 1; // 插件名和函数名之间的分隔符长度

            // 计算函数名的最大允许长度（考虑插件名）
            var maxFunctionNameLength = maxTotalLength - _pluginName.Length - separatorLength;

            // 确保函数名至少有3个字符
            maxFunctionNameLength = Math.Max(maxFunctionNameLength, 3);

            if (string.IsNullOrEmpty(toolName) || toolName.Length <= maxFunctionNameLength)
            {
                return toolName;
            }

            // 智能截断：保留重要部分
            var truncated = SmartTruncate(toolName, maxFunctionNameLength);

            _logger.LogInformation("[McpToolKernelWrapper] 工具名称智能截断: {originalName} ({originalLength}) -> {truncatedName} ({truncatedLength}), 插件名: {pluginName} ({pluginLength})",
                toolName, toolName.Length, truncated, truncated.Length, _pluginName, _pluginName.Length);

            return truncated;
        }

        /// <summary>
        /// 智能截断工具名称，尽量保留有意义的部分
        /// </summary>
        /// <param name="toolName">原始工具名称</param>
        /// <param name="maxLength">最大长度</param>
        /// <returns>截断后的工具名称</returns>
        private string SmartTruncate(string toolName, int maxLength)
        {
            if (string.IsNullOrEmpty(toolName) || toolName.Length <= maxLength)
            {
                return toolName;
            }

            // 策略1: 如果包含下划线或连字符，尝试保留最后几个有意义的部分
            var separators = new[] { '_', '-', '.', '/' };
            foreach (var separator in separators)
            {
                if (toolName.Contains(separator))
                {
                    var parts = toolName.Split(separator);
                    if (parts.Length > 1)
                    {
                        // 从后往前组合，直到超过长度限制
                        var result = "";
                        for (int i = parts.Length - 1; i >= 0; i--)
                        {
                            var candidate = i == parts.Length - 1 ? parts[i] : parts[i] + separator + result;
                            if (candidate.Length <= maxLength)
                            {
                                result = candidate;
                            }
                            else
                            {
                                break;
                            }
                        }

                        if (!string.IsNullOrEmpty(result) && result.Length <= maxLength)
                        {
                            return result;
                        }
                    }
                }
            }

            // 策略2: 如果是驼峰命名，尝试保留重要的单词
            if (HasCamelCase(toolName))
            {
                var words = SplitCamelCase(toolName);
                if (words.Count > 1)
                {
                    // 优先保留动词（通常在前面）和最后的名词
                    var result = "";
                    var importantWords = new List<string>();

                    // 保留第一个单词（通常是动词）
                    if (words.Count > 0) importantWords.Add(words[0]);

                    // 保留最后一个单词（通常是主要名词）
                    if (words.Count > 1 && words[0] != words[words.Count - 1])
                        importantWords.Add(words[words.Count - 1]);

                    // 如果还有空间，添加中间的重要单词
                    for (int i = 1; i < words.Count - 1; i++)
                    {
                        var candidate = string.Join("", importantWords) + words[i];
                        if (candidate.Length <= maxLength)
                        {
                            importantWords.Insert(importantWords.Count - 1, words[i]);
                        }
                        else
                        {
                            break;
                        }
                    }

                    result = string.Join("", importantWords);
                    if (result.Length <= maxLength)
                    {
                        return result;
                    }
                }
            }

            // 策略3: 简单截断，但尝试在单词边界处截断
            var truncated = toolName.Substring(0, Math.Min(maxLength, toolName.Length));

            // 如果截断点不是在单词边界，尝试往前找到单词边界
            if (truncated.Length < toolName.Length)
            {
                var lastSpace = truncated.LastIndexOfAny(new[] { ' ', '_', '-', '.' });
                if (lastSpace > maxLength / 2) // 只有在不会丢失太多内容时才在单词边界截断
                {
                    truncated = truncated.Substring(0, lastSpace);
                }
            }

            return truncated;
        }

        /// <summary>
        /// 检查字符串是否包含驼峰命名
        /// </summary>
        private bool HasCamelCase(string text)
        {
            if (string.IsNullOrEmpty(text)) return false;

            bool hasLower = false;
            bool hasUpper = false;

            foreach (char c in text)
            {
                if (char.IsLower(c)) hasLower = true;
                if (char.IsUpper(c)) hasUpper = true;
                if (hasLower && hasUpper) return true;
            }

            return false;
        }

        /// <summary>
        /// 将驼峰命名的字符串分割成单词
        /// </summary>
        private List<string> SplitCamelCase(string text)
        {
            var words = new List<string>();
            if (string.IsNullOrEmpty(text)) return words;

            var currentWord = "";

            for (int i = 0; i < text.Length; i++)
            {
                char c = text[i];

                if (char.IsUpper(c) && currentWord.Length > 0)
                {
                    words.Add(currentWord);
                    currentWord = c.ToString();
                }
                else
                {
                    currentWord += c;
                }
            }

            if (currentWord.Length > 0)
            {
                words.Add(currentWord);
            }

            return words;
        }
    }
}
