using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.TextExtractDecode;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using System;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain
{
    /// <summary>
    /// 文件处理工厂
    /// </summary>
    public static class DocumentDecoderFactory
    {
        private static Dictionary<string, Type> decoders = new Dictionary<string, Type> {
        { "word", typeof(MsWordDecoder) },
        { "pdf", typeof(PdfDecoder) },
        { "txt", typeof(TextDecoder) },
        { "markdown", typeof(MarkdownDecoder) },
        { "html", typeof(TextDecoder) },
        { "excel", typeof(MsExcelMetadataDecoder) },
        { "image", typeof(ImageDecoder) }
    };

        public static IDocumentDecoder GetDocumentDecoder(string documentType, Kernel kernel, MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper)
        {
            if (decoders.TryGetValue(documentType, out Type decoderType))
            {
                // 参数列表
                object[] parameters = new object[] { kernel, mysoftApiDomainService, mysoftContextFactory, httpContextAccessor, mapper };
                // 参数列表
                IDocumentDecoder decoder = (IDocumentDecoder)Activator.CreateInstance(decoderType, parameters);
                return decoder;
            }
            else
            {
                throw new InvalidOperationException($"Unsupported document type: {documentType}");
            }
        }
    }
}
