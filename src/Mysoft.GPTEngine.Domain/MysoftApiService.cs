using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using DocumentFormat.OpenXml.InkML;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.UnifiedApplicationAuthentication.Client;
using Mysoft.UnifiedApplicationAuthentication.Client.Models;

namespace Mysoft.GPTEngine.Domain
{
    public class MysoftApiService : ApiDomainService, IMysoftApiService
    {
        private HttpClient _httpClient;
        private IHttpContextAccessor _httpContextAccessor;
        private const string _mysoftCookies = "Mysoft-Cookies";
        private readonly IMysoftContextFactory _mysoftContextFactory;

        public MysoftApiService(IMysoftContextFactory mysoftContextFactory, IHttpClientFactory clientFactory, IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor):base(configurationService)
        {
            _httpContextAccessor = httpContextAccessor;
            _httpClient = clientFactory.CreateClient("Mysoft");
            _httpClient.Timeout = TimeSpan.FromMinutes(30);
            _mysoftContextFactory = mysoftContextFactory;
        }
        public async Task<string> AuthCallBack(HttpHeaders heades, string uri, CancellationToken cancellationToken = default)
        {
            // var httpCookies = _httpContextAccessor.HttpContext.Request.Headers[_mysoftCookies];
            // if (string.IsNullOrWhiteSpace(httpCookies) == false)
            // {
            //     var cookies = await CookieStrHandler(httpCookies);
            //     if (cookies?.Count == 0)
            //     {
            //         return await Task.FromResult(uri);
            //     }
            //     var myApiAuthorizationCookie = cookies?.FirstOrDefault(x => x != null && x.Name != null && x.Name.Equals("my-api-Authorization"));
            //     if (myApiAuthorizationCookie != null)
            //     {
            //         heades.Add("my-api-Authorization", myApiAuthorizationCookie.Value);
            //     }
            //     heades.Add("Cookie", httpCookies.ToString());
            // }
            var mysoftContext = _mysoftContextFactory.GetMysoftContext();
            var myApiAuthorizationToken = await GetMyApiAuthorization(mysoftContext);
            if (string.IsNullOrWhiteSpace(myApiAuthorizationToken) == false)
            {
                heades.Add("my-api-Authorization", myApiAuthorizationToken);
            }
            else
            {
                heades.Add("appid", GPTAppInfo.AppId);
                heades.Add("appkey", GPTAppInfo.AppKey);
            }

            // 添加用户信息，确保 auth_user_info 和 userinfo 都存在
            var userInfo = GetUserInfo(mysoftContext);
            if (!string.IsNullOrEmpty(userInfo))
            {
                heades.Add("auth_user_info", userInfo);
                heades.Add("userinfo", userInfo);
            }

            heades.Add("tenantCode", mysoftContext.TenantCode);
            return await Task.FromResult(uri);
        }

        private string GetUserInfo(MysoftContext mysoftContext)
        {
            if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 1)
            {
                AccessTokenContent accessTokenContent = _httpContextAccessor.GetItem<AccessTokenContent>(nameof(AccessTokenContent));
                return "{\"UserCode\":\"" + (accessTokenContent == null ? "" : accessTokenContent.UserCode) + "\"}";
            }
            if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 0)
            {
                return "{\"UserCode\":\"" + mysoftContext.ApplicationPublisherUserCode + "\"}";
            }
            if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 0)
            {
                return "{\"UserCode\":\"admin\"}";
            }
            if (mysoftContext.AuthType == 1)
            {
                return "{\"UserCode\":\"" + mysoftContext.UserContext.UserCode + "\"}";
            }
            return "";
        }

        public async Task<string> PostAsync(string uri, string body, CancellationToken cancellationToken = default)
        {
            var httpContent = new StringContent(body, Encoding.UTF8, "application/json");
            uri = await AuthCallBack(httpContent.Headers, uri);
            return await this.SendRequestAsync(_httpClient, uri, HttpMethod.Post, httpContent, cancellationToken);
        }
        
        public async Task<string> GetAsync(string uri, Dictionary<string,  object> requestParams, CancellationToken cancellationToken = default)
        {
            
            // 解析基础 URL
            Uri baseUri = new Uri(uri);

            // 获取现有的查询字符串
            string existingQuery = baseUri.Query.TrimStart('?');

            // 拼接新的查询字符串
            string newQuery = string.Join("&", requestParams.Select(kvp => $"{kvp.Key}={HttpUtility.UrlEncode(kvp.Value == null ? "" : kvp.Value.ToString())}"));

            // 如果已有查询字符串，则合并
            string finalQuery = string.IsNullOrEmpty(existingQuery) ? newQuery : $"{existingQuery}&{newQuery}";

            // 构建最终的 URL
            string portPart = baseUri.Port > 0 ? $":{baseUri.Port}" : "";
            string url = $"{baseUri.Scheme}://{baseUri.Host}{portPart}{baseUri.PathAndQuery.TrimStart('?')}{(string.IsNullOrEmpty(baseUri.PathAndQuery.TrimStart('?')) ? "" : "?")}{finalQuery}";
            
            var httpContent = new StringContent("");
            
            url = await AuthCallBack(httpContent.Headers, url);
            return await this.SendRequestAsync(_httpClient, url, HttpMethod.Get, httpContent, cancellationToken);
        }
    
    }
    public class ApiDomainService
    {
        private readonly IConfigurationService _configurationService;

        public ApiDomainService (IConfigurationService configurationService)
        {
            _configurationService = configurationService;
        }

        public async Task<string> SendRequestAsync(HttpClient httpClient, string uri, HttpMethod method, HttpContent requestContent, CancellationToken cancellationToken, AuthenticationHeaderValue authenticationHeaderValue=null)
        {
            using var request = new HttpRequestMessage(method, uri) { Content = requestContent };
            if (authenticationHeaderValue != null)
            {
                request.Headers.Authorization = authenticationHeaderValue;
            }
            using var response = await httpClient.SendWithSuccessCheckAsync(request, cancellationToken).ConfigureAwait(false);
            return await response.Content.ReadAsStringWithExceptionMappingAsync().ConfigureAwait(false);
        }
    
        public async Task<List<Cookie>> CookieStrHandler(string cookieStr)
        {
            CookieContainer cookieContainer = new CookieContainer();
            List<Cookie> cookies = new List<Cookie>();
            string[] cookieStrings = cookieStr.Split(';');
            foreach (string cookie in cookieStrings)
            {
                if (string.IsNullOrWhiteSpace(cookie)) continue;
                string[] cookiePair = cookie.Split('=');
                Cookie newCookie = new Cookie(cookiePair[0].Trim(), cookiePair[1].Trim());

                cookies.Add(newCookie);
            }
            return await Task.FromResult(cookies);
        }

        public async Task<String> GetMyApiAuthorization(MysoftContext mysoftContext)
        {
            if (mysoftContext.AuthInfo == null)
            {
                return await Task.FromResult("");
            }

            UnifiedApplicationAuthenticationOptions _options = new UnifiedApplicationAuthenticationOptions
            {
                Enable = true,
                JwkSetJson = _configurationService.GetConfigurationItemByKey("appSecret"),
                ClientId = mysoftContext.AuthInfo == null ? "4200" : mysoftContext.AuthInfo.ClientId
            };
 
            UnifiedApplicationAuthenticationClient _client = new UnifiedApplicationAuthenticationClient(_options);
            UnifiedApplicationAuthenticationProvider _provider = new UnifiedApplicationAuthenticationProvider(_client);
            var jwt = _client.CreateToken();
            return await Task.FromResult(jwt);
        }
        
    }
}
