using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Shared.Extensions
{
    public static class StringExtnsion
    {
        /// <summary>
        /// 去掉字符串开头的指定字符。
        /// </summary>
        /// <param name="source">源字符串。</param>
        /// <param name="charToRemove">要移除的字符。</param>
        /// <returns>处理后的字符串。</returns>
        public static string RemoveStart(this string source, char charToRemove)
        {
            if (string.IsNullOrEmpty(source) || !source.StartsWith(charToRemove.ToString()))
            {
                return source;
            }

            return source.Substring(1);
        }

        /// <summary>
        /// 去掉字符串结尾的指定字符。
        /// </summary>
        /// <param name="source">源字符串。</param>
        /// <param name="charToRemove">要移除的字符。</param>
        /// <returns>处理后的字符串。</returns>
        public static string RemoveEnd(this string source, char charToRemove)
        {
            if (string.IsNullOrEmpty(source) || !source.EndsWith(charToRemove.ToString()))
            {
                return source;
            }

            return source.Substring(0, source.Length - 1);
        }

        /// <summary>
        /// 去掉字符串开头和结尾的指定字符。
        /// </summary>
        /// <param name="source">源字符串。</param>
        /// <param name="charToRemove">要移除的字符。</param>
        /// <returns>处理后的字符串。</returns>
        public static string RemoveStartAndEnd(this string source, char charToRemove)
        {
            return source.RemoveStart(charToRemove).RemoveEnd(charToRemove);
        }
    }
}
