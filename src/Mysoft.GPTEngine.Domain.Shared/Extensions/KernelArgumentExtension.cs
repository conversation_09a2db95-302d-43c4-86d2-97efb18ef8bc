using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System.Linq;

namespace Mysoft.GPTEngine.Domain.Shared.Extensions
{
    public static class KernelArgumentExtension
    {
        public static void AddItem(this IHttpContextAccessor httpContextAccessor, string key, object value)
        {
            if (httpContextAccessor.HttpContext.Items.ContainsKey(key))
            {
                httpContextAccessor.HttpContext.Items[key] = value;
            }
            else
            {
                httpContextAccessor.HttpContext.Items.Add(key, value);
            }
        }
        public static T GetItem<T>(this IHttpContextAccessor httpContextAccessor, string key)
        {
            return (T)httpContextAccessor.HttpContext.Items[key] ?? default(T);
        }

        public static void AddOutArgument(this IHttpContextAccessor httpContextAccessor, string key, object value)
        {
            var chatRunDto = httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));
            var node = chatRunDto.Nodes.FirstOrDefault(x => x.Id == chatRunDto.Chat.CurrentNodeGUID.ToString());
            httpContextAccessor.AddArgument(string.Format(KernelArgumentsConstant.NodeOutput, node.Code, key), value);
        }
        public static void AddArgument(this IHttpContextAccessor httpContextAccessor, string key, object value)
        {
            var chatRunDto = httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));
            if (chatRunDto.ChatArguments.ContainsName(key))
            {
                chatRunDto.ChatArguments[key] = value;
            }
            else
            {
                chatRunDto.ChatArguments.Add(key, value);
            }
        }
        public static T GetArgumentValue<T>(this IHttpContextAccessor httpContextAccessor, string key)
        {
            var chatRunDto = httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));

            if (chatRunDto.ChatArguments.ContainsName(key))
            {
                return (T)chatRunDto.ChatArguments[key];
            }
            return default(T);
        }
        public static KernelArguments GetKernelArguments(this IHttpContextAccessor httpContextAccessor)
        {
            return (httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto)).ChatArguments) ?? new KernelArguments();
        }
    }
}