using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.SemanticKernel;

namespace Mysoft.GPTEngine.Domain.Shared
{
    public interface IKnowledgeDomainService
    {
        Task<ImageUrlDto> GetKnowledgeFileSections(List<Guid> sectionGuids);

        Task<List<KnowledgeQuestionDto>> GetQuestionByQuestionGUID(List<Guid> questionGuids);

        Task<string> GetDownloadUrl(Guid documentGUID);

        Task<List<DocumentInfoBaseDto>> GetDocumentInfo(List<Guid> documentGUIDs, int isReturnUrl = 1);

        Task<Dictionary<string, ReadOnlyMemory<float>>> GetKnowledgeQueryEmbeddingMap(string knowledgeCodes,
            string query, Kernel _kernel);
        
        Task<List<QueryResultDto>> GetQueryTopResult(string knowledgeCodes, string query, int limit, double minScore);
        
        Task<List<QueryResultDto>> GetQueryTopResult(Dictionary<string, ReadOnlyMemory<float>> knowledgeQueryEmbeddingMap, int limit, double minScore);

        Task<string> GetEmbeddingModelCodeByKnowledgeCode(string knowledgeCode);
        
        Task<List<KnowledgeEmbeddingsResultDto>> GetEmbeddingsText(string modelInstanceCode, List<string> input);
        
    }
}
