using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class PageCardDto
    {
        public string Type { get; set; } = "card";

        [JsonPropertyName("next")]
        public string Next { get; set; } = string.Empty;
        public PageParamValueDto Data { get; set; } = new PageParamValueDto();

    }

    public class PageParamValueDto
    {
        public string Id { get; set; }

        public List<PagePropDto> props { get; set; } = new List<PagePropDto>();
        public List<ParamDto> Inputs { get; set; }
        public List<ParamDto> Outputs { get; set; }
        public string NodeCode { get; set; }
    }

    public class PagePropDto
    {
        public string Name { get; set; }

        public Object Value { get; set; }
    }

}
