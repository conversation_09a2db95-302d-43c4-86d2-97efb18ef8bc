using DocumentFormat.OpenXml.Drawing;
using Google.Protobuf;
using Mysoft.GPTEngine.Common.Rabbitmq;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ChatMessageLogDto
    {
        public double Duration => Math.Round(this.ChatMessageNodeLogDtos.Sum(x => x.Duration), 3);
        public double FirstRespDuration { get; set; }
        public DateTime StartTime => this.ChatMessageNodeLogDtos.FirstOrDefault()?.StartTime ?? TimeZoneUtility.LocalNow();
        public DateTime EndTime => this.ChatMessageNodeLogDtos.LastOrDefault()?.EndTime ?? TimeZoneUtility.LocalNow();
        public string Status => this.ChatMessageNodeLogDtos.LastOrDefault()?.Status;
        public string Message => this.ChatMessageNodeLogDtos.LastOrDefault()?.Message;
        public int PromptTokens => this.ChatMessageNodeLogDtos.Sum(x => x.PromptTokens);
        public int CompleteTokens => this.ChatMessageNodeLogDtos.Sum(x => x.CompleteTokens);
        public int Tokens => PromptTokens + CompleteTokens;
        public string Inputs => this.ChatMessageNodeLogDtos.FirstOrDefault()?.Inputs;
        public string Outputs => this.ChatMessageNodeLogDtos.LastOrDefault()?.Outputs;
        public List<ChatMessageNodeLogDto> ChatMessageNodeLogDtos { get; set; } = new List<ChatMessageNodeLogDto>();
    }
    public class ChatMessageNodeLogDto
    {
        public Guid ChatMessageNodeLogGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public Guid NodeGUID { get; set; }
        public Guid BatchGUID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int Index { get; set; }
        public double Duration { get; set; }
        public double FirstRespDuration { get; set; }
        public double StartTimeMillisecond => StartTime.Minute * 60 * 1000 + StartTime.Second * 1000 + StartTime.Millisecond;
        public double EndTimeMillisecond => EndTime.Minute * 60 * 1000 + EndTime.Second * 1000 + EndTime.Millisecond;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Status { get; set; } = "success";
        public string Message { get; set; }
        public int PromptTokens { get; set; } = 0;
        public int CompleteTokens { get; set; } = 0;
        public int Tokens => PromptTokens + CompleteTokens;
        public string Config { get; set; }
        public string Inputs { get; set; }
        public string Outputs { get; set; }
        public string ThinkOutputs { get; set; }

        public void SetDuration(Double millisecond)
        {
            this.Duration = Math.Round(millisecond / 1000.0, 3);
        }
        public void SetFirstRespDuration(Double millisecond)
        {
            this.FirstRespDuration = Math.Round(millisecond / 1000.0, 3);
        }
        public void SetMessage(string message)
        {
            this.Message = message?.Length > 1000 ? message[..1000] : message;
        }
    }

    public class ChatMessageKnowledgeNodeLogDto
    {
        public Guid ChatMessageKnowledgeNodeLogGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public Guid BatchGUID { get; set; }
        public Guid ApplicationGUID { get; set; }
        public Guid AssistantGUID { get; set; }
        public Guid SkillGUID { get; set; }
        public Guid NodeGUID { get; set; }
        public string KnowledgeCode { get; set; }
        public Guid KnowledgeFileSectionGUID { get; set; }
        public double Score { get; set; }
        public string Inputs { get; set; }
        public string Outputs { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string UserGUID { get; set; }
        public string UserName { get; set; }
        public string TenantCode { get; set; }
        public string TenantName { get; set; }
    }
}
