using System.Collections.Generic;
using System.Text.Json.Serialization;
using DocumentFormat.OpenXml.Vml.Spreadsheet;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos.approval
{
    public class ExecutePlanJobDto
    {
        public PlanInstanceInfoDto PlanInstanceInfoDto { get; set; }
        
        public PlanMessageNodeLogDto PlanMessageNodeLogDto { get; set; }
        
        public string ModelInstanceCode { get; set; }

    }

    public class PlanInstanceInfoDto
    {
        public string PlanInstanceGUID { get; set; }

        public string BusinessId { get; set; }

        public string PlanGUID { get; set; }

        public string PlanName { get; set; }
        
        public int PlanMode { get; set; }
        
        public int PromptMode { get; set; }

        public int Status { get; set; }

        public int Result { get; set; }

        public string ResultSummary { get; set; }

        public List<RuleGroupDto> RuleGroups { get; set; }

        public string ErrorMessage { get; set; }
        
        public string BusinessData { get; set; }
        
        public string BusinessDataSchema { get; set; }
        
        public string BusinessFormatData { get; set; }

        public int UseFlow { get; set; } = 0;
        
        public List<AttachmentDto> Attachments { get; set; }
        
        public List<PlanDatasourceInstanceDto> PlanDatasourceInstances { get; set; }

        public Dictionary<string, object> ParamsValue { get; set; }

        public List<PlanParamsInstanceDto> ParamsMapping { get; set; }
        
        public Dictionary<string, ParamsValueDto> ParamsValueMapping { get; set; }
        
        public string WorkSpaceCode { get; set; } = "4200";
        
        public Dictionary<string, List<string>> DynamicRules { get; set; }
    }

    public class RuleGroupDto
    {
        public string GroupGUID { get; set; }

        public string GroupName { get; set; }

        public List<RuleDto> Rules { get; set; }
    }

    public class RuleDto
    {
        public string PlanRuleInstanceGUID { get; set; }
        
        public string RuleGUID { get; set; }

        public string RuleName { get; set; }

        public string RuleCheckContent { get; set; }

        public int RuleType { get; set; }

        public int RuleCheckSource { get; set; }

        public int Status { get; set; }

        public int Result { get; set; }

        public string Overview { get; set; }

        public string Details { get; set; }
        
        public string ErrorMessage { get; set; }

        public string UserMessage { get; set; }


        public string RuleCheckResultDemo { get; set; }
        
        public int RiskLevel { get; set; } = 0;
    }

    public class ExcuteResultDto
    {
        
        [JsonPropertyName("status")]
        public bool? Status { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

    }

    public class AttachmentDto
    {
        public string Id { get; set; }

        public string Title { get; set; }

        public string Source { get; set; }

        public string Type { get; set; }

        public string Tag { get; set; }
        
        public string Content { get; set; }
    }

    public class PlanDatasourceInstanceDto
    {
        public string PlanDatasourceInstanceGUID { get; set; }

        public string PlanDatasourceGUID { get; set; }

        public string PlanInstanceGUID { get; set; }

        public string Name { get; set; }

        public int Source { get; set; }

        public string SourceId { get; set; }

        public string Metadata { get; set; }

        public string RequestParams { get; set; }

        public string RequestBody { get; set; }
        
        public List<AttachmentDto> Attachments  { get; set; }

        public int RequestStatus { get; set; } = 0;
        
        public int AutoReadAttachment { get; set; } = 1;
    }

    public class PlanParamsInstanceDto
    {
        public string PlanParamsGUID { get; set; }

        public string PlanGUID { get; set; }

        public string ParamsName { get; set; }

        public string ParamsId { get; set; }

        public int ParamsType { get; set; }

        public string MappingDatasource { get; set; }
        
    }
    
    public class ParamsValueDto
    {
        public string ParamsName { get; set; }

        public string ParamsId { get; set; }

        public int ParamsType { get; set; }
        
        public object Value { get; set; }
    }

    public class DatasourceFieldDto
    {
        public string PlanDatasourceParamViewGUID { get; set; }
        
        public string ParamId { get; set; }
        
        public string ParamName { get; set; }
        
        public string ParamType { get; set; }
        
        public string ParentId { get; set; }
        
        public int? Required  { get; set; } = 0;
        
        public int Selected  { get; set; } = 1;
        
        public int Attachment { get; set; } = 0;
        
        public List<DatasourceFieldDto> Children { get; set; }
    }
}