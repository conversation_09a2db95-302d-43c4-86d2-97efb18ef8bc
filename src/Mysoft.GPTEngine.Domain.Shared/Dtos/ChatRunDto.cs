using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Dynamic;
using Mysoft.GPTEngine.Domain.DTO;
using Newtonsoft.Json.Linq;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Shared.Event;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;
using System.Diagnostics;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ChatRunDto
    {
        #region 属性
        public ChatDto Chat { get; set; }
        public ModelInstanceDto ModelInstance { get; set; }
        public List<ChatMessageDto> ChatMessages { get; set; } = new List<ChatMessageDto>();
        public List<ChatMessageFileDto> ChatMessageFiles { get; set; } = new List<ChatMessageFileDto>();
        public KernelArguments ChatArguments { get; set; } = new KernelArguments();
        public SkillOrchestrationDto SkillOrchestration { get; set; }
        public List<FlowNode> Nodes { get; set; } = new List<FlowNode>();
        public SkillAgentDto Agent { get; set; } = new SkillAgentDto();
        public List<ChatMessageNodeLogDto> NodeLogs { get; set; } = new List<ChatMessageNodeLogDto>();
        public List<ChatMessageKnowledgeNodeLogDto> KnowledgeNodeLogs { get; set; } = new List<ChatMessageKnowledgeNodeLogDto>();

        public List<ChatMessageKnowledgeNodeLogDto> KnowledgeNodeLogs2 { get; set; } = new List<ChatMessageKnowledgeNodeLogDto>();

        public EventBus EventBus { get; set; } = new EventBus();
        public Stopwatch Stopwatch = new Stopwatch();
        public string SkillModelCode { get; set; }
        public Guid BatchGuid = Guid.NewGuid();
        public bool Next { get; set; }
        public string NextId { get; set; }
        public bool IsStream { get; set; } = true;
        public bool CheckContent { get; set; } = false;
        public List<ReplaceDto> ReplaceDtos { get; set; }
        public bool Cancel { get; set; } = false;
        public int CurrNodeIndex { get; set; } = 0;
        public int NextNodeIndex { get; set; } = 0;
        public Guid SkillGUID { get; set; }
        public string Mode { get; set; }
        public ChatInputDto ChatInput { get; set; }
        public bool SaveLog { get; set; } = true;
        //图片文档相关内容
        public List<WordsResult> WordsResult { get; set; } = new List<WordsResult>();

        public const string Card = "Card";

        public const string MessageCard = "Message";

        public const string InteractiveCard = "Interactive";

        public const string PromptTemplate = "PromptTemplate";

        public const string Selector = "Selector";

        public Dictionary<string, List<ParamDto>> outputCache { get; set; } = new Dictionary<string, List<ParamDto>>();

        /// <summary>
        /// ChatHistory对象，用于保存完整的对话历史（包含工具调用）
        /// </summary>
        public ChatHistory ChatHistory { get; set; }

        /// <summary>
        /// 初始ChatHistory长度，用于提取新增的工具调用消息
        /// </summary>
        public int? InitialChatHistoryCount { get; set; }
        #endregion

        public void UpdateOutputCache(string nodeCode, List<ParamDto> outputs)
        {
            if (outputCache.ContainsKey(nodeCode))
            {
                outputCache[nodeCode] = outputs;
            }
            else
            {
                outputCache.Add(nodeCode, outputs);
            }
        }

        public void AddWordsResult(List<WordsResult> wordsResults)
        {
            WordsResult.AddRange(wordsResults);
            //执行下面的代码时需要先判断是否存在KernelArgumentsConstant.SystemKeywordWordsResult的值。有的话需要先合并在赋值
            if (ChatArguments.ContainsName(KernelArgumentsConstant.SystemKeywordWordsResult))
            {
                var argWordsResult = JsonConvert.DeserializeObject<List<WordsResult>>(ChatArguments[KernelArgumentsConstant.SystemKeywordWordsResult].ToString());
                argWordsResult.AddRange(wordsResults);
                ChatArguments[KernelArgumentsConstant.SystemKeywordWordsResult] = JsonConvert.SerializeObject(argWordsResult);
            }
            else
            {
                ChatArguments.Add(KernelArgumentsConstant.SystemKeywordWordsResult, JsonConvert.SerializeObject(wordsResults));
            }
        }

        public bool IsArgumentContainsKey(string key)
        {
            return ChatArguments.ContainsName(key);
        }
        public string GetNextNodeIdNotEnd(string nextNodeGuid)
        {
            var nextNodeType = Nodes.FirstOrDefault(x => x.Id == nextNodeGuid)?.Type;
            return nextNodeType == SkillNodeTypeConstant.End ? null : nextNodeGuid;
        }
        public async Task AddArgument(string key, string value)
        {
            if (ChatArguments.ContainsName(key))
            {
                ChatArguments[key] = value;
                return;
            }
            ChatArguments.Add(key, value);
            await Task.CompletedTask;
        }
        public async Task<T> GetArgumentValue<T>(string key)
        {
            if (ChatArguments.ContainsName(key))
            {
                return await Task.FromResult((T)ChatArguments[key]);
            }
            return await Task.FromResult(default(T));
        }
        public async Task AddNodeOutputArgument2(string key, string value, string nodeCode)
        {
            var argumentKey = string.Format(KernelArgumentsConstant.NodeOutput, nodeCode, key);
            await AddArgument(argumentKey, value);
        }
        public async Task<KernelArguments> GetNodeInputArgument()
        {
            var kernelArguments = new KernelArguments();
            var inputs = await GetFlowNodeInputs();
            foreach (var input in inputs)
            {
                kernelArguments.Add(input.Code, input.LiteralValue);
            }
            //添加提示词相关日志
            return await Task.FromResult(kernelArguments);
        }
        public async Task<KernelArguments> GetNodeInputArgument(FlowNode flowNode)
        {
            var kernelArguments = new KernelArguments();
            var inputs = flowNode.Config.Inputs;
            foreach (var input in inputs)
            {
                kernelArguments.Add(input.Code, input.LiteralValue);
            }
            //添加提示词相关日志
            return await Task.FromResult(kernelArguments);
        }
        public async Task<KernelArguments> GetNodeOutputArgument()
        {
            var kernelArguments = new KernelArguments();
            var outputs = await GetFlowNodeOutputs();
            foreach (var output in outputs.Where(x => string.IsNullOrEmpty(x.Code) == false))
            {
                kernelArguments.Add(output.Code, output.LiteralValue);
            }
            return await Task.FromResult(kernelArguments);
        }
        public async Task<FlowNode> GetFlowNode()
        {
            if (Nodes.Any(x => x.Id == Chat.CurrentNodeGUID.ToString()) == false)
            {
                Console.WriteLine($"ChatRunDto.Nodes:{JsonConvert.SerializeObject(Nodes)}");
                Console.WriteLine($"ChatRunDto.GetFlowNode:{Chat.CurrentNodeGUID.ToString()}");
            }
            return await Task.FromResult(Nodes.FirstOrDefault(x => x.Id == Chat.CurrentNodeGUID.ToString()));

        }
        public async Task<FlowNode> GetFlowNode(string code)
        {
            return await Task.FromResult(Nodes.First(x => x.Code == code));
        }
        public async Task<List<ParamDto>> GetFlowNodeOutputs()
        {
            var flowNode = await GetFlowNode();
            return await Task.FromResult(flowNode.Config.Outputs);
        }
        public async Task<List<ParamDto>> GetFlowNodeInputs()
        {
            var flowNode = await GetFlowNode();
            return await Task.FromResult(flowNode.Config.Inputs);
        }
        public async Task<List<ParamDto>> GetFlowNodeFiles()
        {
            var flowNode = await GetFlowNode();
            return await Task.FromResult(flowNode.Config.Files);
        }
        public async Task InputsArgumentParser()
        {
            var flowNode = await GetFlowNode();
            await InputsArgumentParser(flowNode);
        }

        public async Task InputsArgumentParser(FlowNode flowNode)
        {
            if (flowNode.Config?.Inputs?.Count != 0)
            {
                await ArgumentParser(flowNode, flowNode.Config.Inputs);
            }
            if (flowNode.Config?.Files?.Count != 0)
            {
                await ArgumentParser(flowNode, flowNode.Config.Files);
            }
            if (flowNode.Config?.Conditions?.Count != 0)
            {
                foreach (var condition in flowNode.Config.Conditions)
                {
                    if (condition.Expressions != null && condition.Expressions.Any())
                    {
                        foreach (var expression in condition.Expressions)
                        {
                            foreach (var rule in expression.Rules)
                            {
                                if (rule.Left != null)
                                {
                                    await ArgumentParser(flowNode, rule.Left);
                                }
                                if (rule.Right != null)
                                {
                                    await ArgumentParser(flowNode, rule.Right);
                                }
                            }
                        }
                    }
                }
            }
        }

        public async Task OutputsArgumentParser()
        {
            var flowNode = await GetFlowNode();
            await ArgumentParser(flowNode, flowNode.Config.Outputs);
        }
        public async Task OutputsArgumentParser(FlowNode flowNode)
        {
            await ArgumentParser(flowNode, flowNode.Config.Outputs);
        }
        public async Task ArgumentParser(FlowNode flowNode, List<ParamDto> dtos)
        {
            foreach (var item in dtos)
            {
                if (item.Value == null) continue;
                await ArgumentParser(flowNode, item);
            }
        }
        public async Task ArgumentParser(FlowNode flowNode, ParamDto item)
        {
            if (item.Value == null) return;
            switch (item.Value.Type)
            {
                case "literal":
                    item.LiteralValue = item.Value.Content;
                    item.LiteralCode = string.Format(KernelArgumentsConstant.NodeInput, flowNode.Code, item.Code);
                    break;
                case "ref":
                    await RefArgumentParser(item);
                    break;
                default: break;
            }

        }
        public async Task RefArgumentParser(ParamDto paramDto)
        {
            var data = await GetLiteralValue(paramDto.Value.Content);
            if (data.Item1 is null)
            {
                return;
            }
            if (data.Item2 is ParamDto item)
            {
                paramDto.Name = paramDto.Name ?? item.Name;
                paramDto.Type = item.Type;
                //paramDto.Required = item.Required;
                paramDto.LiteralValue = string.IsNullOrEmpty(item.LiteralValue) ? await GetArgumentValue<string>(paramDto.Value.Content) : item.LiteralValue;
                paramDto.LiteralCode = string.IsNullOrEmpty(item.LiteralCode) ? paramDto.Value.Content : item.LiteralCode;

                if (paramDto.Type == FieldTypeConstant.ArrayField && paramDto.Schema != null && paramDto.Schema.Count > 0)
                {
                    var unSelectd = item.Schema.Where(item1 => !paramDto.Schema.Any(item2 =>
                        item2.Value.Type == "ref" && item2.Value.Content == item1.Code))
                                    .ToList();
                    foreach (var unselectItem in unSelectd)
                    {
                        paramDto.LiteralValue = JsonHelper.RemoveFieldFromJsonArray(item.LiteralValue, unselectItem.Code);
                    }
                    foreach (var scheme in paramDto.Schema)
                    {
                        //判断系统关键字
                        if (scheme.Value.Content.StartsWith(KernelArgumentsConstant._prefix))
                        {
                            var str = await GetArgumentValue<string>(scheme.Value.Content);
                            paramDto.LiteralValue = JsonHelper.AddFieldToJsonArray(paramDto.LiteralValue, scheme.Code, str);
                        }
                        if (scheme.Value.Type == "literal")
                        {
                            paramDto.LiteralValue = JsonHelper.AddFieldToJsonArray(paramDto.LiteralValue, scheme.Code, scheme.Value.Content);
                        }
                    }
                }
            }
            else if (paramDto.Type == FieldTypeConstant.ArrayField && paramDto.Schema != null && paramDto.Schema.Count > 0)
            {
                await InitSchemeSyetemVal(paramDto);
            }
        }
        public async Task InitSchemeSyetemVal(ParamDto paramDto)
        {
            foreach (var scheme in paramDto.Schema)
            {
                //判断系统关键字
                if (scheme.Value.Content.StartsWith(KernelArgumentsConstant._prefix))
                {
                    var str = await GetArgumentValue<string>(scheme.Value.Content);
                    scheme.LiteralValue = str;
                }
                if (scheme.Value.Type == "literal")
                {
                    scheme.LiteralValue = scheme.Value.Content;
                }
            }
        }
        public async Task<(string, object, FlowNode)> GetLiteralValue(string key)
        {
            if (key == KernelArgumentsConstant.Input || key.StartsWith(KernelArgumentsConstant._prefixKeyword))
            {
                return (key, new ParamDto
                {
                    LiteralValue = await GetArgumentValue<string>(key),
                    LiteralCode = key,
                    Type = FieldTypeConstant.TextField,
                }, null);
            }
            var keys = key.Split("_");
            if (keys != null && keys.Length >= 3)
            {
                var prefix = keys[0];
                var nodeCode = keys[1];
                var parameterCode = key.Replace($"{prefix}_{nodeCode}_", "");
                var flowNode = await GetFlowNode(nodeCode);
                var item = prefix == KernelArgumentsConstant._prefixNodeInput ?
                    flowNode.Config.Inputs.FirstOrDefault(x => x.Code == parameterCode)
                    : flowNode.Config.Outputs.FirstOrDefault(x => x.Code == parameterCode);

                if (item != null && string.IsNullOrEmpty(item.LiteralValue) && item.Value?.SyncMessage is LlmGetStreamingChatMessage streamingChatMessage)
                {
                    return (item.Value.Content, streamingChatMessage, flowNode);
                }
                if (item != null)
                {
                    //只调整取值的逻辑，保证拿到的节点结构是完整的
                    if (IsArgumentContainsKey(key))
                    {
                        item.LiteralValue = await GetArgumentValue<string>(key);
                    }
                    return (item.LiteralCode, item, flowNode);
                }
            }
            return (null, null, null);
        }


        public async Task AddNodeLog()
        {
            //var node = await GetFlowNode();
            //var nodeLog = new ChatMessageNodeLogDto
            //{
            //    ChatMessageNodeLogGUID = Guid.NewGuid(),
            //    ChatGUID = Chat.ChatGUID,
            //    NodeGUID = Chat.CurrentNodeGUID.GetValueOrDefault(),
            //    //BatchGUID = NodeLogs.FirstOrDefault()?.BatchGUID ?? Guid.NewGuid(),
            //    Name = node.Name,
            //    Description = node.Description,
            //    Index = NodeLogs.Count,
            //    StartTime = TimeZoneUtility.LocalNow(),
            //    Config = JsonConvert.SerializeObject(node.Config),
            //    Inputs = await KernelArgumentsFormatJson((await GetNodeInputArgument()))
            //};
            //// todo 先写死类型判断日志输入输出
            //switch (node.Type)
            //{
            //    case Selector:
            //        //选择器
            //        nodeLog.Inputs = JsonConvert.SerializeObject(node.Config.Conditions);
            //        break;
            //    default:
            //        break;
            //}

            //NodeLogs.Add(nodeLog);
        }
        public async Task AddSucceedNodeLog()
        {
            //var node = await GetFlowNode();
            //var nodeLog = NodeLogs.FirstOrDefault(x => x.NodeGUID == Chat.CurrentNodeGUID && x.BatchGUID.ToString() == "00000000-0000-0000-0000-000000000000");
            //if (nodeLog == null) return;

            //nodeLog.EndTime = TimeZoneUtility.LocalNow();
            //nodeLog.Outputs = await KernelArgumentsFormatJson((await GetNodeOutputArgument()));
            //// todo 先写死类型判断日志输入输出
            //switch (node.Type)
            //{
            //    case Card:
            //        //消息卡片
            //        if (string.Equals(node.Config.Type, MessageCard, StringComparison.OrdinalIgnoreCase))
            //        {
            //            nodeLog.Outputs = node.Config.Content;
            //        }
            //        break;
            //    case Selector:
            //        //选择器
            //        nodeLog.Outputs = JsonConvert.SerializeObject(node.Config.Conditions.First(f => f.Value == true));
            //        break;
            //    case PromptTemplate:
            //        nodeLog.Inputs = await KernelArgumentsFormatJson((await GetNodeInputArgument()));
            //        break;
            //    default:
            //        break;
            //}
        }
        public async Task AddfailedNodeLog(string message)
        {
            var node = await GetFlowNode();
            var nodeLog = NodeLogs.FirstOrDefault(x => x.NodeGUID == Chat.CurrentNodeGUID);
            if (nodeLog == null) return;

            nodeLog.EndTime = TimeZoneUtility.LocalNow();
            nodeLog.Status = "failed";
            nodeLog.SetMessage(message);
        }
        public async Task AddPromptNodeLog(int promptTokens, int completeTokens)
        {
            //var nodeLog = NodeLogs.FirstOrDefault(x => x.NodeGUID == Chat.CurrentNodeGUID);
            //if (nodeLog == null) return;
            //nodeLog.PromptTokens = promptTokens;
            //nodeLog.CompleteTokens = completeTokens;
        }

        //添加知识库节点命中日志
        public async Task AddKnowledgeNodeLog(List<KnowledgeFileSectionDto> knowledgeFileSectionDtos, List<QueryResultDto> queryResultDtos, string question)
        {
            if ((knowledgeFileSectionDtos == null || knowledgeFileSectionDtos.Count == 0) && (queryResultDtos == null || queryResultDtos.Count == 0)) return;
            //循环knowledgeFileSectionDtos 匹配queryResultDtos的值 并给KnowledgeNodeLogs的属性赋值
            foreach (var item in knowledgeFileSectionDtos)
            {
                var queryResultDto = queryResultDtos.FirstOrDefault(x => x.id == item.KnowledgeFileSectionGUID);
                if (queryResultDto == null) ;
                var knowledgeNodeLog = new ChatMessageKnowledgeNodeLogDto()
                {
                    ChatMessageKnowledgeNodeLogGUID = Guid.NewGuid(),
                    ChatGUID = Chat.ChatGUID,
                    BatchGUID = BatchGuid,
                    ApplicationGUID = Chat.ApplicationGUID,
                    AssistantGUID = Chat.AssistantGUID,
                    SkillGUID = Chat.SkillGUID,
                    NodeGUID = Chat.CurrentNodeGUID.Value,
                    KnowledgeCode = queryResultDto == null ? null : queryResultDto.knowledgeCode,
                    KnowledgeFileSectionGUID = item.KnowledgeFileSectionGUID,
                    Inputs = question,
                    Outputs = item.Content,
                    Score = queryResultDto.score,
                    CustomerId = Chat.CustomerId,
                    CustomerName = Chat.CustomerName,
                    UserGUID = Chat.UserGUID,
                    UserName = Chat.UserName,
                    TenantCode = Chat.TenantCode,
                    TenantName = Chat.TenantName
                };
                KnowledgeNodeLogs.Add(knowledgeNodeLog);
            }

        }

        private async Task<string> KernelArgumentsFormatJson(KernelArguments arguments)
        {
            var result = new ExpandoObject();
            foreach (var arg in arguments)
            {
                result.TryAdd(arg.Key, arg.Value);
            }
            return await Task.FromResult(JsonConvert.SerializeObject(result));
        }
    }
}