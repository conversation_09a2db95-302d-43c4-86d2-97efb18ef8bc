using DocumentFormat.OpenXml.VariantTypes;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class KnowledgeRecordDetailDto
    {
        public String Content { get; set; }
        public double Score { get; set; }
        public String KnowledgeName { get; set; }
        public String KnowledgeCode { get; set; }
        public String FileName { get; set; }
        public int SectionNumber { get; set; }
        public Guid KnowledgeRecordGUID { get; set; }
        public Guid KnowledgeGUID { get; set; }
        public string OriginFileName { get; set; }
        public Guid KnowledgeFileGUID { get; set; }
        public int FileSourceEnum { get; set;}
        public string Title { get; set; }
    }
}
