using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    #region EMC 相关配置
    public class EMCConfigDto
    {

    }

    public class DbInstances
    {
        public List<DbInstanceItem> items { get; set; }
    }
    public class DbUsed
    {
        public List<DbUsedItem> items { get; set; }
    }
    public class DbUsedItem
    {
        public string Category { get; set; }
        public string Code { get; set; }
        public string DbMasterInstanceId { get; set; }
        public string DbSlaveInstanceId { get; set; }
        public string DbName { get; set; }
        public string DbUser { get; set; }
        public string TenantCode { get; set; }
    }

    public class DbInstanceItem
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Host { get; set; }
        public string Port { get; set; } // 注意这里使用了int类型，因为端口号通常是一个整数
        public string Username { get; set; }
        public string Password { get; set; }
        public string ReadonlyUsername { get; set; }
        public string ReadonlyPassword { get; set; }
        public string Type { get; set; }
    }
    #endregion
}
