using Mysoft.GPTEngine.Domain.Shared.Constants;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ErrorResultDto
    {
        /// <summary>
        /// 错误编码，用于程序内部处理或日志记录。
        /// </summary>
        public string ErrorCode { get; set; }

        /// <summary>
        /// 用户可见的通用报错信息，提供给用户的友好提示。
        /// </summary>
        public string UserMessage { get; set; } = ErrMsgConst.NormalErrMsg;

        /// <summary>
        /// 实际的报错信息，可能包含技术细节，用于调试或进一步分析。
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 错误类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 错误码链接
        /// </summary>
        public string Link { get; set; }
    }
}
