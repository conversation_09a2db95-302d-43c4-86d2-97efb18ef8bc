using DocumentFormat.OpenXml.Office2013.Drawing.ChartStyle;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class PluginApiInfoDto
    {
        public PluginApiInfoDto() {
            this.Params = new List<ApiParam>();
            this.Results = new List<ApiParam>();
        }
        // api服务名称
        public string Name { get; set; }

        // 描述信息
        public string Describe { get; set; }

        // code
        public string Code { get; set; }

        // path
        public string Path { get; set; }

        public string Method { get; set; }

        public string ContentType { get; set; }

        public string MetaData{ get; set; }

        public string OperationId { get; set; }

        // 入参
        public List<ApiParam> Params { get; set; }

        // 返回参数
        public List<ApiParam> Results { get; set; }
    }
    public class ApiParam
    {
        public string Name { get; set; }
        public string Describe { get; set; }
        public string Type { get; set; }
        public bool Required { get; set; }
        public bool IsShow { get; set; }
        public bool Show { get; set; }
        public bool IsAttachment { get; set; }
        public string ParamType { get; set; }

        public string Id { get; set; }

        public List<ApiParam> Child { get; set; }
    }
}
