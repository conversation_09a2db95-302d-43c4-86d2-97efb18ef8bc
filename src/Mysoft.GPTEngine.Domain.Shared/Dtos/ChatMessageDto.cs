using System;
using Mysoft.GPTEngine.Domain.Shared.Constants;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ChatMessageDto : BaseDto
    {
        public Guid ChatMessageGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public Guid? NodeGUID { get; set; }
        public string Content { get; set; }
        public string Role { get; set; }
        public int Index { get; set; }
        public Guid BatchGUID { get; set; }
        
        public String CustomerId { get; set; } = string.Empty;
        
        public String CustomerName { get; set; } = string.Empty;
        
        public String UserGUID { get; set; } = string.Empty;
        
        public String UserName { get; set; } = string.Empty;
        
        public String TenantCode { get; set; } = string.Empty;
        
        public String TenantName { get; set; } = string.Empty;

        public String Error { get; set; } = string.Empty;

        public String ContentEventType { get; set; } 
        
        public String Answer { get; set; } = string.Empty;
        
        public string PlanGUID { get; set; }
        
        public int IsHidden { get; set; }
    }
}
