using Microsoft.Extensions.Configuration;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Shared
{
    public interface IConfigurationService
    {
        string GetBuilderUrl();

        string GetConfigurationItemByKey(string key);

        DbInstanceItem GetDbInstanceItem(string tenantCode);

        IConfiguration GetIConfiguration();
    }
}
