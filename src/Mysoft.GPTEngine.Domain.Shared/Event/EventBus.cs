using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Domain.Shared.Event
{
    public class EventBus : TEvent
    {
        private readonly ConcurrentDictionary<Guid, Dictionary<string, object>> _handlers = new ConcurrentDictionary<Guid, Dictionary<string, object>>();

        public void Subscribe<TEvent>(IEventHandler<TEvent> handler, string code) where TEvent : EventBase
        {
            var eventType = typeof(TEvent);
            if (!_handlers.ContainsKey(eventType.GUID))
            {
                _handlers[eventType.GUID] = new Dictionary<string, object>();
            }
            if (_handlers[eventType.GUID].ContainsKey(code) == false)
            {
                _handlers[eventType.GUID][code] = handler;
            }
            else
            {
                _handlers[eventType.GUID].Add(code, handler);
            }
        }

        public async Task PublishAsync<TEvent>(TEvent @event) where TEvent : EventBase
        {
            var eventType = typeof(TEvent);
            if (_handlers.TryGetValue(eventType.GUID, out var keyHandlers))
            {
                var tasks = keyHandlers.Select(handler => (handler.Value as IEventHandler<TEvent>).HandleAsync(@event, handler.Key));

                await Task.WhenAll(tasks);
            }
        }
    }

    public interface TEvent { }
}
