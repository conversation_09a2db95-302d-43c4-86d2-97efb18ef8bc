using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Event
{
    public class FieldEvent : EventBase
    {
        public string FlowCode { get; set; }
        public string Key { get; set; }
        public string Value { get; set; }
        public IJsonFixerProperty JsonFixerProperty { get; set; }
        public FieldEvent(string flowCode, string key, string value)
        {
            FlowCode = flowCode;
            Key = key;
            Value = value;
        }
        public FieldEvent(string flowCode, string key, string value, IJsonFixerProperty jsonFixerProperty)
        {
            FlowCode = flowCode;
            Key = key;
            Value = value;
            JsonFixerProperty = jsonFixerProperty;
        }

        public override string ToString()
        {
            if (string.IsNullOrWhiteSpace(Key) || string.IsNullOrWhiteSpace(Value) || string.Equals("null", Value)) return null;
            if (JsonFixerProperty == null)
            {
                // 序列化为JSON字符串
                return JsonHelper.JsonSerialize(Key, Value);
            }

            return JsonFixerProperty.GetJsonValue(Key, Value);
        }
    }
    public class PublishEventConfig
    {
        public bool PublishTextEvent { get; set; } = false;
        public bool PublishLlmResultEvent { get; set; } = false;
        // 大模型处理输出结果统一使用提示词输出类型，需要做技能提示词版本时使用多个配置类型
        public ResponseFormatType ResponseFormatType { get; set; }
        public List<string> ParamCodes { get; set; }
    }
    public enum ResponseFormatType
    {
        Text,
        JsonObject
    }

}
