using System.Collections.Generic;
using System.Dynamic;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.Shared
{
    public abstract class IApprovalDatasource
    {

        public abstract Task RequestBody(PlanDatasourceInstanceDto datasource,
            PlanInstanceInfoDto planInstanceInfoDto, CancellationToken cancellationToken);

        
        protected Dictionary<string, object> FormatGetRequestParams(PlanDatasourceInstanceDto datasource,
            PlanInstanceInfoDto planInstanceInfoDto)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();
            var paramsValueMapping = planInstanceInfoDto.ParamsValueMapping;
            foreach (var kv in paramsValueMapping)
            {
                var id = datasource.PlanDatasourceGUID + "_";
                if (!kv.Key.StartsWith(id))
                {
                    continue;
                }
                var field = kv.Key.Substring(id.Length);
                result.TryAdd(field, kv.Value.Value);
            }

            return result;
        }
        
        
        protected string FormatRequestParams(PlanDatasourceInstanceDto datasource,
            PlanInstanceInfoDto planInstanceInfoDto)
        {
            var result = new ExpandoObject();
            var paramsValueMapping = planInstanceInfoDto.ParamsValueMapping;
            foreach (var kv in paramsValueMapping)
            {
                var id = datasource.PlanDatasourceGUID + "_";
                if (!kv.Key.StartsWith(id))
                {
                    continue;
                }
                var field = kv.Key.Substring(id.Length);
                result.TryAdd(field, kv.Value.Value);
            }
            return JsonConvert.SerializeObject(result);
        }
        
    }
}