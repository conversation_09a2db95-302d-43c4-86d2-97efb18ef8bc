using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Shared.Utilities
{
    public sealed class ExpiredCacheUtil<TValue> where TValue : class
    {
        private static readonly ExpiredCacheUtil<TValue> _instance = new ExpiredCacheUtil<TValue>();
        private readonly ConcurrentDictionary<string, CacheEntry<TValue>> _cache = new ConcurrentDictionary<string, CacheEntry<TValue>>();


        public static ExpiredCacheUtil<TValue> Instance
        {
            get { return _instance; }
        }

        public void Set(string key, TValue value, TimeSpan timeSpan)
        {
            var expirationTime = TimeZoneUtility.LocalNow().Add(timeSpan);
            var cacheEntry = new CacheEntry<TValue>(value, expirationTime);

            _cache[key] = cacheEntry;
        }

        public TValue Get(string key)
        {
            if (_cache.TryGetValue(key, out CacheEntry<TValue> cacheEntry))
            {
                if (cacheEntry.IsExpired)
                {
                    // Entry has expired, remove it from the cache.
                    _cache.TryRemove(key, out _);
                    return default(TValue);
                }
                else
                {
                    // Entry is still valid.
                    return cacheEntry.Value;
                }
            }
            else
            {
                // Key not found in the cache.
                return default(TValue);
            }
        }

        public void Remove(string key)
        {
            _cache.TryRemove(key, out _);
        }

        private class CacheEntry<T>
        {
            public T Value { get; }
            public DateTime ExpirationTime { get; }

            public CacheEntry(T value, DateTime expirationTime)
            {
                Value = value;
                ExpirationTime = expirationTime;
            }

            public bool IsExpired => TimeZoneUtility.LocalNow() > ExpirationTime;
        }
    }
}
