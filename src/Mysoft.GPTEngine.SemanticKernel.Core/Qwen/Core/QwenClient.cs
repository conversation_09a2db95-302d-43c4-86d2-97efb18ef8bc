// Copyright (c) Microsoft. All rights reserved.

using Azure.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Http;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection.Metadata;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using static Mysoft.GPTEngine.SemanticKernel.Core.Dtos.LLMResponseDto;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core
{
    internal sealed class QwenClient
    {
        private readonly HttpClient _httpClient;

        internal string ModelId { get; }
        internal string? ApiKey { get; }
        internal Uri Endpoint { get; }
        internal string Separator { get; }
        internal ILogger Logger { get; }

        internal QwenClient(
            string modelId,
            HttpClient httpClient,
            Uri? endpoint = null,
            string? apiKey = null,
            ILogger? logger = null)
        {
            Verify.NotNullOrWhiteSpace(modelId);
            Verify.NotNull(httpClient);

            endpoint ??= new Uri("https://dashscope.aliyuncs.com");
            Separator = endpoint.AbsolutePath.EndsWith("/", StringComparison.InvariantCulture) ? string.Empty : "/";
            Endpoint = endpoint;
            ModelId = modelId;
            ApiKey = apiKey;
            _httpClient = httpClient;
            Logger = logger ?? NullLogger.Instance;
        }

        #region ClientCore
        internal static void ValidateMaxTokens(int? maxTokens)
        {
            if (maxTokens < 1)
            {
                throw new ArgumentException($"MaxTokens {maxTokens} is not valid, the value must be greater than zero");
            }
        }

        internal static void ValidateMaxNewTokens(int? maxNewTokens)
        {
            if (maxNewTokens < 0)
            {
                throw new ArgumentException($"MaxNewTokens {maxNewTokens} is not valid, the value must be greater than or equal to zero");
            }
        }

        internal async Task<string> SendRequestAndGetStringBodyAsync(
            HttpRequestMessage httpRequestMessage,
            CancellationToken cancellationToken)
        {
            // 打印请求的出入参和header
            Logger.LogInformation("[QwenClient] [SendRequestAndGetStringBodyAsync] [Request] {Request}", httpRequestMessage);
            using var response = await _httpClient.SendWithSuccessCheckAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);
            Logger.LogInformation("[QwenClient] [SendRequestAndGetStringBodyAsync] [Response] {Response}", response);

            var body = await response.Content.ReadAsStringWithExceptionMappingAsync()
                .ConfigureAwait(false);

            return body;
        }

        internal async Task<HttpResponseMessage> SendRequestAndGetResponseImmediatelyAfterHeadersReadAsync(
            HttpRequestMessage httpRequestMessage,
            string modelId,
            CancellationToken cancellationToken)
        {
            try
            {
                // 打印请求的出入参和header
                Logger.LogDebug("[QwenClient] [SendRequestAndGetResponseImmediatelyAfterHeadersReadAsync]  [Request] {Request}", httpRequestMessage);
                Logger.LogDebug("[QwenClient] [SendRequestAndGetResponseImmediatelyAfterHeadersReadAsync]  [RequestBody] {RequestBody}", httpRequestMessage.Content.ReadAsStringAsync().Result);
                var response = await _httpClient.SendWithSuccessCheckAsync(httpRequestMessage, HttpCompletionOption.ResponseHeadersRead, cancellationToken)
                    .ConfigureAwait(false);
                Logger.LogDebug("[QwenClient] [SendRequestAndGetResponseImmediatelyAfterHeadersReadAsync]  [Response] {Response}", response);
                return response;
            }
            catch (HttpOperationException httpEx)
            {
                JsonUtility.TryDeserializeObject(httpEx.ResponseContent,out QwenDto qwenDto);
                string errMsg = string.Empty;
                if (qwenDto != null)
                {
                    errMsg = string.Format(ErrMsgConst.LLM_ErrMsgQwen, modelId, qwenDto.request_id, qwenDto.code, qwenDto.message);
                }
                else
                {
                    errMsg = string.Format(ErrMsgConst.LLM_ErrMsgOthers, modelId,httpEx.StatusCode ,httpEx.ResponseContent);
                }
                throw new LLmCustomException("LLM", errMsg, (int)ModelInstanceTypeEnum.AliTextGeneration);
            }
}

        internal static T DeserializeResponse<T>(string body)
        {
            try
            {
                var deserializedResponse = JsonSerializer.Deserialize<T>(body);
                if (deserializedResponse is null)
                {
                    throw new JsonException("Response is null");
                }

                return deserializedResponse;
            }
            catch (JsonException exc)
            {
                throw new KernelException("Unexpected response from model", exc)
                {
                    Data = { { "ResponseData", body } },
                };
            }
        }

        internal void SetRequestHeaders(HttpRequestMessage request)
        {
            request.Headers.Add("User-Agent", HttpHeaderConstant.Values.UserAgent);
            request.Headers.Add(HttpHeaderConstant.Names.SemanticKernelVersion, HttpHeaderConstant.Values.GetAssemblyVersion(GetType()));
            request.Headers.Add("X-DashScope-OssResourceResolve", "enable");

            if (!string.IsNullOrEmpty(ApiKey))
            {
                request.Headers.Add("Authorization", $"Bearer {ApiKey}");
            }
        }

        internal HttpRequestMessage CreatePost(object requestData, Uri endpoint, string? apiKey)
        {
            var httpRequestMessage = HttpRequest.CreatePostRequest(endpoint, requestData);
            this.SetRequestHeaders(httpRequestMessage);

            return httpRequestMessage;
        }
        internal HttpRequestMessage CreateGet(object? requestData, string endpoint, string? apiKey)
        {
            var httpRequestMessage = HttpRequest.CreateGetRequest(endpoint, requestData);
            this.SetRequestHeaders(httpRequestMessage);

            return httpRequestMessage;
        }

        #endregion

        #region oss
        public async Task UploadsAsync(ChatHistory chatMessages, CancellationToken cancellationToken)
        {
            if (chatMessages.Any(x => x.Items.Any(item => item is ImageContent || item is SemanticKernel.Core.Contents.BinaryContent)) == false)
            {
                return;
            }

            GetPolicyResponse policy =new GetPolicyResponse();
            if (chatMessages.Any(x => x.Items.Any(item => item is ImageContent)))
            {
                policy = await GetPolicy(cancellationToken);
            }
            

            foreach (var chatMessage in chatMessages)
            {
                foreach (var item in chatMessage.Items)
                {
                    if (item is ImageContent image)
                    {
                        image.Uri = await UploadAsync(image, policy, cancellationToken);
                    }

                    if (item is SemanticKernel.Core.Contents.BinaryContent binary)
                    {
                        binary.FileId = await UploadFileAsync(binary, cancellationToken);
                    }
                }
            }
        }

        private async Task<Uri> UploadAsync(ImageContent image, GetPolicyResponse policy, CancellationToken cancellationToken)
        {
            var fileData = (byte[])image.Metadata.GetValueOrDefault("FileContent", string.Empty);
            
            var path = $"{policy.Data.UploadDir}/{Guid.NewGuid()}";
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, policy.Data.UploadHost);
            SetRequestHeaders(httpRequestMessage);
            var multipartForm = new MultipartFormDataContent($"-----{Guid.NewGuid()}")
            {
                { new ByteArrayContent(Encoding.UTF8.GetBytes(policy.Data.OssAccessKeyId)), "\"OSSAccessKeyId\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes(path)), "\"Key\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes(policy.Data.Policy)), "\"Policy\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes(policy.Data.Signature)), "\"Signature\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes("private")), "\"x-oss-object-acl\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes("true")), "\"x-oss-forbid-overwrite\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes("200")), "\"success_action_status\"" },
                { new ByteArrayContent(fileData), "\"file\"" }
            };
            
            var boundary = multipartForm.Headers.ContentType.Parameters.First(o => o.Name == "boundary");
            boundary.Value = boundary.Value.Replace("\"", String.Empty);
            
            httpRequestMessage.Content = multipartForm;
            await this.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken).ConfigureAwait(false);
            return new Uri($"oss://{path}");
        }
    


        /// <summary>
        /// OpenAI文件上传
        /// </summary>
        /// <param name="binary"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<String> UploadFileAsync(SemanticKernel.Core.Contents.BinaryContent binary, CancellationToken cancellationToken)
        {
            var fileData = binary.Data.GetValueOrDefault().Span.ToArray();
            using var formData = new MultipartFormDataContent();
            using var contentPurpose = new StringContent("file-extract");
            using var contentFile = new ByteArrayContent(fileData);
            formData.Add(contentPurpose, "purpose");
            formData.Add(contentFile, "file", binary.FileName?? "文档");
            var path = new Uri($"{this.Endpoint}compatible-mode/v1/files");
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, path){Content = formData };
            SetRequestHeaders(httpRequestMessage);
            string body = await this.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);

            var response = DeserializeResponse<FileUploadResponse>(body);
            return $"fileid://{response.Id}";
        }
     

        private async Task<GetPolicyResponse> GetPolicy(CancellationToken cancellationToken)
        {
            var endpoint = $"{this.Endpoint}{this.Separator}api/v1/uploads?action=getPolicy&model={this.ModelId}";
            using var httpRequestMessage = this.CreateGet(null, endpoint, this.ApiKey);

            string body = await this.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken).ConfigureAwait(false);
            return DeserializeResponse<GetPolicyResponse>(body);
        }
        #endregion

        #region Embeddings
        public async Task<IList<ReadOnlyMemory<float>>> GenerateEmbeddingsAsync(
    IList<string> data,
    Kernel? kernel,
    CancellationToken cancellationToken)
        {
            var endpoint = this.GetEmbeddingGenerationEndpoint();

            if (data.Count > 1)
            {
                throw new NotSupportedException("Currently this interface does not support multiple embeddings results per data item, use only one data item");
            }

            var request = new TextEmbeddingRequest
            {
                Model = ModelId,
                Input = new EmbeddingInput { Texts = data }
            };
            using var httpRequestMessage = this.CreatePost(request, endpoint, this.ApiKey);

            string body = await this.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);

            var response = DeserializeResponse<TextEmbeddingResponse>(body);

            // Currently only one embedding per data is supported
            return response.Output.Embeddings.Select(embedding => embedding.Embedding).ToList();
        }
        private Uri GetEmbeddingGenerationEndpoint() => new Uri($"{this.Endpoint}{this.Separator}api/v1/services/embeddings/text-embedding/text-embedding");
        #endregion
    }
}
