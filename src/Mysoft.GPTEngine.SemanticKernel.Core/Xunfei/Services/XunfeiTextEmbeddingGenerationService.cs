using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Services;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.Xunfei.Core;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Xunfei.Services
{
    public class XunfeiTextEmbeddingGenerationService : ITextEmbeddingGenerationService
    {

        private Dictionary<string, object?> AttributesInternal { get; } = new Dictionary<string, object?>();
        private XunfeiClient Client { get; }

        /// <inheritdoc />
        public IReadOnlyDictionary<string, object?> Attributes => this.AttributesInternal;

        public XunfeiTextEmbeddingGenerationService(
            string model,
            Uri? endpoint = null,
            string? apiKey = null,
            HttpClient? httpClient = null,
            ILoggerFactory? loggerFactory = null)
        {
            this.Client = new XunfeiClient(modelId: model, endpoint: endpoint ?? httpClient?.BaseAddress, apiKey: apiKey, httpClient: HttpClientProvider.GetHttpClient(httpClient), logger: loggerFactory?.CreateLogger(this.GetType())
                );
            this.AttributesInternal.Add(AIServiceExtensions.ModelIdKey, model);
        }

        public Task<IList<ReadOnlyMemory<float>>> GenerateEmbeddingsAsync(IList<string> data, Kernel? kernel = null, CancellationToken cancellationToken = default)
            => this.Client.GenerateEmbeddingsAsync(data, kernel, cancellationToken);
    }
}
