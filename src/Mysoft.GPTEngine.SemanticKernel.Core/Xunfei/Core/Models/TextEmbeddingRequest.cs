using System;
using System.Collections.Generic;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Xunfei.Core.Models
{
    internal sealed class TextEmbeddingRequest
    {
        /// <summary>
        /// model
        /// </summary>
        [JsonPropertyName("model")]
        public string Model { get; set; }
        /// <summary>
        /// Data to embed.
        /// </summary>
        [JsonPropertyName("inputs")]
        public IList<string> Inputs { get; set; } = new List<string>();
        /// <summary>
        /// parameters
        /// </summary>
        [JsonPropertyName("parameters")]
        public EmbeddingParameter? Parameters { get; set; }


        /// <summary>
        /// 解析URL
        /// </summary>
        /// <param name="requestUrl"></param>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        public Url ParseUrl( string requestUrl ) {
            string host = requestUrl.Substring(requestUrl.IndexOf("://") + 3);
            string schema = requestUrl.Substring(0, requestUrl.IndexOf("://") + 3);
            int edidx = host.IndexOf("/");
            if (edidx <= 0)
            {
                throw new System.Exception("Invalid request URL: " + requestUrl);
            }
            string path = edidx > 0 ? host.Substring(edidx) : "/";
            host = edidx > 0 ? host.Substring(0, edidx) : host;
            return new Url(host, path, schema);
        }

        /// <summary>
        /// 生成鉴权URL 参考文档 https://www.xfyun.cn/doc/spark/http_url_authentication.html
        /// </summary>
        /// <param name="requestUrl"></param>
        /// <param name="method"></param>
        /// <param name="apiKey"></param>
        /// <param name="apiSecret"></param>
        /// <returns></returns>
        public string AssembleWsAuthUrl(string requestUrl, string method = "GET", string apiKey = "", string apiSecret = "") {
            Url uri = ParseUrl(requestUrl);
            string host = uri.Host;
            string path = uri.Path;

            DateTime now = DateTime.UtcNow;

            string date = now.ToString("r", CultureInfo.InvariantCulture);

            string signatureOrigin = $"host: {host}\ndate: {date}\n{method} {path} HTTP/1.1";
            byte[] keyBytes = Encoding.UTF8.GetBytes(apiSecret);

            byte[] signatureBytes;
            using (var hmac = new HMACSHA256(keyBytes))
            {
                signatureBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(signatureOrigin));
            }

            string signatureSha = Convert.ToBase64String(signatureBytes);

            string authorizationOrigin = $"api_key=\"c4bd0b6afea8895d42998dd93867a342\", algorithm=\"hmac-sha256\", headers=\"host date request-line\", signature=\"{signatureSha}\"";
            string authorization = Convert.ToBase64String(Encoding.UTF8.GetBytes(authorizationOrigin));
            /***
             * 代码未完成，讯飞文本转向量接口鉴权方式与chat差距太大 
             * 
             * 
             * **/
            string result = "";
            return result;
        }

    }
    internal sealed class EmbeddingParameter
    {
        /// <summary>
        /// 取值：query或document，默认值为document。
        /// 说明：文本转换为向量后可以应用于检索、聚类、分类等下游任务，对于检索这类非对称任务，为了达到更好的检索效果，建议区分查询文本（query）和底库文本（document）类型，聚类、分类等对称任务可以不用特殊指定，
        /// </summary>
        [JsonPropertyName("text_type")]
        public string TextType { get; set; } = "document";
    }

    internal sealed class Url
    {
        public string Host { get; set; }
        public string Path { get; set; }
        public string Schema { get; set; }

        public Url(string host, string path, string schema)
        {
            Host = host;
            Path = path;
            Schema = schema;
        }
    }
}
