using AlibabaCloud.SDK.Ocr_api20210707.Models;
using Mysoft.GPTEngine.Common.CustomerException;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Tea;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Aliyun.Services
{
    /// <summary>
    /// 增值税发票识别服务
    /// </summary>
    public class RecognizeInvoiceService : RecognizeBaseService, IOcrRecognizeService
    {
        public RecognizeInvoiceService(string endpoint, string accessKeyId, string accessSecret) 
            : base(endpoint, accessKeyId, accessSecret) { }

        public async Task<OcrResponse> Execute(OcrRequest body)
        {
            OcrResponse ocrResponse = new OcrResponse();
            using MemoryStream stream = body.memoryStream != null && body.memoryStream.Length > 0 
                ? body.memoryStream 
                : new MemoryStream(body.Body);
            
            stream.Position = 0;
            if (stream.Length == 0)
            {
                Console.WriteLine("Either Body must be provided.");
                return await Task.FromResult(ocrResponse);
            }
            RecognizeInvoiceRequest recognizeInvoiceRequest = new RecognizeInvoiceRequest
            {
                Body = stream
            };
            
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime = new AlibabaCloud.TeaUtil.Models.RuntimeOptions();
            try
            {
                var response = _client.RecognizeInvoiceWithOptions(recognizeInvoiceRequest, runtime);
                ProcessOcrRequest(response, ocrResponse);
            }
            catch (TeaException error)
            {
                ocrResponse.Content = error.Message;
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
                ThrowOcrException(error.StatusCode, error.Message,body.ocrCode, ModelInstanceTypeEnum.AliOCR);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                ocrResponse.Content = _error.Message;
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
                Console.WriteLine(_error.InnerException?.StackTrace);
                Console.WriteLine(_error.StackTrace);
            }
            return await Task.FromResult(ocrResponse);
        }

        private void ProcessOcrRequest(RecognizeInvoiceResponse response, OcrResponse ocrResponse)
        {
            ocrResponse.Success = response.StatusCode == 200;
            if (ocrResponse.Success && response?.Body?.Data != null)
            {
                var data = JsonSerializer.Deserialize<RecognizeInvoiceData>(response.Body.Data);
                ocrResponse.Content = JsonConvert.SerializeObject(data.Data);

                // 转换为统一的WordsResult格式
                ocrResponse.WordsResult = new List<WordsResult>();
                
                if (data.PrismKeyValueInfo?.Any() == true)
                {
                    foreach (var item in data.PrismKeyValueInfo)
                    {
                        if (string.IsNullOrEmpty(item.Value))
                            continue;
                        ocrResponse.WordsResult.Add(new WordsResult
                        {
                            Words = item.Value,
                            Prob = item.ValueProb,
                            Location = new Location()
                        });
                    }
                }
                ocrResponse.WordsResultNum = ocrResponse.WordsResult.Count;
            }
        }
    }

    /// <summary>
    /// 发票识别返回结果
    /// </summary>
    public partial class RecognizeInvoiceData
    {
        /// <summary>
        /// 图片旋转角度
        /// </summary>
        [JsonPropertyName("angle")]
        public int Angle { get; set; }

        /// <summary>
        /// 识别结果数据
        /// </summary>
        [JsonPropertyName("data")] 
        public InvoiceData Data { get; set; }

        /// <summary>
        /// 图片高度
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; }

        /// <summary>
        /// 原始图片高度
        /// </summary>
        [JsonPropertyName("orgHeight")]
        public int OrgHeight { get; set; }

        /// <summary>
        /// 原始图片宽度
        /// </summary>
        [JsonPropertyName("orgWidth")]
        public int OrgWidth { get; set; }

        /// <summary>
        /// 结构化坐标信息
        /// </summary>
        [JsonPropertyName("prism_keyValueInfo")]
        public List<PrismKeyValueInfo> PrismKeyValueInfo { get; set; }

        /// <summary>
        /// 图片宽度
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; }
    }

    public class InvoiceData
    {
        /// <summary>
        /// 发票代码
        /// </summary>
        [JsonPropertyName("invoiceCode")]
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 发票号码
        /// </summary>
        [JsonPropertyName("invoiceNumber")]
        public string InvoiceNumber { get; set; }
        
        [JsonPropertyName("printedInvoiceCode")]
        public string PrintedInvoiceCode { get; set; }
        
        [JsonPropertyName("printedInvoiceNumber")]
        public string PrintedInvoiceNumber { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        [JsonPropertyName("invoiceDate")]
        public string InvoiceDate { get; set; }
        
        /// <summary>
        /// 机器编号
        /// </summary>
        [JsonPropertyName("machineCode")]
        public string MachineCode { get; set; }

        /// <summary>
        /// 校验码
        /// </summary>
        [JsonPropertyName("checkCode")]
        public string CheckCode { get; set; }
        
        [JsonPropertyName("purchaserName")]
        public string PurchaserName { get; set; }
        
        [JsonPropertyName("purchaserTaxNumber")]
        public string PurchaserTaxNumber { get; set; }
        
            
        [JsonPropertyName("purchaserContactInfo")]
        public string PurchaserContactInfo { get; set; }
        
         
        [JsonPropertyName("purchaserBankAccountInfo")]
        public string PurchaserBankAccountInfo { get; set; }
        
        [JsonPropertyName("passwordArea")]
        public string PasswordArea { get; set; }
        
        
        [JsonPropertyName("invoiceAmountPreTax")]
        public string InvoiceAmountPreTax { get; set; }
        
        [JsonPropertyName("invoiceTax")]
        public string InvoiceTax { get; set; }
        
        /// <summary>
        /// 价税合计(大写)
        /// </summary>
        [JsonPropertyName("totalAmountInWords")]
        public string TotalAmountInWords { get; set; }
        
        /// <summary>
        /// 价税合计(小写)
        /// </summary>
        [JsonPropertyName("totalAmount")]
        public string TotalAmount { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        [JsonPropertyName("withoutTaxAmount")]
        public string WithoutTaxAmount { get; set; }
        
        /// <summary>
        /// 合计税额
        /// </summary>
        [JsonPropertyName("taxAmount")]
        public string TaxAmount { get; set; }

        /// <summary>
        /// 销售方名称
        /// </summary>
        [JsonPropertyName("sellerName")]
        public string SellerName { get; set; }

        /// <summary>
        /// 销售方纳税人识别号
        /// </summary>
        [JsonPropertyName("sellerTaxNumber")]
        public string SellerTaxNumber { get; set; }

        /// <summary>
        /// 销售方地址、
        /// </summary>
        [JsonPropertyName("sellerContactInfo")]
        public string SellerContactInfo { get; set; }

        /// <summary>
        /// 销售方开户行及账号
        /// </summary>
        [JsonPropertyName("sellerBankAccountInfo")]
        public string SellerBankAccountInfo { get; set; }
        
        [JsonPropertyName("recipient")]
        public string Recipient { get; set; }
        
        [JsonPropertyName("reviewer")]
        public string Reviewer { get; set; }
        
                
        [JsonPropertyName("drawer")]
        public string Drawer { get; set; }
        
        [JsonPropertyName("remarks")]
        public string Remarks { get; set; }
        
        [JsonPropertyName("title")]
        public string Title { get; set; }
        
        [JsonPropertyName("formType")]
        public string formType { get; set; }

        /// <summary>
        /// 发票消费类型
        /// </summary>
        [JsonPropertyName("invoiceType")]
        public string InvoiceType { get; set; }

        [JsonPropertyName("specialTag")]
        public string SpecialTag { get; set; }
        
        /// <summary>
        /// 发票明细条目
        /// </summary>
        [JsonPropertyName("invoiceDetails")]
        public List<InvoiceItem> InvoiceDetails { get; set; }

    }

    public class InvoiceItem
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        [JsonPropertyName("itemName")]
        public string ItemName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [JsonPropertyName("specification")]
        public string Specification { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [JsonPropertyName("quantity")]
        public string Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [JsonPropertyName("unitPrice")]
        public string UnitPrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [JsonPropertyName("amount")]
        public string Amount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [JsonPropertyName("taxRate")]
        public string TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [JsonPropertyName("tax")]
        public string Tax { get; set; }
    }

    /// <summary>
    /// 结构化坐标信息
    /// </summary>
    public class PrismKeyValueInfo
    {
        /// <summary>
        /// 字段key值
        /// </summary>
        [JsonPropertyName("key")]
        public string Key { get; set; }

        /// <summary>
        /// key的置信度
        /// </summary>
        [JsonPropertyName("keyProb")] 
        public double KeyProb { get; set; }

        /// <summary>
        /// 字段value值
        /// </summary>
        [JsonPropertyName("value")]
        public string Value { get; set; }

        /// <summary>
        /// value的坐标信息
        /// </summary>
        [JsonPropertyName("valuePos")]
        public List<ValuePosition> ValuePos { get; set; }

        /// <summary>
        /// value的置信度
        /// </summary>
        [JsonPropertyName("valueProb")]
        public double ValueProb { get; set; }
    }

    /// <summary>
    /// 坐标信息
    /// </summary>
    public class ValuePosition
    {
        /// <summary>
        /// x坐标
        /// </summary>
        [JsonPropertyName("x")]
        public int X { get; set; }

        /// <summary>
        /// y坐标
        /// </summary>
        [JsonPropertyName("y")]
        public int Y { get; set; }
    }
} 