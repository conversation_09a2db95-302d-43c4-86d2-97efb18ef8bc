using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr
{
    public class OcrBaseService
    {
        public void ThrowOcrException(int errorCode, string message, string ocrCode, ModelInstanceTypeEnum modelInstanceTypeEnum)
        {
            string errorMsg = string.Format(ErrMsgConst.Ocr_ErrMsg, ocrCode, errorCode, message);
            throw new LLmCustomException("Ocr", errorMsg, (int)modelInstanceTypeEnum);
        }
    }
}
