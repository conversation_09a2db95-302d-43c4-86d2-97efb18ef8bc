using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr
{
    public static class OcrRecognizeConst
    {
        /**
         * 阿里OCR模型编码
         */
        public const string Ali_RecognizeAdvanced = "Ali_RecognizeAdvanced";

        public const string Ali_RecognizeHandwriting = "Ali_RecognizeHandwriting";
        public const string Ali_RecognizeInvoice = "Ali_RecognizeInvoice";
        public const string Ali_RecognizeVl = "qwen-vl-ocr"; // 特殊处理

        /**
         * 百度OCR模型编码
         */
        public const string Baidu_RecognizeAdvanced = "Baidu_RecognizeAdvanced";

        public const string Baidu_RecognizeHandwriting = "Baidu_RecognizeHandwriting";
        public const string Baidu_RecognizeInvoice = "Baidu_RecognizeInvoice";
    }
}