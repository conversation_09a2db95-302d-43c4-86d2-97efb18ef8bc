// Copyright (c) Microsoft. All rights reserved.

using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu.Services;
using System;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Mysoft.GPTEngine.SemanticKernel.Core.ContentReview;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu
{
    /// <summary>
    /// Provides extension methods for the <see cref="IKernelBuilder"/> class to configure Hugging Face connectors.
    /// </summary>
    public static class ContentReviewKernelBuilderExtensions
    {
        public static IKernelBuilder AddContentReviewRecognize(
            this IKernelBuilder builder,
            string model,
            string endpoint,
            string accessKeyId,
            string accessSecret,
            string serviceId,
            string strategyId)
        {
            Verify.NotNull(builder);
            Verify.NotNull(endpoint);

            builder.Services.AddKeyedSingleton<IContentReview>(serviceId, (serviceProvider, _) =>
            {
                switch (model)
                {
                     case ContentReviewConst.Ali_ContentReview:
                         return new AliContentReview(endpoint, accessKeyId, accessSecret, strategyId);
                    default:
                        return new BaiduContentReview(endpoint, accessKeyId, accessSecret, strategyId);
                }
            });
            return builder;
        }
    }
}
