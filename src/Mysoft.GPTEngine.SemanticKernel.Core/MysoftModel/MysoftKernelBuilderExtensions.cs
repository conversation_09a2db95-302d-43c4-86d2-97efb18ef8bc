// Copyright (c) Microsoft. All rights reserved.

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using System;
using System.Net.Http;
using Microsoft.SemanticKernel.Embeddings;
using Mysoft.GPTEngine.SemanticKernel.Core.Baidu.Services;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Services;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel
{
    /// <summary>
    /// Provides extension methods for the <see cref="IKernelBuilder"/> class to configure Hugging Face connectors.
    /// </summary>
    public static class MysoftKernelBuilderExtensions
    {

        /// <summary>
        /// Adds an Hugging Face chat completion service with the specified configuration.
        /// </summary>
        /// <param name="builder">The <see cref="IKernelBuilder"/> instance to augment.</param>
        /// <param name="model">The name of the Hugging Face model.</param>
        /// <param name="endpoint">The endpoint URL for the chat completion service.</param>
        /// <param name="apiKey">The API key required for accessing the Hugging Face service.</param>
        /// <param name="serviceId">A local identifier for the given AI service.</param>
        /// <param name="httpClient">The HttpClient to use with this service.</param>
        /// <returns>The same instance as <paramref name="builder"/>.</returns>
        public static IKernelBuilder AddMysoftChatCompletion(
            this IKernelBuilder builder,
            string modelId,
            string? deploymentName,
            Uri? endpoint = null,
            string? apiKey = null,
            string? apiSecret = null,
            string? serviceId = null,
            string? vendor = null,
            HttpClient? httpClient = null)
        {
            Verify.NotNull(builder);
            Verify.NotNull(modelId);

            builder.Services.AddKeyedSingleton<IChatCompletionService>(serviceId, (serviceProvider, _) =>
                new MysoftChatCompletionService(
                    modelId,
                    deploymentName,
                    endpoint,
                    apiKey,
                    apiSecret,
                    vendor,
                    HttpClientProvider.GetHttpClient(httpClient, serviceProvider),
                    serviceProvider.GetService<ILoggerFactory>()
                ));

            return builder;
        }
        
        public static IKernelBuilder AddMysoftEmbedding(
            this IKernelBuilder builder,
            string modelId,
            string? deploymentName,
            Uri? endpoint = null,
            string? apiKey = null,
            string? clientId = null,
            string? vendor = null,
            string? serviceId = null,
            HttpClient? httpClient = null)
        {
            Verify.NotNull(builder);
            Verify.NotNull(modelId);

            builder.Services.AddKeyedSingleton<ITextEmbeddingGenerationService>(serviceId, (serviceProvider, _) =>
                new MysoftTextEmbeddingGenerationService(modelId,  deploymentName, endpoint, apiKey, clientId, vendor:vendor, HttpClientProvider.GetHttpClient(httpClient, serviceProvider), serviceProvider.GetService<ILoggerFactory>()
                ));

            return builder;
        }
    }
}
