// Copyright (c) Microsoft. All rights reserved.

using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core.Models;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Models;
using Mysoft.GPTEngine.SemanticKernel.Core.Text;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Models;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core
{
    /// <summary>
    /// This class is responsible for making HTTP requests to the HuggingFace Inference API - Chat Completion Message API
    /// <see href="https://huggingface.co/docs/text-generation-inference/main/en/messages_api" />
    /// </summary>
    internal sealed class MysoftMessageApiClient
    {
        private readonly MysoftClient _clientCore;

        private static readonly string s_namespace = typeof(MysoftMessageApiClient).Namespace!;
        
        private static readonly string ALI_YUN_PROXY = "aliyunproxy";

        /// <summary>
        /// Instance of <see cref="Meter"/> for metrics.
        /// </summary>
        private static readonly Meter s_meter = new Meter(s_namespace);

        /// <summary>
        /// Instance of <see cref="Counter{T}"/> to keep track of the number of prompt tokens used.
        /// </summary>
        private static readonly Counter<int> s_promptTokensCounter =
            s_meter.CreateCounter<int>(
                name: $"{s_namespace}.tokens.prompt",
                unit: "{token}",
                description: "Number of prompt tokens used");

        /// <summary>
        /// Instance of <see cref="Counter{T}"/> to keep track of the number of completion tokens used.
        /// </summary>
        private static readonly Counter<int> s_completionTokensCounter =
            s_meter.CreateCounter<int>(
                name: $"{s_namespace}.tokens.completion",
                unit: "{token}",
                description: "Number of completion tokens used");

        /// <summary>
        /// Instance of <see cref="Counter{T}"/> to keep track of the total number of tokens used.
        /// </summary>
        private static readonly Counter<int> s_totalTokensCounter =
            s_meter.CreateCounter<int>(
                name: $"{s_namespace}.tokens.total",
                unit: "{token}",
                description: "Number of total tokens used");

        internal MysoftMessageApiClient(
            string modelId,
            string? deploymentName,
            HttpClient httpClient,
            Uri? endpoint = null,
            string? apiKey = null,
            string? apiSecret = null,
            string? vendor = null,
            ILogger? logger = null)
        {
            _clientCore = new MysoftClient(
                modelId,
                deploymentName,
                httpClient,
                endpoint,
                apiKey,
                apiSecret,
                vendor,
                logger);
        }
  
        internal async IAsyncEnumerable<StreamingChatMessageContent> StreamCompleteChatMessageAsync(
          ChatHistory chatHistory,
          PromptExecutionSettings? executionSettings,
          [EnumeratorCancellation] CancellationToken cancellationToken)
        {
            
            await _clientCore.UploadsAsync(chatHistory, cancellationToken);

            var muiltimodal = chatHistory.Any(x => x.Items.Count > 1);
            
            string modelId = executionSettings?.ModelId ?? _clientCore.ModelId;
            var endpoint = GetChatGenerationEndpoint();
            var request = new Object();
            if (ALI_YUN_PROXY.Equals(_clientCore.Vendor))
            {
                request = CreateChatRequestQwen(chatHistory, executionSettings);
            }
            else
            {
                var requestOrg = CreateChatRequest(chatHistory, executionSettings);
                requestOrg.Stream = true;
                request = requestOrg;
            }
            using var httpRequestMessage = _clientCore.CreatePost(request, endpoint, _clientCore.ApiKey);
            if (ALI_YUN_PROXY.Equals(_clientCore.Vendor))
            {
                httpRequestMessage.Headers.Add("X-DashScope-SSE", "enable");
            }

            using var response = await _clientCore.SendRequestAndGetResponseImmediatelyAfterHeadersReadAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);

            using var responseStream = await response.Content.ReadAsStreamAndTranslateExceptionAsync()
                .ConfigureAwait(false);

            await foreach (var streamingChatContent in this.ProcessChatResponseStreamAsync(responseStream, modelId, cancellationToken, muiltimodal).ConfigureAwait(false))
            {
                yield return streamingChatContent;
            }
            
        }
        
        public Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models.ChatCompletionRequest CreateChatRequestQwen(
            ChatHistory chatHistory,
            PromptExecutionSettings? promptExecutionSettings)
        {
            var executionSettings = QwenPromptExecutionSettings.FromExecutionSettings(promptExecutionSettings);
            executionSettings.ModelId ??= _clientCore.ModelId;

            QwenClient.ValidateMaxTokens(executionSettings.MaxTokens);
            var request = Qwen.Core.Models.ChatCompletionRequest.FromChatHistoryAndExecutionSettings(chatHistory, executionSettings, _clientCore.DeploymentName);
            request.Model = _clientCore.DeploymentName;
            return request;
        }

        internal async Task<IReadOnlyList<ChatMessageContent>> CompleteChatMessageAsync(
            ChatHistory chatHistory,
            PromptExecutionSettings? executionSettings,
            CancellationToken cancellationToken)
        {
            string modelId = executionSettings?.ModelId ?? _clientCore.ModelId;
            var endpoint = GetChatGenerationEndpoint();
            var request = new Object();
            if (ALI_YUN_PROXY.Equals(_clientCore.Vendor))
            {
                var executionSettingsNew = QwenPromptExecutionSettings.FromExecutionSettings(executionSettings);
                var requestOrigin = Qwen.Core.Models.ChatCompletionRequest.FromChatHistoryAndExecutionSettings(chatHistory, executionSettingsNew, _clientCore.DeploymentName);
                requestOrigin.Parameters.IncrementalOutput = false;
                request = requestOrigin;
            }
            else
            {
                 request = CreateChatRequest(chatHistory, executionSettings);
            }
            using var httpRequestMessage = _clientCore.CreatePost(request, endpoint, _clientCore.ApiKey);

            string body = await _clientCore.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);
            var chatContents = new List<ChatMessageContent>();
            if (ALI_YUN_PROXY.Equals(_clientCore.Vendor))
            {
                var response = MysoftClient.DeserializeResponse<Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models.ChatCompletionResponse>(body);
                chatContents = GetChatMessageContentsFromResponseByQwen(response, null);
                LogChatCompletionUsageQwen(executionSettings, response);
                
            }
            else
            {
                var response = MysoftClient.DeserializeResponse<ChatCompletionResponse>(body);
                chatContents = GetChatMessageContentsFromResponse(response, modelId);
                LogChatCompletionUsage(executionSettings, response);
            }
            return chatContents;
        }

        public static List<ChatMessageContent> GetChatMessageContentsFromResponseByQwen(Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models.ChatCompletionResponse response, string modelId)
        {
            var chatMessageContents = new List<ChatMessageContent>();
            foreach (var choice in response.Output.Choices!)
            {
                var metadata = new QwenChatCompletionMetadata
                {
                    Created = response.Output.Created,
                    FinishReason = choice.FinishReason,
                    UsageCompletionTokens = response.Usage?.OutputTokens,
                    UsagePromptTokens = response.Usage?.InputTokens,
                    UsageTotalTokens = response.Usage?.TotalTokens,
                };

                chatMessageContents.Add(new ChatMessageContent(
                    role: new AuthorRole(choice.Message?.Role ?? AuthorRole.Assistant.ToString()),
                    content: choice.Message?.Content,
                    modelId: modelId,
                    innerContent: response,
                    encoding: Encoding.UTF8,
                    metadata: metadata));
            }

            return chatMessageContents;
        }

        private void LogChatCompletionUsage(PromptExecutionSettings? executionSettings, ChatCompletionResponse chatCompletionResponse)
        {
            if (_clientCore.Logger.IsEnabled(LogLevel.Debug))
            {
                _clientCore.Logger.Log(
                LogLevel.Debug,
                "HuggingFace chat completion usage - ModelId: {ModelId}, Prompt tokens: {PromptTokens}, Completion tokens: {CompletionTokens}, Total tokens: {TotalTokens}",
                chatCompletionResponse.Model,
                chatCompletionResponse.Usage!.PromptTokens,
                chatCompletionResponse.Usage!.CompletionTokens,
                chatCompletionResponse.Usage!.TotalTokens);
            }

            s_promptTokensCounter.Add(chatCompletionResponse.Usage!.PromptTokens);
            s_completionTokensCounter.Add(chatCompletionResponse.Usage!.CompletionTokens);
            s_totalTokensCounter.Add(chatCompletionResponse.Usage!.TotalTokens);
        }
        
        private void LogChatCompletionUsageQwen(PromptExecutionSettings? executionSettings, Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models.ChatCompletionResponse chatCompletionResponse)
        {
            if (_clientCore.Logger.IsEnabled(LogLevel.Debug))
            {
                _clientCore.Logger.Log(
                    LogLevel.Debug,
                    "Qwen chat completion usage -  Prompt tokens: {PromptTokens}, Completion tokens: {CompletionTokens}, Total tokens: {TotalTokens}",
                    chatCompletionResponse.Usage!.InputTokens,
                    chatCompletionResponse.Usage!.OutputTokens,
                    chatCompletionResponse.Usage!.TotalTokens);
            }

            s_promptTokensCounter.Add(chatCompletionResponse.Usage!.InputTokens);
            s_completionTokensCounter.Add(chatCompletionResponse.Usage!.OutputTokens);
            s_totalTokensCounter.Add(chatCompletionResponse.Usage!.TotalTokens);
        }
        

        private static List<ChatMessageContent> GetChatMessageContentsFromResponse(ChatCompletionResponse response, string modelId)
        {
            var chatMessageContents = new List<ChatMessageContent>();
            foreach (var choice in response.Choices!)
            {
                var metadata = new MysoftChatCompletionMetadata
                {
                    Id = response.Id,
                    Model = response.Model,
                    @Object = response.Object,
                    SystemFingerPrint = response.SystemFingerprint,
                    Created = response.Created,
                    FinishReason = choice.FinishReason,
                    LogProbs = choice.LogProbs,
                    UsageCompletionTokens = response.Usage?.CompletionTokens,
                    UsagePromptTokens = response.Usage?.PromptTokens,
                    UsageTotalTokens = response.Usage?.TotalTokens,
                };

                chatMessageContents.Add(new ChatMessageContent(
                    role: new AuthorRole(choice.Message?.Role ?? AuthorRole.Assistant.ToString()),
                    content: choice.Message?.Content,
                    modelId: response.Model,
                    innerContent: response,
                    encoding: Encoding.UTF8,
                    metadata: metadata));
            }

            return chatMessageContents;
        }

        private static StreamingChatMessageContent GetStreamingChatMessageContentFromStreamResponse(ChatCompletionStreamResponse response, string modelId)
        {
            var choice = response.Choices.FirstOrDefault();
            if (choice != null)
            {
                var metadata = new MysoftChatCompletionMetadata
                {
                    Id = response.Id,
                    Model = response.Model,
                    @Object = response.Object,
                    SystemFingerPrint = response.SystemFingerprint,
                    Created = response.Created,
                    FinishReason = choice.FinishReason,
                    LogProbs = choice.LogProbs,
                };

                var streamChat = choice.Delta?.Role != null ? new StreamingChatMessageContent(
                    new AuthorRole(choice.Delta.Role),
                    choice.Delta?.Content,
                    response,
                    choice.Index,
                    modelId,
                    Encoding.UTF8,
                    metadata) : new StreamingChatMessageContent(
                    null,
                    choice.Delta?.Content,
                    response,
                    choice.Index,
                    modelId,
                    Encoding.UTF8,
                    metadata);

                return streamChat;
            }

            throw new KernelException("Unexpected response from model")
            {
                Data = { { "ResponseData", response } },
            };
        }

        private async IAsyncEnumerable<StreamingChatMessageContent> ProcessChatResponseStreamAsync(Stream stream, string modelId, [EnumeratorCancellation] CancellationToken cancellationToken, bool muiltimodal)
        {
            if (ALI_YUN_PROXY.Equals(_clientCore.Vendor))
            {
                if (muiltimodal)
                {
                    await foreach (var content in QwenMessageApiClient.ParseMultimodalChatResponseStreamAsync(stream, cancellationToken).ConfigureAwait(false))
                    {
                        yield return QwenMessageApiClient.GetStreamingChatMessageContentFromStreamResponse(content, modelId);
                    }
                }
                else
                {
                    await foreach (var content in QwenMessageApiClient.ParseChatResponseStreamAsync(stream, cancellationToken).ConfigureAwait(false))
                    {
                        yield return QwenMessageApiClient.GetStreamingChatMessageContentFromStreamResponse(content, modelId);
                    }
                }
            }
            else
            {
                await foreach (var content in ParseChatResponseStreamAsync(stream, cancellationToken).ConfigureAwait(false))
                {
                    yield return GetStreamingChatMessageContentFromStreamResponse(content, modelId);
                }
            }
        }

        private ChatCompletionRequest CreateChatRequest(
            ChatHistory chatHistory,
            PromptExecutionSettings? promptExecutionSettings)
        {
            var executionSettings = MysoftPromptExecutionSettings.FromExecutionSettings(promptExecutionSettings);
            executionSettings.ModelId ??= _clientCore.DeploymentName;

            MysoftClient.ValidateMaxTokens(executionSettings.MaxTokens);
            var request = ChatCompletionRequest.FromChatHistoryAndExecutionSettings(chatHistory, executionSettings);
            return request;
        }

        private IAsyncEnumerable<ChatCompletionStreamResponse> ParseChatResponseStreamAsync(Stream responseStream, CancellationToken cancellationToken)
            => SseJsonParser.ParseAsync<ChatCompletionStreamResponse>(responseStream, cancellationToken);

        private Uri GetChatGenerationEndpoint()
            => new Uri($"{_clientCore.Endpoint}");
    }
}
