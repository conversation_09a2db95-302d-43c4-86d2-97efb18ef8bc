// Copyright (c) Microsoft. All rights reserved.

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.SemanticKernel;

using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core.Models;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models;
using TextEmbeddingRequest = Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core.Models.TextEmbeddingRequest;
using TextEmbeddingResponse = Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core.Models.TextEmbeddingResponse;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core
{
    internal sealed class MysoftClient
    {
        private readonly HttpClient _httpClient;

        internal string ModelId { get; }
        internal string? DeploymentName { get; }
        internal string? ApiKey { get; }
        internal string? ClientId { get; }
        internal string? Vendor { get; }
        internal Uri Endpoint { get; }
        internal string Separator { get; }
        internal ILogger Logger { get; }
        
        private static readonly string ALI_YUN_PROXY = "aliyunproxy";
        
        private static readonly string ALI_YUN_APIKEY = "sk-260de2cc94264eff8dba9c1670c11be3";

        internal MysoftClient(
            string modelId,
            string? deploymentName,
            HttpClient httpClient,
            Uri? endpoint = null,
            string? apiKey = null,
            string? clientId = null,
            string? vendor = null,
            ILogger? logger = null)
        {
            Verify.NotNullOrWhiteSpace(modelId);
            Verify.NotNull(httpClient);

            endpoint ??= new Uri("https://myy-openai.myscrm.cn");
            Separator = endpoint.AbsolutePath.EndsWith("/", StringComparison.InvariantCulture) ? string.Empty : "/";
            Endpoint = endpoint;
            ModelId = modelId;
            DeploymentName = deploymentName;
            ApiKey = apiKey;
            ClientId = clientId;
            Vendor = vendor;
            _httpClient = httpClient;
            Logger = logger ?? NullLogger.Instance;
        }

        #region ClientCore

        internal static void ValidateMaxTokens(int? maxTokens)
        {
            if (maxTokens < 1)
            {
                throw new ArgumentException($"MaxTokens {maxTokens} is not valid, the value must be greater than zero");
            }
        }

        internal static void ValidateMaxNewTokens(int? maxNewTokens)
        {
            if (maxNewTokens < 0)
            {
                throw new ArgumentException(
                    $"MaxNewTokens {maxNewTokens} is not valid, the value must be greater than or equal to zero");
            }
        }

        internal async Task<string> SendRequestAndGetStringBodyAsync(
            HttpRequestMessage httpRequestMessage,
            CancellationToken cancellationToken)
        {
            using var response = await _httpClient.SendWithSuccessCheckAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);

            var body = await response.Content.ReadAsStringWithExceptionMappingAsync()
                .ConfigureAwait(false);

            return body;
        }

        internal async Task<HttpResponseMessage> SendRequestAndGetResponseImmediatelyAfterHeadersReadAsync(
            HttpRequestMessage httpRequestMessage,
            CancellationToken cancellationToken)
        {
            var response = await _httpClient.SendWithSuccessCheckAsync(httpRequestMessage,
                    HttpCompletionOption.ResponseHeadersRead, cancellationToken)
                .ConfigureAwait(false);
            return response;
        }

        internal static T DeserializeResponse<T>(string body)
        {
            try
            {
                var deserializedResponse = JsonSerializer.Deserialize<T>(body);
                if (deserializedResponse is null)
                {
                    throw new JsonException("Response is null");
                }

                return deserializedResponse;
            }
            catch (JsonException exc)
            {
                throw new KernelException("Unexpected response from model", exc)
                {
                    Data = { { "ResponseData", body } },
                };
            }
        }

        internal void SetRequestHeaders(HttpRequestMessage request, string? apiKey = null)
        {
            request.Headers.Add("User-Agent", HttpHeaderConstant.Values.UserAgent);
            request.Headers.Add(HttpHeaderConstant.Names.SemanticKernelVersion,
                HttpHeaderConstant.Values.GetAssemblyVersion(GetType()));
            if (!string.IsNullOrEmpty(apiKey))
            {
                request.Headers.Add("Authorization", $"Bearer {apiKey}");
            }
            else
            {
                if (!string.IsNullOrEmpty(ApiKey))
                {
                    request.Headers.Add("Authorization", $"Bearer {ApiKey}");
                }
            }
        }

        internal HttpRequestMessage CreatePost(object requestData, Uri endpoint, string? apiKey)
        {
            var httpRequestMessage = HttpRequest.CreatePostRequest(endpoint, requestData);
            this.SetRequestHeaders(httpRequestMessage);

            return httpRequestMessage;
        }

        #endregion

          #region oss
        public async Task UploadsAsync(ChatHistory chatMessages, CancellationToken cancellationToken)
        {
            if (chatMessages.Any(x => x.Items.Any(item => item is ImageContent)) == false)
            {
                return;
            }
            var policy = await GetPolicy(cancellationToken);

            foreach (var chatMessage in chatMessages)
            {
                foreach (var item in chatMessage.Items)
                {
                    if (item is ImageContent image)
                    {
                        image.Uri = await UploadAsync(image, policy, cancellationToken);
                    }
                }
            }
        }

        private async Task<Uri> UploadAsync(ImageContent image, GetPolicyResponse policy, CancellationToken cancellationToken)
        {
            var fileName = image.Metadata.GetValueOrDefault("FileName", string.Empty).ToString();
            var fileData = (byte[])image.Metadata.GetValueOrDefault("FileContent", string.Empty);

            var path = $"{policy.Data.UploadDir}/{Guid.NewGuid()}";
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, policy.Data.UploadHost);
            SetRequestHeaders(httpRequestMessage, ALI_YUN_APIKEY);
            var multipartForm = new MultipartFormDataContent($"-----{Guid.NewGuid()}")
            {
                { new ByteArrayContent(Encoding.UTF8.GetBytes(policy.Data.OssAccessKeyId)), "\"OSSAccessKeyId\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes(path)), "\"Key\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes(policy.Data.Policy)), "\"Policy\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes(policy.Data.Signature)), "\"Signature\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes("private")), "\"x-oss-object-acl\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes("true")), "\"x-oss-forbid-overwrite\"" },
                { new ByteArrayContent(Encoding.UTF8.GetBytes("200")), "\"success_action_status\"" },
                { new ByteArrayContent(fileData), "\"file\"" }
            };

            var boundary = multipartForm.Headers.ContentType.Parameters.First(o => o.Name == "boundary");
            boundary.Value = boundary.Value.Replace("\"", String.Empty);

            httpRequestMessage.Content = multipartForm;
            await this.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken).ConfigureAwait(false);
            return new Uri($"oss://{path}");
        }

        private async Task<GetPolicyResponse> GetPolicy(CancellationToken cancellationToken)
        {
            //var endpoint = $"https://dashscope.aliyuncs.com{this.Separator}api/v1/uploads?action=getPolicy&model={this.ModelId}";
            var endpoint = "https://dashscope.aliyuncs.com/api/v1/uploads?action=getPolicy&model=qwen-vl-plus";
            using var httpRequestMessage = this.CreateGet(null, endpoint, ALI_YUN_APIKEY);

            string body = await this.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken).ConfigureAwait(false);
            return DeserializeResponse<GetPolicyResponse>(body);
        }
        
        internal HttpRequestMessage CreateGet(object? requestData, string endpoint, string? apiKey)
        {
            var httpRequestMessage = HttpRequest.CreateGetRequest(endpoint, requestData);
            this.SetRequestHeaders(httpRequestMessage, apiKey);

            return httpRequestMessage;
        }
        #endregion

        #region Embeddings

        public async Task<IList<ReadOnlyMemory<float>>> GenerateEmbeddingsAsync(
            IList<string> data,
            Kernel? kernel,
            CancellationToken cancellationToken)
        {
            var endpoint = this.GetEmbeddingGenerationEndpoint();

            if (data.Count > 1)
            {
                throw new NotSupportedException(
                    "Currently this interface does not support multiple embeddings results per data item, use only one data item");
            }

            Object request = new Object();
            if (ALI_YUN_PROXY.Equals(Vendor))
            {
                var input = new Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core.Models.MyAliTextEmbeddingRequest.AliInput
                {
                    texts = data,
                };
                request = new MyAliTextEmbeddingRequest
                {
                    input = input,
                    model = DeploymentName
                };
            }
            else
            { 
                request = new TextEmbeddingRequest
                {
                    input = data,
                    model = DeploymentName
                };
            }

        
            Logger.LogInformation("#### Mysoft AI Request Body: {body}", JsonSerializer.Serialize(data));
            using var httpRequestMessage = this.CreatePost(request, endpoint, this.ApiKey);

            string body = await this.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);
            Logger.LogInformation("#### Mysoft AI Response Body: {body}", body);
            
            
            float[] floatArray = new float[1536];
            if (ALI_YUN_PROXY.Equals(Vendor))
            {
                var response = DeserializeResponse<Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models.TextEmbeddingResponse>(body);
                return response.Output.Embeddings.Select(embedding => embedding.Embedding).ToList();
            }
            else
            {
                var textEmbeddingResponse = DeserializeResponse<TextEmbeddingResponse>(body);
                List<float> embeddings = new List<float>();
                
                if (textEmbeddingResponse  != null  && textEmbeddingResponse.data != null) {
                    //因为是分片生成向量，data.count = 1 这里只需要读取第一个即可
                    embeddings = textEmbeddingResponse.data.FirstOrDefault().embedding;
                }
                for (int i = 0;i < 1536; i++) {
                    if (i >= embeddings.Count){
                        floatArray[i] = 0.00f;
                    }
                    else {
                        floatArray[i] = (float)embeddings[i];
                    }
                }
                // 使用float数组创建ReadOnlyMemory<float>实例
            }

            ReadOnlyMemory<float> readOnlyMemory = new ReadOnlyMemory<float>(floatArray);
            List <ReadOnlyMemory<float>> result = new List<ReadOnlyMemory<float>>();
            result.Add(readOnlyMemory);
            // Currently only one embedding per data is supported
            return result;
        }
        
        private Uri GetEmbeddingGenerationEndpoint() =>
            new Uri($"{this.Endpoint}{this.Separator}v1/proxy/common/embeddings");

        #endregion
    }
}