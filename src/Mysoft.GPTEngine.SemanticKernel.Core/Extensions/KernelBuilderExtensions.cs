using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.AzureOpenAI;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.SemanticKernel.Embeddings;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen;
using Mysoft.GPTEngine.SemanticKernel.Core.Text;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Extensions
{
    /// <summary>
    /// Provides extension methods for the <see cref="IKernelBuilder"/> class to configure Hugging Face connectors.
    /// </summary>
#pragma warning disable SKEXP0010
    public static class KernelBuilderExtensions
    {
        public static IKernelBuilder AddAzureOpenAIEmbedding(
            this IKernelBuilder builder,
            string deploymentName,
            string modelId,
            string endpoint = null,
            string apiKey = null,
            string? serviceId = null,
            HttpClient? httpClient = null)
        {
            Verify.NotNull(builder);
            Verify.NotNull(modelId);

            builder.Services.AddKeyedSingleton<ITextEmbeddingGenerationService>(serviceId, (serviceProvider, _) =>
                new AzureOpenAITextEmbeddingGenerationService(
                    deploymentName,
                    endpoint,
                    apiKey,
                    modelId,
                    HttpClientProvider.GetHttpClient(httpClient, serviceProvider),
                    serviceProvider.GetService<ILoggerFactory>()
                ));

            return builder;
        }

        public static IKernelBuilder AddOpenAIEmbedding(
           this IKernelBuilder builder,
           string modelId,
           string apiKey,
           string? orgId = null,
           string? serviceId = null,
           HttpClient? httpClient = null)
        {
            Verify.NotNull(builder);
            Verify.NotNull(modelId);

            builder.Services.AddKeyedSingleton<ITextEmbeddingGenerationService>(serviceId, (serviceProvider, _) =>
                new OpenAITextEmbeddingGenerationService(
                    modelId,
                    apiKey,
                    orgId,
                    HttpClientProvider.GetHttpClient(httpClient, serviceProvider),
                    serviceProvider.GetService<ILoggerFactory>()
                ));

            return builder;
        }

        public static PromptExecutionSettings? DeserializePromptExecutionSettings(string json)
        {
            return JsonSerializer.Deserialize<PromptExecutionSettings>(json, JsonOptionsCache.ReadPermissive);
        }
    }
}
