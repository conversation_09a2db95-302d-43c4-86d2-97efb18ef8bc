using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Dtos
{
    public class ParamDto
    {
        [JsonProperty(PropertyName = "code")]
        public String Code { get; set; }
        [JsonProperty(PropertyName = "name")]
        public String Name { get; set; }
        [JsonProperty(PropertyName = "type")]
        public String Type { get; set; }
        [JsonProperty(PropertyName = "required")]
        public Boolean Required { get; set; }
        [JsonProperty(PropertyName = "description")]
        public String Description { get; set; }
        [JsonProperty(PropertyName = "paramType")]
        public String ParamType { get; set; }
        [JsonProperty(PropertyName = "defaultValue")]
        public String DefaultValue { get; set; }
        [JsonProperty(PropertyName = "literalCode")]
        public String LiteralCode { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "literalValue")]
        public String LiteralValue { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "value")]
        public ParamValueDto? Value { get; set; }
        [JsonProperty(PropertyName = "schema")]
        public List<ParamDto> Schema { get; set; }
    }
    public class ParamValueDto
    {
        public string? Type { get; set; }
        public string? Content { get; set; }
        [System.Text.Json.Serialization.JsonIgnore]
        public object? SyncMessage { get; set; }
    }
}