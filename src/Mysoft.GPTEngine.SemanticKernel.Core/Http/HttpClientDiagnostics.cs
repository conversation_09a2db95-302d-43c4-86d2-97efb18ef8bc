using Org.BouncyCastle.Tls;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Text;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Http
{
    /// <summary>
    /// 当前未使用默认HttpMessageHandler，内置HTTP Client的诊断事件不会被触发。
    /// 此处参照内置，实现诊断事件发送。
    /// </summary>
    /// <seealso href="https://github.com/dotnet/runtime/blob/main/src/libraries/System.Net.Http/src/System/Net/Http/DiagnosticsHandler.cs"/>
    internal sealed class HttpClientDiagnostics
    {

        public const string DiagnosticListenerName = "HttpHandlerDiagnosticListener";
        public const string RequestNamespace = "System.Net.Http";
        public const string RequestWriteNameDeprecated = RequestNamespace + ".Request";
        public const string ResponseWriteNameDeprecated = RequestNamespace + ".Response";
        public const string ExceptionEventName = RequestNamespace + ".Exception";
        public const string RequestActivityName = RequestNamespace + ".HttpRequestOut";
        public const string RequestActivityStartName = RequestActivityName + ".Start";
        public const string RequestActivityStopName = RequestActivityName + ".Stop";

        public const string ConnectionsNamespace = "Experimental.System.Net.Http.Connections";
        public const string ConnectionSetupActivityName = ConnectionsNamespace + ".ConnectionSetup";
        public const string WaitForConnectionActivityName = ConnectionsNamespace + ".WaitForConnection";

        private static readonly DiagnosticListener s_diagnosticListener = new DiagnosticListener(DiagnosticListenerName);


        public static void WriteRequestStartEvent(HttpRequestMessage request)
        {
            if (s_diagnosticListener.IsEnabled(RequestWriteNameDeprecated))
            {
                s_diagnosticListener.Write(RequestWriteNameDeprecated, new { Request = request });
            }

            if (s_diagnosticListener.IsEnabled(RequestActivityStartName))
            {
                s_diagnosticListener.Write(RequestActivityStartName, new { Request = request });
            }
        }

        public static void WriteRequestStopEvent(HttpResponseMessage response)
        {
            if (s_diagnosticListener.IsEnabled(ResponseWriteNameDeprecated))
            {
                s_diagnosticListener.Write(ResponseWriteNameDeprecated, new { Response = response });
            }
            if (s_diagnosticListener.IsEnabled(RequestActivityStopName))
            {
                s_diagnosticListener.Write(RequestActivityStopName, new { Response = response });
            }
        }

        public static void WriteHttpExceptionEvent(HttpRequestMessage request, Exception exception)
        {
            if (s_diagnosticListener.IsEnabled(ExceptionEventName))
            {
                s_diagnosticListener.Write(ExceptionEventName, new { Request = request, Exception = exception });
            }
        }

    }
}
