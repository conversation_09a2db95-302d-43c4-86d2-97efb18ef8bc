// Copyright (c) Microsoft. All rights reserved.

using Microsoft.SemanticKernel;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Http
{
    [ExcludeFromCodeCoverage]
    public static class HttpClientExtensions
    {
        /// <summary>
        /// Sends an HTTP request using the provided <see cref="HttpClient"/> instance and checks for a successful response.
        /// If the response is not successful, it logs an error and throws an <see cref="HttpOperationException"/>.
        /// </summary>
        /// <param name="client">The <see cref="HttpClient"/> instance to use for sending the request.</param>
        /// <param name="request">The <see cref="HttpRequestMessage"/> to send.</param>
        /// <param name="completionOption">Indicates if HttpClient operations should be considered completed either as soon as a response is available,
        /// or after reading the entire response message including the content.</param>
        /// <param name="cancellationToken">A <see cref="CancellationToken"/> for canceling the request.</param>
        /// <returns>The <see cref="HttpResponseMessage"/> representing the response.</returns>
        [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "By design. See comment below.")]
        [SuppressMessage("Reliability", "CA2016:Forward the 'CancellationToken' parameter to methods", Justification = "The `ReadAsStringAsync` method in the NetStandard 2.0 version does not have an overload that accepts the cancellation token.")]
        public static async Task<HttpResponseMessage> SendWithSuccessCheckAsync(this HttpClient client, HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
        {
            HttpResponseMessage? response = null;
            try
            {
                HttpClientDiagnostics.WriteRequestStartEvent(request);
                response = await client.SendAsync(request, completionOption, cancellationToken).ConfigureAwait(false);
            }
            catch (HttpRequestException e)
            {
                HttpClientDiagnostics.WriteHttpExceptionEvent(request, e);
                throw new HttpOperationException(HttpStatusCode.BadRequest, null, e.Message, e);
            }

            if (!response.IsSuccessStatusCode)
            {
                string? responseContent = null;
                try
                {
                    // On .NET Framework, EnsureSuccessStatusCode disposes of the response content;
                    // that was changed years ago in .NET Core, but for .NET Framework it means in order
                    // to read the response content in the case of failure, that has to be
                    // done before calling EnsureSuccessStatusCode.
                    responseContent = await response!.Content.ReadAsStringAsync().ConfigureAwait(false);
                    response.EnsureSuccessStatusCode(); // will always throw
                }
                catch (Exception e)
                {
                    HttpClientDiagnostics.WriteHttpExceptionEvent(request, e);
                    throw new HttpOperationException(response.StatusCode, responseContent, e.Message, e);
                }
            }
            HttpClientDiagnostics.WriteRequestStopEvent(response);
            return response;
        }

        /// <summary>
        /// Sends an HTTP request using the provided <see cref="HttpClient"/> instance and checks for a successful response.
        /// If the response is not successful, it logs an error and throws an <see cref="HttpOperationException"/>.
        /// </summary>
        /// <param name="client">The <see cref="HttpClient"/> instance to use for sending the request.</param>
        /// <param name="request">The <see cref="HttpRequestMessage"/> to send.</param>
        /// <param name="cancellationToken">A <see cref="CancellationToken"/> for canceling the request.</param>
        /// <returns>The <see cref="HttpResponseMessage"/> representing the response.</returns>
        public static async Task<HttpResponseMessage> SendWithSuccessCheckAsync(this HttpClient client, HttpRequestMessage request, CancellationToken cancellationToken)
        {
            return await client.SendWithSuccessCheckAsync(request, HttpCompletionOption.ResponseContentRead, cancellationToken).ConfigureAwait(false);
        }
    }
}
