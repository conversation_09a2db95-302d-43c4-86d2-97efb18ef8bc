# McpToolKernelWrapper 数组参数处理修复说明

## 问题描述

在使用 McpToolKernelWrapper 处理复杂参数时，当参数类型为 `array` 时会出现转换错误。具体表现为：

1. **错误日志示例**：
```
[McpToolKernelWrapper] 开始转换参数值: [{"moduleName": "员工管理", "moduleCode": "100001", ...}] (String) -> array
[McpToolKernelWrapper] 参数值转换成功: [{"moduleName": "员工管理", ...}] -> {"moduleName": "员工管理", ...} (Object[])
```

2. **问题根因**：
   - `ConvertArgumentValue` 方法的 switch 语句中没有处理 `"array"` 类型
   - 数组参数被当作字符串处理，导致数组中的对象被错误转换
   - 最终传递给 MCP 工具的参数格式不正确，导致 Java 端反序列化失败

## 修复方案

### 1. 添加数组类型支持

在 `ConvertArgumentValue` 方法中添加对 `"array"` 类型的专门处理：

```csharp
convertedValue = targetType switch
{
    "array" => ConvertToArray(value),  // 新增数组类型处理
    "number" => Convert.ToDouble(value),
    // ... 其他类型
    _ => value
};
```

### 2. 实现 ConvertToArray 方法

新增专门的数组转换方法，支持多种输入格式：

```csharp
private object ConvertToArray(object value)
{
    // 1. 如果已经是数组类型，直接返回
    if (value is Array || value is IEnumerable<object>)
        return value;

    // 2. 处理 JSON 字符串数组
    if (value is string strValue)
    {
        // 解析 JSON 字符串为对象数组
        using var document = JsonDocument.Parse(strValue);
        if (document.RootElement.ValueKind == JsonValueKind.Array)
        {
            var jsonArray = new List<object>();
            foreach (var element in document.RootElement.EnumerateArray())
            {
                if (element.ValueKind == JsonValueKind.Object)
                {
                    // 将 JsonElement 转换为 Dictionary 以便 MCP 工具处理
                    var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(element.GetRawText());
                    jsonArray.Add(dict);
                }
                else
                {
                    jsonArray.Add(GetJsonElementValue(element));
                }
            }
            return jsonArray.ToArray();
        }
    }

    // 3. 处理 JsonElement 类型
    if (value is JsonElement jsonElement)
    {
        // 类似处理逻辑
    }

    // 4. 其他情况包装为单元素数组
    return new[] { value };
}
```

### 3. 添加辅助方法

新增 `GetJsonElementValue` 方法来正确提取 JsonElement 的值：

```csharp
private object GetJsonElementValue(JsonElement element)
{
    return element.ValueKind switch
    {
        JsonValueKind.String => element.GetString(),
        JsonValueKind.Number => element.TryGetInt32(out var intVal) ? intVal : element.GetDouble(),
        JsonValueKind.True => true,
        JsonValueKind.False => false,
        JsonValueKind.Null => null,
        JsonValueKind.Object => JsonSerializer.Deserialize<Dictionary<string, object>>(element.GetRawText()),
        JsonValueKind.Array => element.EnumerateArray().Select(GetJsonElementValue).ToArray(),
        _ => element.GetRawText()
    };
}
```

## 修复效果

### 修复前
```
输入: {"modules":"[{\"moduleName\": \"员工管理\", ...}]"}
处理: 数组被当作字符串，每个对象变成字符串
输出: ["{\\"moduleName\\": \\"员工管理\\", ...}"]  // 错误格式
结果: Java 端反序列化失败
```

### 修复后
```
输入: {"modules":"[{\"moduleName\": \"员工管理\", ...}]"}
处理: 正确解析 JSON 数组，保持对象结构
输出: [{"moduleName": "员工管理", ...}]  // 正确格式
结果: Java 端成功反序列化为 List<AppModuleDto>
```

## 测试验证

修复后的代码能够正确处理以下场景：

1. **JSON 字符串数组**：`"[{\"key\":\"value\"}, {...}]"`
2. **已存在的数组对象**：`object[]` 或 `IEnumerable<object>`
3. **JsonElement 数组**：来自 JSON 解析的数组元素
4. **单个值**：自动包装为单元素数组
5. **空值处理**：返回空数组或 null

## 相关文件

- `src/Mysoft.GPTEngine.Domain/McpCustomService.cs`
  - 修改了 `ConvertArgumentValue` 方法
  - 新增了 `ConvertToArray` 方法
  - 新增了 `GetJsonElementValue` 方法
  - 添加了 `using System.Collections;` 引用

## 注意事项

1. **向后兼容**：修复不影响现有的基本类型转换逻辑
2. **性能考虑**：只在参数类型为 `array` 时才进行复杂的 JSON 解析
3. **错误处理**：包含完整的异常处理，确保在解析失败时返回原始值
4. **日志记录**：添加了详细的调试日志，便于问题排查

## 总结

此修复解决了 McpToolKernelWrapper 在处理复杂数组参数时的转换问题，确保了：

1. 数组参数能够正确传递给 MCP 工具
2. 对象结构在转换过程中保持完整
3. Java 端能够成功反序列化参数
4. 不影响其他类型参数的正常处理

修复后，类似 `create_app_module_batch` 这样需要复杂数组参数的 MCP 工具将能够正常工作。
